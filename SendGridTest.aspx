<%@ Page Language="VB" AutoEventWireup="false" CodeFile="SendGridTest.aspx.vb" Inherits="SendGridTest" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>SendGrid Email Test</title>
</head>
<body>
    <form id="form1" runat="server">
        <div style="padding: 20px;">
            <h2>SendGrid Email Test</h2>
            
            <table>
                <tr>
                    <td>To Email:</td>
                    <td><asp:TextBox ID="txtToEmail" runat="server" Width="300px" Text="<EMAIL>"></asp:TextBox></td>
                </tr>
                <tr>
                    <td>Subject:</td>
                    <td><asp:TextBox ID="txtSubject" runat="server" Width="300px" Text="SendGrid Test Email"></asp:TextBox></td>
                </tr>
                <tr>
                    <td>Message:</td>
                    <td><asp:TextBox ID="txtMessage" runat="server" TextMode="MultiLine" Rows="5" Width="300px" Text="This is a test email sent via SendGrid from SBOnet."></asp:TextBox></td>
                </tr>
                <tr>
                    <td></td>
                    <td>
                        <asp:Button ID="btnSendEmail" runat="server" Text="Send Test Email" OnClick="btnSendEmail_Click" />
                    </td>
                </tr>
            </table>
            
            <br />
            <asp:Label ID="lblResult" runat="server" Font-Bold="true"></asp:Label>
            
            <br /><br />
            <div style="background-color: #f0f0f0; padding: 10px; border: 1px solid #ccc;">
                <strong>Instructions:</strong><br />
                1. Make sure you have set the SendGrid API key in web.config<br />
                2. Enter a valid email address<br />
                3. Click "Send Test Email"<br />
                4. Check the result message and your email inbox
            </div>
        </div>
    </form>
</body>
</html>
