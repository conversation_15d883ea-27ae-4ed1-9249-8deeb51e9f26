'Change log
'2016-06-15 DAF - S19 // B-02411 - Add Google Analytics Tracking

Option Explicit On

Imports System.Data
Imports System.Data.SqlClient
Imports System.Net
Imports System.IO
Imports System.Web.Services
Imports SBOTypeLibrary
Imports SBOWebSiteServices
Imports InventoryWebSiteServices

''' <summary>
''' BDean: Sprint 29 // B-02709 // TK-02820 - I replaced the "Android Market" icon/link with one for the "Google Play Store".
''' </summary>

Partial Class Login
    Inherits System.Web.UI.Page
    Friend sCaption As String
    Friend sLoadURL As String

    Sub Page_Load(ByVal sender As Object, ByVal e As EventArgs) Handles Me.Load

        'BDean: 5/27/2016 - Added the event handler back to this Sub ( it was removed for an unknown reason back in 2013 )

        'Response.Redirect("outage.aspx")
        'Write host name to tell which server is being accessed.
        Response.Write("<!-- " & Dns.GetHostName & "//-->")

        'BDean: Sprint 19 // B-02403 // TK-02033 - Clear the session if the user clicked the "Logout" link
        If Len(Request.QueryString("logout")) > 0 AndAlso Session("IWSS") IsNot Nothing Then

            Dim InvWSS As InventoryWebSiteServices.InventoryServices = Session("IWSS")
            InvWSS.ExecuteCommand("INSERT INTO tmUserLog(URL,AccountID,UserIP,LogTime) VALUES('[LOGGED OUT]','" & InvWSS.Platform.Application.AccountIdentifier & "','" & If(InStr(Request.ServerVariables("SERVER_NAME").ToLower, "localhost"), Server.MachineName, Request.UserHostAddress()) & "',GETDATE())")

            Dim hvID As String = Request.Cookies("HV")("ID")

            Response.Cookies.Clear()

            Response.Cookies("HV").Expires = Now.AddDays(-1)
            Response.Cookies("HV").Value = ""
            If not InStr(Request.ServerVariables("SERVER_NAME").ToLower, "localhost") > 0 
                Response.Cookies("HV").Secure = True
                Response.Cookies("HV").HttpOnly = True
            End If

            Response.Cookies("UHVCS" + hvID).Expires = Now.AddDays(-1)
            Response.Cookies("UHVCS" + hvID).Value = ""
            If not InStr(Request.ServerVariables("SERVER_NAME").ToLower, "localhost") > 0 
                Response.Cookies("UHVCS").Secure = True
                Response.Cookies("UHVCS").HttpOnly = True
            End If

            Response.Cookies("ASP.NET_SessionId").Expires = Now.AddDays(-1)
            Response.Cookies("ASP.NET_SessionId").Value = ""
            If not InStr(Request.ServerVariables("SERVER_NAME").ToLower, "localhost") > 0 
                Response.Cookies("ASP.NET_SessionId").Secure = True
                Response.Cookies("ASP.NET_SessionId").HttpOnly = True
            End If

            Session.Clear()
            Session.Abandon()

        End If

        If Len(Request.QueryString("timeout")) > 0 Then
            sLoadURL = "parent.location.href='login.aspx'"
            sCaption = "Session has expired. Please Login."
        End If

        If Len(Request.QueryString("failedlogin")) > 0 Then
            sCaption = "Bad username or password. Please try again."
        End If

    End Sub

    Sub Login(ByVal Sender As Object, ByVal E As EventArgs)
        Dim InvWSS As New InventoryWebSiteServices.InventoryServices
        Dim Platform As ISBOPlatformV100
        Session.Clear()
        Try
            If IsPostBack Then
                If InStr(UserID.Text, " ") Or InStr(Password.Text, " ") Then
                    Session.Clear()
                    Exit Sub
                End If

                Session("DTSServerName") = "WEB1"

                Dim sServerName As String









                Platform = InvWSS.Login(UserID.Text, Password.Text)

                If Len(Platform.Application.UserID) > 0 Then

                    InvWSS.ExecuteCommand("INSERT INTO tmUserLog(URL,AccountID,UserIP,LogTime) VALUES('[LOGGED IN]','" & Platform.Application.AccountIdentifier & "','" & If(InStr(Request.ServerVariables("SERVER_NAME").ToLower, "localhost"), Server.MachineName, Request.UserHostAddress()) & "',GETDATE())")


                    Session("AccountID") = Platform.Application.AccountIdentifier
                    Session("UserID") = Platform.Application.UserID
                    Session("PWD") = Password.Text
                    Session("IWSS") = InvWSS
                    Session("organizationName") = Platform.Application.Organization

                    Session("ConnectString") = InvWSS.ConnectString()

                    Session("VendorDirectory") = Platform.PathMapLookup("VendorData")

                    Session("DTSServerName") = Platform.Application.HostName()

                    VerifyTOS(InvWSS)


                    sLoadURL = Platform.PersistentValue("InventoryConfig", "StartPageURL", "portalframes.aspx")

                    sLoadURL = Platform.PersistentValue("InventoryConfig", "StartPageURL", "mp_portal.aspx")
                    If InStr(Request.ServerVariables("SERVER_NAME").ToLower, "localhost") Or InStr(Request.ServerVariables("SERVER_NAME").ToLower, "*************") Then
                        sLoadURL = Right(sLoadURL, sLoadURL.Length - InStr(sLoadURL, "/"))
                    End If

                    sLoadURL = "parent.location.href='" & sLoadURL & "'"

                    sServerName = Request.ServerVariables("SERVER_NAME")

                    If InStr(sLoadURL.ToLower, "altwebinventoryproduction") Then
                        Response.Cookies("DumacSettings")("uid") = Session("UserID")
                        Response.Cookies("DumacSettings")("pwd") = Session("PWD")
                        If not InStr(Request.ServerVariables("SERVER_NAME").ToLower, "localhost") > 0 
                            Response.Cookies("DumacSettings").Secure = True
                            Response.Cookies("DumacSettings").HttpOnly = True
                        End If
                        'Response.Redirect("https://www.dumacwebservices.com/altwebinventoryproduction/altlogin.aspx")
                        Response.Redirect("https://" & sServerName & "/altwebinventoryproduction/altlogin.aspx")
                        Exit Sub
                    ElseIf InStr(sLoadURL.ToLower, "webinventoryproduction203") Then
                        Response.Cookies("DumacSettings")("uid") = Session("UserID")
                        Response.Cookies("DumacSettings")("pwd") = Session("PWD")
                        If not InStr(Request.ServerVariables("SERVER_NAME").ToLower, "localhost") > 0 
                            Response.Cookies("DumacSettings").Secure = True
                            Response.Cookies("DumacSettings").HttpOnly = True
                        End If
                        'Response.Redirect("https://www.dumacwebservices.com/webinventoryproduction203/altlogin.aspx")
                        Response.Redirect("https://" & sServerName & "/webinventoryproduction203/altlogin.aspx")
                        Exit Sub
                    ElseIf InStr(sLoadURL.ToLower, "webinventoryproduction204") Then
                        Response.Cookies("DumacSettings")("uid") = Session("UserID")
                        Response.Cookies("DumacSettings")("pwd") = Session("PWD")
                        If not InStr(Request.ServerVariables("SERVER_NAME").ToLower, "localhost") > 0 
                            Response.Cookies("DumacSettings").Secure = True
                            Response.Cookies("DumacSettings").HttpOnly = True
                        End If
                        'Response.Redirect("https://www.dumacwebservices.com/webinventoryproduction204/altlogin.aspx")
                        Response.Redirect("https://" & sServerName & "/webinventoryproduction204/altlogin.aspx")
                        Exit Sub
                    ElseIf InStr(sLoadURL.ToLower, "webinventoryproduction400") Then
                        Response.Cookies("DumacSettings")("uid") = Session("UserID")
                        Response.Cookies("DumacSettings")("pwd") = Session("PWD")
                        If not InStr(Request.ServerVariables("SERVER_NAME").ToLower, "localhost") > 0 
                            Response.Cookies("DumacSettings").Secure = True
                            Response.Cookies("DumacSettings").HttpOnly = True
                        End If
                        'Response.Redirect("https://www.dumacwebservices.com/webinventoryproduction400/altlogin.aspx")
                        Response.Redirect("https://" & sServerName & "/webinventoryproduction400/altlogin.aspx")
                        Exit Sub
                    ElseIf InStr(sLoadURL.ToLower, "webinventoryproductiondev") Then
                        Response.Cookies("DumacSettings")("uid") = Session("UserID")
                        Response.Cookies("DumacSettings")("pwd") = Session("PWD")
                        If not InStr(Request.ServerVariables("SERVER_NAME").ToLower, "localhost") > 0 
                            Response.Cookies("DumacSettings").Secure = True
                            Response.Cookies("DumacSettings").HttpOnly = True
                        End If
                        'Response.Redirect("https://www.dumacwebservices.com/webinventoryproductiondev/altlogin.aspx")
                        Response.Redirect("https://" & sServerName & "/webinventoryproductiondev/altlogin.aspx")
                        Exit Sub
                        'Else
                        '    Response.Write(sLoadURL)
                    End If







                End If







            Else
                'do nothing
            End If
        Catch ePage_Load As Exception
            sLoadURL = "parent.location.href='login.aspx?failedlogin=1'"



            If Not Platform Is Nothing Then
                Platform.LogError(ePage_Load, "login.aspx", "Login")
            End If
        End Try
    End Sub

    Sub MobileLogin(ByVal Sender As Object, ByVal E As EventArgs)
        Dim InvWSS As New InventoryWebSiteServices.InventoryServices
        Dim Platform As ISBOPlatformV100
        Session.Clear()
        Try
            If IsPostBack Then
                If InStr(UserID.Text, " ") Or InStr(Password.Text, " ") Then
                    Session.Clear()
                    Exit Sub
                End If

                Session("DTSServerName") = "WEB1"

                Platform = InvWSS.Login(UserID.Text, Password.Text)

                Dim sServerName As String

                If Len(Platform.Application.UserID) > 0 Then
                    Session("Mobile") = 1
                    Session("AccountID") = Platform.Application.AccountIdentifier
                    Session("UserID") = Platform.Application.UserID
                    Session("PWD") = Password.Text
                    Session("IWSS") = InvWSS
                    Session("organizationName") = Platform.Application.Organization

                    'SBO upgrade this line...
                    Session("ConnectString") = InvWSS.ConnectString()
                    'replaces this line...
                    'Session("ConnectString") = InvWSS.ConnectStringInventory(Session("UserID"), Session("PWD"))
                    ''''''''''''''''''''''''
                    Session("VendorDirectory") = Platform.PathMapLookup("VendorData")
                    Session("DTSServerName") = Platform.Application.HostName()
                    Session("Device") = "Mobile"

                    VerifyTOS(InvWSS)

                    sLoadURL = Platform.PersistentValue("InventoryConfig", "StartPageURL", "portalframes.aspx")

                    sLoadURL = Platform.PersistentValue("InventoryConfig", "StartPageURL", "mp_portal.aspx")
                    If InStr(Request.ServerVariables("SERVER_NAME").ToLower, "localhost") Or InStr(Request.ServerVariables("SERVER_NAME").ToLower, "*************") Then
                        sLoadURL = Right(sLoadURL, sLoadURL.Length - InStr(sLoadURL, "/"))
                    End If

                    sServerName = Request.ServerVariables("SERVER_NAME")

                    sLoadURL = "parent.location.href='" & sLoadURL & "'"
                    If InStr(sLoadURL.ToLower, "altwebinventoryproduction") Then
                        Response.Cookies("DumacSettings")("uid") = Session("UserID")
                        Response.Cookies("DumacSettings")("pwd") = Session("PWD")
                        If not InStr(Request.ServerVariables("SERVER_NAME").ToLower, "localhost") > 0 
                            Response.Cookies("DumacSettings").Secure = True
                            Response.Cookies("DumacSettings").HttpOnly = True
                        End If
                        'Response.Redirect("https://www.dumacwebservices.com/altwebinventoryproduction/altlogin.aspx")
                        Response.Redirect("https://" & sServerName & "/altwebinventoryproduction/altlogin.aspx")
                        Exit Sub
                    ElseIf InStr(sLoadURL.ToLower, "webinventoryproduction203") Then
                        Response.Cookies("DumacSettings")("uid") = Session("UserID")
                        Response.Cookies("DumacSettings")("pwd") = Session("PWD")
                        If not InStr(Request.ServerVariables("SERVER_NAME").ToLower, "localhost") > 0 
                            Response.Cookies("DumacSettings").Secure = True
                            Response.Cookies("DumacSettings").HttpOnly = True
                        End If
                        'Response.Redirect("https://www.dumacwebservices.com/webinventoryproduction203/altlogin.aspx")
                        Response.Redirect("https://" & sServerName & "/webinventoryproduction203/altlogin.aspx")
                        Exit Sub
                    ElseIf InStr(sLoadURL.ToLower, "webinventoryproduction204") Then
                        Response.Cookies("DumacSettings")("uid") = Session("UserID")
                        Response.Cookies("DumacSettings")("pwd") = Session("PWD")
                        If not InStr(Request.ServerVariables("SERVER_NAME").ToLower, "localhost") > 0 
                            Response.Cookies("DumacSettings").Secure = True
                            Response.Cookies("DumacSettings").HttpOnly = True
                        End If
                        'Response.Redirect("https://www.dumacwebservices.com/webinventoryproduction204/altlogin.aspx")
                        Response.Redirect("https://" & sServerName & "/webinventoryproduction204/altlogin.aspx")
                        Exit Sub
                    ElseIf InStr(sLoadURL.ToLower, "webinventoryproduction400") Then
                        Response.Cookies("DumacSettings")("uid") = Session("UserID")
                        Response.Cookies("DumacSettings")("pwd") = Session("PWD")
                        If not InStr(Request.ServerVariables("SERVER_NAME").ToLower, "localhost") > 0 
                            Response.Cookies("DumacSettings").Secure = True
                            Response.Cookies("DumacSettings").HttpOnly = True
                        End If
                        'Response.Redirect("https://www.dumacwebservices.com/webinventoryproduction400/altlogin.aspx?Mobile=1")
                        Response.Redirect("https://" & sServerName & "/webinventoryproduction400/altlogin.aspx?Mobile=1")
                        Exit Sub
                    ElseIf InStr(sLoadURL.ToLower, "webinventoryproductiondev") Then
                        Response.Cookies("DumacSettings")("uid") = Session("UserID")
                        Response.Cookies("DumacSettings")("pwd") = Session("PWD")
                        If not InStr(Request.ServerVariables("SERVER_NAME").ToLower, "localhost") > 0 
                            Response.Cookies("DumacSettings").Secure = True
                            Response.Cookies("DumacSettings").HttpOnly = True
                        End If
                        'Response.Redirect("https://www.dumacwebservices.com/webinventoryproductiondev/altlogin.aspx?Mobile=1")
                        Response.Redirect("https://" & sServerName & "/webinventoryproductiondev/altlogin.aspx?Mobile=1")
                        Exit Sub
                        'Else
                        '    Response.Write(sLoadURL)
                    End If
                End If
            Else
                'do nothing
            End If
        Catch ePage_Load As Exception
            sLoadURL = "parent.location.href='login.aspx?failedlogin=1'"
            If Not Platform Is Nothing Then
                Platform.LogError(ePage_Load, "login.aspx", "Login")
            End If
        End Try
    End Sub

    Sub CarbonLogin(ByVal Sender As Object, ByVal E As EventArgs)
        Dim sServerName As String
        sServerName = Request.ServerVariables("SERVER_NAME")
        sServerName = sServerName.Replace("sbonet","sbo")

        Response.StatusCode = 307
        'Response.RedirectLocation = "https://www.dumacsbo.com/Login"
        Response.RedirectLocation = "https://" & sServerName & "/Login"
        Response.End()
    End Sub

    Sub CrewLogin(ByVal Sender As Object, ByVal E As EventArgs)
        Response.Redirect("http://sascheduler.dumacwebservices.com/")
    End Sub

    Sub VerifyTOS(ByRef InvWSS As InventoryWebSiteServices.InventoryServices)

        Dim bVerified As Boolean = False
        Dim bIsTOSSignatory As Boolean = False
        Dim sbSQL As New StringBuilder
        sbSQL.AppendLine("SELECT TOP 1")
        sbSQL.AppendLine("  MAX(ACT.[TOSRevsionID]) AS SignedTOSRevsionID")
        sbSQL.AppendLine("  ,MAX(TOS.[TOSRevsionID]) AS CurrentTOSRevsionID")
        sbSQL.AppendLine("  ,ACT.[UniversalAccountIdentifier]")
        sbSQL.AppendLine("FROM")
        sbSQL.AppendLine("  tmAccount ACT WITH(NOLOCK)")
        sbSQL.AppendLine("  ,tmTOS TOS WITH(NOLOCK)")
        sbSQL.AppendLine("WHERE")
        sbSQL.AppendLine("  TOSSignatory = 1")
        sbSQL.AppendLine("GROUP BY")
        sbSQL.AppendLine("  ACT.[UniversalAccountIdentifier]")

        Dim tabTOS As New DataTable
        InvWSS.GetData(sbSQL.ToString, tabTOS)

        If Not IsNothing(tabTOS) AndAlso tabTOS.Rows.Count > 0 Then
            bVerified = (tabTOS.Rows(0).Item(0) >= tabTOS.Rows(0).Item(1))
            If Session("AccountID") = tabTOS.Rows(0).Item(2) Then
                bIsTOSSignatory = True
            End If
            tabTOS.Dispose()
        Else
            bVerified = True
        End If

        If Not bVerified And bIsTOSSignatory Then
            sbSQL = New StringBuilder
            sbSQL.AppendLine("SELECT TOP 1")
            sbSQL.AppendLine("  TOSRevsionID")
            sbSQL.AppendLine("  ,TOSVerbiage")
            sbSQL.AppendLine("FROM")
            sbSQL.AppendLine("  tmTOS WITH(NOLOCK)")
            sbSQL.AppendLine("ORDER BY")
            sbSQL.AppendLine("  TOSRevsionID DESC")

            InvWSS.GetData(sbSQL.ToString, tabTOS)
            If Not IsNothing(tabTOS) AndAlso tabTOS.Rows.Count > 0 Then

                'txtTOSRevsionID.Text = tabTOS.Rows(0).Item(0).ToString
                'litTOSVerbiage.Text = tabTOS.Rows(0).Item(1).ToString

                'Page.ClientScript.RegisterStartupScript(Me.GetType(), "window-script", "showTOSSignatoryDialog()", True)
                'tabTOS.Dispose()
            End If
        ElseIf Not bVerified Then
            'Page.ClientScript.RegisterStartupScript(Me.GetType(), "window-script", "showTOSNotSignedDialog()", True)
            Session.Clear()
            Dim sServerName As String
            sServerName = Request.ServerVariables("SERVER_NAME")

            Response.Redirect("https://" & sServerName & "/webinventoryproduction400/TOSNotSigned.aspx")
        End If
    End Sub

    Private Sub Login_PreRender(sender As Object, e As EventArgs) Handles Me.PreRender
        If not InStr(Request.ServerVariables("SERVER_NAME").ToLower, "localhost") > 0 
            If Response.Cookies.Count > 0
                For each s As String In Response.Cookies.AllKeys
                    Response.Cookies(s).Secure = True
                    Response.Cookies(s).HttpOnly = True
                Next
            End If
        End If
    End Sub

    <WebMethod(EnableSession:=False)>
    Shared Function GetUserLoginOptions(userId As String, pwd As String) As UserLoginOptionsModel

        Dim InvWSS As New InventoryWebSiteServices.InventoryServices
        Dim Platform As ISBOPlatformV100

        Dim dtUserRoleRank = New DataTable

        Dim isCarbonUser As Boolean = False
        Dim userRank As Integer = 0
        Dim enforcedRankThreshold As Integer = 0

        Try

            Platform = InvWSS.Login(userId, pwd)

            ' Is Carbon User
            isCarbonUser = Platform.PersistentValue("DealerInfo", "Carbon", False)

            ' User Rank
            InvWSS.GetData("SELECT [dbo].[pf_GetUserRoleRank] ('" & Platform.Application.AccountIdentifier & "')", dtUserRoleRank)
            If Not IsNothing(dtUserRoleRank) AndAlso dtUserRoleRank.Rows.Count > 0 Then
                userRank = dtUserRoleRank.Rows(0).Item(0)
            End If

            ' Enforced Rank Threshold
            enforcedRankThreshold = Platform.PersistentValue("DealerInfo", "Carbon_EnforcedRankThreshold", 0)

        Catch ex As Exception

            If Not Platform Is Nothing Then
                Platform.LogError(ex, "login.aspx", "CanAccessSBOnet4")
            End If

        End Try

        Return New UserLoginOptionsModel() With {
            .IsCarbonUser = isCarbonUser,
            .CanAccessSBOnet4 = userRank >= enforcedRankThreshold
        }

    End Function

    Public Class UserLoginOptionsModel
        Public IsCarbonUser As Boolean = False
        Public CanAccessSBOnet4 As Boolean = False
    End Class

End Class
