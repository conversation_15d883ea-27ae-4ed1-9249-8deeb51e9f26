﻿Option Explicit On

Imports System.Data
Imports System.Net
Imports System.IO
Imports System.Data.SqlClient
Imports SBOTypeLibrary
Imports SBOWebSiteServices
Imports InventoryWebSiteServices

''' <summary>
''' BDean: Sprint 30 // D-01215 // TK-02893 - I modified the SQL to remove "Mobile Pay" (code 3A14) from the [CC] line. Wilco uses that code for their "Loyalty Cards", so that
'''                                         - code IS still included for Wilco.
'''Sprint 31 // B-02695 // TK-02980 2017-02-24 jjc 
'''     Added an enum to make column inserting easier.
'''     Resized dTotals Array so that it matches up With the Column Enum (there's a few entries that aren't used, but it's easier than maintaining a varying offset between Column number and Total Array Index number.
'''     Defined MinColumn MaxColumn Constants To have a Single place To change For range Of columns To Loop through For doing totals.
'''     Changed: MyDataGrid_ItemDataBound, MyDataGrid_ItemCreated, AddHeaderRow, AddTotalRow
'''         To use SalesJournalCol Enum instead Of Index numbers, And replaced several cascading If, elseif, elseif cascades with a better organized Select Case statement.
'''     Oh yeah, and Added Seperate Column for Promo and removed the Promo from Gift Card Sold and changed both to work off of the totalizers in the ops report config instead of codes stuffed into the page.
'''2020-01-17 jjc Sprint 76 SBOD-1463/1476 Adding '382A' to the long and lumbering list of codes for [CC] (Credit Cards)
'''2020-06-02 jjc Sprint 79w23 SBOD-1912 Changing CC from a code list to using Ops Codes less Mobile pay and cccard (whatever that is)
'''2020-06-29 jjc Sprint 79w26S SBOD-1912 Part Deux So apparetnly having Uber eats added to Credit cards ruffled some feathers since they recive the funds as an ACH not from their Credit Card processor
'''     (accounting type woes) Left the CC amount predomentantly based on the ops config, but added a new Uber Eats column and have subtracted that from CC.
'''2020-10-06 jjc Sprint 79w41 SBOD-1999 Added code 306A to Coupns.  Since that code is a negative nubmer, and the other's are positive, I subtracted it so that it would effoectively Add it.
'''2020-10-14 jjc Cleaned up the SQL to be more legible and consistiently formatted.  It's still garbage but it's better organized garbage.  No functional changes made.  Checking in because
'''     it's easer to easier to islate cleanup and makes it easier to diff acutal differences for the next version
'''2020-10-15 jjc Sprint 79w42 SBOD-2001 Changing Uber Eats to Delivey and adding applicable codes for other delivery services.
'''2020-10-15 jjc Sprint 79w42 change for discounts/coupons, change can be made in configuration.
'''2020-11-10 jjc Sprint 79w46 SBOD-2042 Missed need to change Column header in AddHeaderRow() From Uber Eats to Delievery.
'''2020-11-11 jjc Sprint 79w46 SBOD-2043 Revactored numerous ttsFinancial subqueries into a single pivoted query, also changed the query to a paramaerized query
'''2021-02-03 jjc Rev 38 Sprint 79w57 SBOD-2137 Adding WenDigital Code to delivery (382B)
'''2021-02-22 jjc Sprint 79w59 SBOD-2157 Adding Other Delivery Code to delivery (38C8)
'''2021-07-27 JW Sprint  79w68 SBOD-2280 Adding additional Taxable codes (6302, 6303, 6306, 6307)
'''2021-12-13 JLW Sprint 79w68 SBOD-2469 Adding Codes to Disc and Meals (300h,3012, 3049)
''' </summary>
''' 


Enum SalesJournalCol
    StoreID = 0
    Day = 1
    NetSales = 2
    Coupons = 3
    DiscountsMeals = 4
    Discounts = 5
    Meals = 6
    GCRedeemed = 7
    GCSold = 8
    Promo = 9
    SalesTax = 10
    TaxExempt = 11
    CalcTax = 12
    TaxDiff = 13
    PaidOut = 14
    CashVar = 15
    CashDeposit = 16
    CC = 17
    PUW = 18
    CheckCount = 19
    Taxable = 20
    NonTaxable = 21
    State = 22
    AuditPMT = 23
    Closed = 24
    NetExempt = 25
    DepositCount = 26
    DepositVerifiedCount = 27
    MobilePay = 28
    CCCards = 29
    Delivery = 30
    BDate = 31
End Enum


Partial Class SalesJournal
    Inherits System.Web.UI.Page

    Private InvWSS As InventoryWebSiteServices.InventoryServices

    Private sSQL As New StringBuilder

    Friend sTitle As String = ""
    Friend sSalesTable As New StringBuilder

    Dim bIsOhio As Boolean = False
    Dim bIsOhioPage As Boolean = False
    Dim nColSpan As Integer = 26
    Dim dTotals(1, 30) As Decimal
    Const MinColumn As Integer = 2
    Const MaxColumn As Integer = 30
    Dim nCurrentStoreID As Integer = 0
    Dim nInitialStoreID As Integer = 0
    Dim cLightGreen As System.Drawing.Color = Drawing.ColorTranslator.FromHtml("#99FF99")
    Dim cLightRed As System.Drawing.Color = Drawing.ColorTranslator.FromHtml("#FF9999")
    Friend RowY As Integer
    Dim bCombineMeals As Boolean
    Dim IsExport As Boolean = False
    'MW Bug 1984
    Dim bHasAuditPTM As Boolean = False
    Dim dStartDate As DateTime
    Dim dEndDate As DateTime

    Sub Page_Load(ByVal sender As Object, ByVal e As EventArgs) Handles Me.Load
        If Len(Session("UserID")) = 0 Then
            Response.Redirect("login.aspx?timeout=1")
        End If

        InvWSS = Session("IWSS")
        Try
            bCombineMeals = InvWSS.Platform.PersistentValue("InventoryConfig", "CombineMealsDisc")
        Catch ex As Exception
            bCombineMeals = 1
        End Try

        If Not IsPostBack Then
            PopWeeks()
            PopRegions()
            PopDistricts()
            PopStores()

            PopPeriods()
            PopMonths()
            txtStartDate.Text = DateAdd(DateInterval.Day, -1, Now()).ToShortDateString()
            txtEndDate.Text = DateAdd(DateInterval.Day, -1, Now()).ToShortDateString()
            txtSingleDate.Text = DateAdd(DateInterval.Day, -1, Now()).ToShortDateString()


            fsWeek.Visible = False
            fsPeriod.Visible = False
            fsMonth.Visible = False
            fsStartDate.Visible = False
            fsEndDate.Visible = False
            fsSingleDate.Visible = True

            SetDateRange()
            SetSQL()
            BindData()
            If Session("saro-export-excel") = "true" Then
                GoExcel()
            End If
        End If
    End Sub

    Sub BindData()
        Try
            btnExcel.Visible = False
            Dim dt As New DataTable

            Dim dtStores As New DataTable
            dtStores.Columns.Add(New DataColumn("ID", GetType(Integer)))

            'Loop through the Store list...
            For Each li As System.Web.UI.WebControls.ListItem In cblStore.Items
                'If a store is selected --> Append it
                If li.Selected Then
                    dtStores.LoadDataRow({li.Value}, LoadOption.Upsert)
                End If
            Next

            Dim objCmd As New SqlCommand
            objCmd.CommandText = sSQL.ToString()
            objCmd.CommandType = CommandType.Text

            Dim conDSS As New SqlConnection(InvWSS.ConnectString)
            conDSS.Open()

            With objCmd
                .Connection = conDSS
                .Parameters.AddWithValue("@StartDate", dStartDate)
                .Parameters.AddWithValue("@EndDate", dEndDate)
                .Parameters.Add(New SqlParameter("@StoreList", SqlDbType.Structured) With {.TypeName = "StoreList", .Value = dtStores})
            End With

            dt = InvWSS.Platform.SBODBExecQuerySQLCommand("Inventory", objCmd)

            If dt.Rows.Count > 0 Then
                nInitialStoreID = dt.Rows(0).Item("StoreID")
                btnExcel.Visible = True

            End If
            MyDataGrid.DataSource = dt.DefaultView
            MyDataGrid.DataBind()
        Catch eGoSalesReport As Exception
            'Response.Write(eGoSalesReport.ToString)
        End Try
    End Sub

    Private Sub BuildTitle(ByVal vsStoreID As Integer, Optional ByVal nOffset As Integer = 0, Optional ByVal bIsFooter As Boolean = False)

        sTitle = "<div style=""padding: 5px;"">Sales Journal Report <br />"

        If Not bIsFooter Then
            Dim tabStoreInfo As New DataTable
            InvWSS.GetData("SELECT TOP 1 StoreDescription, StoreNum FROM  tmStore WITH(NOLOCK) WHERE StoreID = " & vsStoreID, tabStoreInfo)
            For Each dRow As DataRow In tabStoreInfo.Rows
                sTitle &= dRow.Item(0) & " " & dRow.Item(1) & "<br />"
            Next
            tabStoreInfo.Dispose()
        Else
            sTitle = "Grand Totals<br />"
        End If

        Select Case ddRange.SelectedValue

            Case "optDaily"
                sTitle &= txtSingleDate.Text
                dStartDate = CDate(txtSingleDate.Text)
                dEndDate = CDate(txtSingleDate.Text)
            Case "optWeekly"
                'sTitle &= "Year: " & Session("rptopYear") & "<br />"
                'sTitle &= "Period: " & Session("rptopPeriod") & "<br />"
                'sTitle &= "Week: " & Session("rptopWeek") & "<br />"

                Dim sWeeks As String() = Split(ddWeek.SelectedValue, "|")
                dStartDate = CDate(sWeeks(0))
                dEndDate = CDate(sWeeks(1))
                sTitle &= "Week: " & ddWeek.SelectedValue.Replace("|", " - ")
            Case "optPeriod"
                Dim sPeriod As String() = Split(ddPeriod.SelectedValue, "|")
                dStartDate = CDate(sPeriod(0))
                dEndDate = CDate(sPeriod(1))
                sTitle &= "Period: " & ddPeriod.SelectedItem.Text
            Case "optMonthly"
                Dim sMonth As String() = Split(ddMonth.SelectedValue, "|")
                dStartDate = CDate(sMonth(1) & "/1/" & sMonth(0))
                dEndDate = DateAdd(DateInterval.Day, -1, DateAdd(DateInterval.Month, 1, dStartDate))
                sTitle &= "Month: " & ddMonth.SelectedItem.Text
            Case "optRange"
                dStartDate = CDate(txtStartDate.Text)
                dEndDate = CDate(txtEndDate.Text)
                sTitle &= "Date Range: " & dStartDate.ToShortDateString & " - " & dEndDate.ToShortDateString
            Case Else
                dStartDate = Now().ToShortDateString
                dEndDate = Now().ToShortDateString
        End Select

        Dim tcell As New TableCell
        tcell.Text = sTitle
        tcell.HorizontalAlign = HorizontalAlign.Center
        tcell.BackColor = System.Drawing.Color.White
        tcell.ColumnSpan = nColSpan
        Dim oRow As New DataGridItem(1, 5, ListItemType.Header)
        oRow.Cells.Add(tcell)

        MyDataGrid.Controls(0).Controls.AddAt(RowY + 1 - nOffset, oRow)
        RowY += 1
    End Sub

    Private Sub SetSQL()
        sSQL = New StringBuilder

        sSQL.AppendLine("/*")
        sSQL.AppendLine("DECLARE @StartDate DATETIME")
        sSQL.AppendLine("DECLARE @EndDate DATETIME")
        sSQL.AppendLine("")
        sSQL.AppendLine("SET @StartDate = '2020-10-01'")
        sSQL.AppendLine("SET @EndDate = '2020-10-31'")
        sSQL.AppendLine("")
        sSQL.AppendLine("DECLARE @StoreList StoreList")
        sSQL.AppendLine("")
        sSQL.AppendLine("INSERT @StoreList")
        sSQL.AppendLine("	([StoreID])")
        sSQL.AppendLine("SELECT ")
        sSQL.AppendLine("	[StoreID]")
        sSQL.AppendLine("FROM [dbo].[tmStore] WITH (NOLOCK)")
        sSQL.AppendLine("--WHERE [StoreID] IN (0,81,74,75,76,82,77,62,68,88,69,63,50,51,64,56,57,58,52,89,65,66,54,92,71,98,91,99,67,70,59,83,78,84,79,90,72,85,73,53,80,86,87,61,60,55,103,111,104,107) ")
        sSQL.AppendLine("--*/")
        sSQL.AppendLine("")
        sSQL.AppendLine("DECLARE @OpsCodes TABLE")
        sSQL.AppendLine("	([Tag] VARCHAR(16)")
        sSQL.AppendLine("	,[Column] NVARCHAR(16)")
        sSQL.AppendLine("	)")
        sSQL.AppendLine("")
        sSQL.AppendLine("DECLARE @FinCodes TABLE")
        sSQL.AppendLine("	([Tag] VARCHAR(16)")
        sSQL.AppendLine("	,[CODE] NVARCHAR(8)")
        sSQL.AppendLine("	,[ReverseSign] BIT DEFAULT(0)")
        sSQL.AppendLine("	)")
        sSQL.AppendLine("")
        sSQL.AppendLine("INSERT @OpsCodes([Tag],[Column]) VALUES('GiftCardSold', 'GiftCardSold')")
        sSQL.AppendLine("INSERT @OpsCodes([Tag],[Column]) VALUES('GiftCertPromoFin', 'GiftCertPromoFin')")
        sSQL.AppendLine("")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('CouponCode', '4050')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('CouponCode', 'COUP')")
        sSQL.AppendLine("")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE],[ReverseSign]) VALUES('GCRedeemed', '50', 1)")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE],[ReverseSign]) VALUES('GCRedeemed', '60', 0)")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE],[ReverseSign]) VALUES('GCRedeemed', '3A08', 1)")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE],[ReverseSign]) VALUES('GCRedeemed', '3A5A', 1)")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE],[ReverseSign]) VALUES('GCRedeemed', '3300', 0)")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE],[ReverseSign]) VALUES('GCRedeemed', '6503', 1)")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE],[ReverseSign]) VALUES('GCRedeemed', '3A2D', 1)")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE],[ReverseSign]) VALUES('GCRedeemed', '3A10', 1)")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE],[ReverseSign]) VALUES('GCRedeemed', '3AFD', 1)")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE],[ReverseSign]) VALUES('GCRedeemed', '382D', 1)")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE],[ReverseSign]) VALUES('GCRedeemed', 'GIF', 1)")
        sSQL.AppendLine("")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('CCCards', '3A30')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('CCCards', '3812')")
        sSQL.AppendLine("")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('MobilePay', '3A2E')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('MobilePay', '3A4E')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('MobilePay', '3A14')")
        sSQL.AppendLine("")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('SalesTax', '22')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('SalesTax', '23')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('SalesTax', '82')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('SalesTax', '87')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('SalesTax', '6201')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('SalesTax', '6202')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('SalesTax', '6203')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('SalesTax', '6206')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('SalesTax', '6207')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('SalesTax', 'TAX')")
        sSQL.AppendLine("")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('TaxExempt', '64')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('TaxExempt', '2007')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('TaxExempt', '2105')")
        sSQL.AppendLine("")

        'MSW 8/16/2013 Bug 1984 <- haha Thats the title of a song. 
        'DAF 2016-12-08 - RIP Mr. Bowie
        'jjc 2020-11-11 SQL has been refactored, moving this Bowie tribute to the analogous locaton in the refactored code.
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('AuditPMT', '2034')")
        sSQL.AppendLine("")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('Delivery', '3846')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('Delivery', '3847')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('Delivery', '3848')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('Delivery', '38C7')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('Delivery', '382B')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('Delivery', '38D1') --Canada UberEats")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('Delivery', '38C8') --Other Delivery")
        sSQL.AppendLine("")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('Taxable', '20')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('Taxable', '6301')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('Taxable', '6302')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('Taxable', '6303')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('Taxable', '6306')")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('Taxable', '6307')")
        sSQL.AppendLine("")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('NonTaxable', '2105')")
        sSQL.AppendLine("")
        sSQL.AppendLine("INSERT @FinCodes([Tag],[CODE]) VALUES('NetExempt', '2032')")
        sSQL.AppendLine("")
        sSQL.AppendLine("SELECT")
        sSQL.AppendLine("	 Data.[StoreID]")
        sSQL.AppendLine("	,CAST(DAY(Data.[BusinessDate]) AS VARCHAR) + ' - ' + DATENAME(dw, Data.[BusinessDate]) AS [Day]")
        sSQL.AppendLine("	,Data.[NetSales]")
        sSQL.AppendLine("")
        sSQL.AppendLine("	--2020-10-15 jjc So this is unexpected and screwy enough that I'm putting an explanation right here in the middle")
        sSQL.AppendLine("	--of the SQL there is a Persistent Value 'InventoryConfig\CombineMealsDisc' that will show or hide rows based on")
        sSQL.AppendLine("	--its value, So Coupons, Mgr Meal, Emp Meal, and Senior Disc are all included on the report, but how they are")
        sSQL.AppendLine("	--consolidated wil vary based on the setting of that persistent value")
        sSQL.AppendLine("	--As of this writing the only organization that has CombineMealsDisc set to false is Leakas")
        sSQL.AppendLine("	/********************************************************************/")
        sSQL.AppendLine("	/*                    *         *          *          *             */")
        sSQL.AppendLine("	/*                    * Coupons * Mgr Meal * Emp Meal * Senior Disc */")
        sSQL.AppendLine("	/*                    *         *          *          *             */")
        sSQL.AppendLine("	/********************************************************************/")
        sSQL.AppendLine("	/* Combined Meals     * Coupons *            Disc + Meal            */")
        sSQL.AppendLine("	/********************************************************************/")
        sSQL.AppendLine("	/* Not Combined Meals *      Discounts     *          Meals         */ --Leakas")
        sSQL.AppendLine("	/********************************************************************/")
        sSQL.AppendLine("	--Combined Meals")
        sSQL.AppendLine("	,(Data.[Coupons] + Data.[CouponCode]) AS [Coupons]")
        sSQL.AppendLine("	,Data.[DiscountsMeals]")
        sSQL.AppendLine("	--/Combined Meals")
        sSQL.AppendLine("	--Not Combined Meals")
        sSQL.AppendLine("	,Data.[Discounts]")
        sSQL.AppendLine("	,Data.[Meals]")
        sSQL.AppendLine("	--/Not Combined Meals")
        sSQL.AppendLine("	,Data.[GCRedeemed]")
        sSQL.AppendLine("	,Data.[GiftCardSold] AS [GCSold]")
        sSQL.AppendLine("	,Data.[GiftCertPromoFin] + Data.[GiftCertPromoPlu] AS [Promo]")
        sSQL.AppendLine("	,Data.[SalesTax]")
        sSQL.AppendLine("	,Data.[TaxExempt]")
        sSQL.AppendLine("	,Data.[CalcTax]")
        sSQL.AppendLine("	,0 AS [TaxDiff]")
        sSQL.AppendLine("	,Data.[PaidOut]")

        'MSW 8/16/2013 Bug 1984
        'MSW 7/10/2014 Bug 2411 Steve Williams wanted me to note
        'one this page (he pointed here) that Financial Accum 3500 (paid outs)
        'had to be added to R & L Foods on the Admin-PosProgram-Financial Page.
        'sSQL.AppendLine("   ,[CashVar] ")
        'LY 08/10/2014 BZ2301 Subtract DTFA (Donations from CashVar)
        '2015-04-03 jjc Bug 2677 Undoing Bug 2301 Modification.  DTFA accounting moved to POS Finanical Accumulators as
        'used by the Finanical Rollup so the [CashVar] number should now be correct without outside adjustment.

        sSQL.AppendLine("	,Data.[CashVar] - Data.[AuditPMT] AS [CashVar]")
        sSQL.AppendLine("	,Data.[CashDeposit]")
        sSQL.AppendLine("	,Data.[OpsChargeTotal] - Data.[MobilePay] - Data.[CCCards] - Data.[Delivery] AS [CC]")
        sSQL.AppendLine("	,Data.[PUW]")
        sSQL.AppendLine("	,Data.[CheckCount]")
        sSQL.AppendLine("	,Data.[Taxable]")
        sSQL.AppendLine("	,Data.[NonTaxable]")
        sSQL.AppendLine("	,Data.[State]")
        sSQL.AppendLine("	,Data.[AuditPMT]")
        sSQL.AppendLine("	,ISNULL(Data.[Closed], 0) AS [Closed]")
        sSQL.AppendLine("	,Data.[NetExempt]")
        sSQL.AppendLine("	,Data.[DepositCount]")
        sSQL.AppendLine("	,Data.[DepositVerifiedCount]")
        sSQL.AppendLine("	,Data.[MobilePay]")
        sSQL.AppendLine("	,Data.[CCCards]")
        sSQL.AppendLine("	,Data.[Delivery]")
        sSQL.AppendLine("	,Data.[BusinessDate]")
        sSQL.AppendLine("FROM")
        sSQL.AppendLine("	(")
        sSQL.AppendLine("	SELECT")
        sSQL.AppendLine("		 FSR.[StoreID]")
        sSQL.AppendLine("		,FSR.[BusinessDate]")
        sSQL.AppendLine("		,FSR.[NetSales]")
        sSQL.AppendLine("		,FSR.[Coupons]")
        sSQL.AppendLine("		,ISNULL(tsF.[CouponCode], 0) AS [CouponCode]")
        sSQL.AppendLine("		,FSR.[SeniorCitizenDiscounts] + FSR.[EmpMeals] + FSR.[MgrMeals] + FSR.[FreeItems] + FSR.[OtherDiscounts] AS [DiscountsMeals]")
        sSQL.AppendLine("		,FSR.[Coupons] + FSR.[SeniorCitizenDiscounts] + FSR.[FreeItems] + FSR.[OtherDiscounts] AS [Discounts]")
        sSQL.AppendLine("		,FSR.[EmpMeals] + FSR.[MgrMeals] AS [Meals]")
        sSQL.AppendLine("		,ISNULL(tsF.[GCRedeemed], 0) AS [GCRedeemed]")
        sSQL.AppendLine("		,ISNULL(Ops.[GiftCardSold], 0) AS [GiftCardSold]")
        sSQL.AppendLine("		,ISNULL(Ops.[GiftCertPromoFin], 0) AS [GiftCertPromoFin]")
        sSQL.AppendLine("		,	(")
        sSQL.AppendLine("			SELECT ISNULL(SUM([ExtendedPrice]),0)")
        sSQL.AppendLine("			FROM [dbo].[BI_trStoreSalesDetail] AS [tsPLU] WITH(NOLOCK)")
        sSQL.AppendLine("				INNER JOIN [dbo].[tmDynamic-PLUCodes] AS [PluCode] WITH(NOLOCK)")
        sSQL.AppendLine("					ON tsPLU.[STOREID] = FSR.[StoreID]")
        sSQL.AppendLine("						AND tsPLU.[BUSINESSDATE] = FSR.[BusinessDate]")
        sSQL.AppendLine("						AND (tsPLU.[PLUNumber] BETWEEN PluCode.[PLU] AND PluCode.[EndPLU] OR tsPLU.[PLUNumber] = PluCode.[PLU]) AND (STINV.[POSProgramID] = PluCode.[POSProgramID] OR PluCode.[POSProgramID] = 0)")
        sSQL.AppendLine("						AND PluCode.[Tag] = 'GiftCertPromoPlu'")
        sSQL.AppendLine("			) AS [GiftCertPromoPlu]")
        sSQL.AppendLine("		,ISNULL(tsF.[CCCards], 0) AS [CCCards]")
        sSQL.AppendLine("		,ISNULL(tsF.[MobilePay], 0) AS [MobilePay]")
        sSQL.AppendLine("		,ISNULL(tsF.[SalesTax], 0) AS [SalesTax]")
        sSQL.AppendLine("		,ISNULL(tsF.[TaxExempt], 0) AS [TaxExempt]")
        sSQL.AppendLine("		,STINV.[CalcTaxRate] AS [CalcTax]")
        sSQL.AppendLine("		,FSR.[CashVar]")
        sSQL.AppendLine("		,ISNULL(tsF.[AuditPMT], 0) AS [AuditPMT]")
        sSQL.AppendLine("		,(SELECT ISNULL(SUM([Deposit]), 0) FROM [dbo].[vReport_Deposit] WITH(NOLOCK) WHERE [BusinessDate] = FSR.[BusinessDate] AND [StoreID] = FSR.[StoreID]) AS [CashDeposit]")
        sSQL.AppendLine("		,ISNULL(tsF.[Delivery], 0) AS [Delivery]")
        sSQL.AppendLine("		,	(")
        sSQL.AppendLine("			SELECT ISNULL(SUM([Amount]) ,0)")
        sSQL.AppendLine("			FROM [dbo].[ttsFinancial] AS [tsF] WITH(NOLOCK)")
        sSQL.AppendLine("				INNER JOIN [dbo].[tmDynamic-FinancialCodes] AS [FinCode] WITH(NOLOCK)")
        sSQL.AppendLine("					INNER JOIN [tmDynamic-Sum] AS [SumTotal]")
        sSQL.AppendLine("						ON FinCode.[Tag] = SumTotal.[TagReference]")
        sSQL.AppendLine("							AND SumTotal.[Tag] = 'ChargeTotal'")
        sSQL.AppendLine("					ON tsF.[STOREID] = FSR.[StoreID]")
        sSQL.AppendLine("						AND tsF.[BUSINESSDATE] = FSR.[BusinessDate]")
        sSQL.AppendLine("						AND tsF.[CODE] = FinCode.[Code]")
        sSQL.AppendLine("						AND FinCode.[POSProgramID] = STINV.[POSProgramID]")
        sSQL.AppendLine("			) AS [OpsChargeTotal]")
        sSQL.AppendLine("		,FSR.[PaidOut]")
        sSQL.AppendLine("		,ISNULL(tsF.[Taxable], 0) AS [Taxable]")
        sSQL.AppendLine("		,ISNULL(tsF.[NonTaxable], 0) AS [NonTaxable]")
        sSQL.AppendLine("		,FSR.[PUW]")
        sSQL.AppendLine("		,FSR.[CheckCount]")
        sSQL.AppendLine("		,STO.[State]")
        sSQL.AppendLine("		,FSR.[StoreNumber]")
        sSQL.AppendLine("		,SS.[Closed]")
        sSQL.AppendLine("		,ISNULL(tsF.[NetExempt], 0) AS [NetExempt]")
        sSQL.AppendLine("		,(SELECT COUNT(*) FROM [dbo].[vReport_Deposit] WHERE [BusinessDate] = FSR.[BusinessDate] AND [StoreID] = FSR.[StoreID]) AS [DepositCount]")
        sSQL.AppendLine("		,(SELECT COUNT(*) FROM [dbo].[vReport_Deposit] WHERE [BusinessDate] = FSR.[BusinessDate] AND [StoreID] = FSR.[StoreID] AND [VerifiedDateTime] IS NOT NULL) AS [DepositVerifiedCount]")
        sSQL.AppendLine("	FROM [dbo].[vjFinancialSummaryReport_P1] AS [FSR] WITH(NOLOCK)")
        sSQL.AppendLine("		LEFT OUTER JOIN")
        sSQL.AppendLine("			(")
        sSQL.AppendLine("			SELECT")
        sSQL.AppendLine("				 PVT.[StoreID]")
        sSQL.AppendLine("				,PVT.[BUSINESSDATE]")
        sSQL.AppendLine("				,ISNULL(PVT.[AuditPMT], 0) AS [AuditPMT]")
        sSQL.AppendLine("				,ISNULL(PVT.[CCCards], 0) AS [CCCards]")
        sSQL.AppendLine("				,ISNULL(PVT.[CouponCode], 0) AS [CouponCode]")
        sSQL.AppendLine("				,ISNULL(PVT.[Delivery], 0) AS [Delivery]")
        sSQL.AppendLine("				,ISNULL(PVT.[GCRedeemed], 0) AS [GCRedeemed]")
        sSQL.AppendLine("				,ISNULL(PVT.[MobilePay], 0) AS [MobilePay]")
        sSQL.AppendLine("				,ISNULL(PVT.[NetExempt], 0) AS [NetExempt]")
        sSQL.AppendLine("				,ISNULL(PVT.[NonTaxable], 0) AS [NonTaxable]")
        sSQL.AppendLine("				,ISNULL(PVT.[SalesTax], 0) AS [SalesTax]")
        sSQL.AppendLine("				,ISNULL(PVT.[Taxable], 0) AS [Taxable]")
        sSQL.AppendLine("				,ISNULL(PVT.[TaxExempt], 0) AS [TaxExempt]")
        sSQL.AppendLine("			FROM")
        sSQL.AppendLine("				(")
        sSQL.AppendLine("				SELECT")
        sSQL.AppendLine("					 tsF.[StoreID]")
        sSQL.AppendLine("					,tsF.[BUSINESSDATE]")
        sSQL.AppendLine("					,Codes.[Tag]")
        sSQL.AppendLine("					,SUM")
        sSQL.AppendLine("						(")
        sSQL.AppendLine("						ISNULL([Amount], 0)")
        sSQL.AppendLine("						* CASE WHEN Codes.[ReverseSign] = 0 THEN 1 ELSE -1 END")
        sSQL.AppendLine("						) AS [Amount]")
        sSQL.AppendLine("				FROM [dbo].[ttsFinancial] AS [tsF] WITH(NOLOCK)")
        sSQL.AppendLine("					INNER JOIN @FinCodes AS [Codes]")
        sSQL.AppendLine("						ON tsF.[CODE] = Codes.[CODE]")
        sSQL.AppendLine("				WHERE [BusinessDate] BETWEEN @StartDate AND @EndDate")
        sSQL.AppendLine("					AND [StoreID] IN (SELECT [StoreID] FROM @StoreList)")
        sSQL.AppendLine("				GROUP BY")
        sSQL.AppendLine("					 tsF.[StoreID]")
        sSQL.AppendLine("					,tsF.[BUSINESSDATE]")
        sSQL.AppendLine("					,Codes.[Tag]")
        sSQL.AppendLine("				) AS DAT")
        sSQL.AppendLine("				PIVOT")
        sSQL.AppendLine("				(")
        sSQL.AppendLine("				SUM(DAT.[Amount])")
        sSQL.AppendLine("				FOR DAT.[Tag] IN")
        sSQL.AppendLine("					([AuditPMT]")
        sSQL.AppendLine("					,[CCCards]")
        sSQL.AppendLine("					,[CouponCode]")
        sSQL.AppendLine("					,[Delivery]")
        sSQL.AppendLine("					,[GCRedeemed]")
        sSQL.AppendLine("					,[MobilePay]")
        sSQL.AppendLine("					,[NetExempt]")
        sSQL.AppendLine("					,[NonTaxable]")
        sSQL.AppendLine("					,[SalesTax]")
        sSQL.AppendLine("					,[Taxable]")
        sSQL.AppendLine("					,[TaxExempt]")
        sSQL.AppendLine("					)")
        sSQL.AppendLine("				) AS [PVT]")
        sSQL.AppendLine("			) AS [tsF]")
        sSQL.AppendLine("			ON FSR.[StoreID] = tsf.[STOREID]")
        sSQL.AppendLine("				AND FSR.[BusinessDate] = tsf.[BusinessDate]")
        sSQL.AppendLine("		LEFT OUTER JOIN")
        sSQL.AppendLine("			(")
        sSQL.AppendLine("			SELECT")
        sSQL.AppendLine("				 PVT.[StoreID]")
        sSQL.AppendLine("				,PVT.[BUSINESSDATE]")
        sSQL.AppendLine("				,ISNULL(PVT.[GiftCardSold], 0) AS [GiftCardSold]")
        sSQL.AppendLine("				,ISNULL(PVT.[GiftCertPromoFin], 0) AS [GiftCertPromoFin]")
        sSQL.AppendLine("			FROM")
        sSQL.AppendLine("				(")
        sSQL.AppendLine("				SELECT ")
        sSQL.AppendLine("					 tsF.[StoreID]")
        sSQL.AppendLine("					,tsF.[BUSINESSDATE]")
        sSQL.AppendLine("					,OC.[Column]")
        sSQL.AppendLine("					,SUM(ISNULL([Amount], 0)) AS [Amount]")
        sSQL.AppendLine("				FROM [dbo].[ttsFinancial] AS [tsF] WITH(NOLOCK)")
        sSQL.AppendLine("					INNER JOIN tmStoreInvt AS [tmSI] WITH(NOLOCK)")
        sSQL.AppendLine("						ON tsf.[STOREID] = tmSI.[StoreID]")
        sSQL.AppendLine("					INNER JOIN [dbo].[tmDynamic-FinancialCodes] AS [FinCode] WITH(NOLOCK)")
        sSQL.AppendLine("						INNER JOIN @OpsCodes AS [OC]")
        sSQL.AppendLine("							ON FinCode.[Tag] = OC.[Tag]")
        sSQL.AppendLine("						ON tsF.[STOREID]  IN (SELECT [StoreID] FROM @StoreList)")
        sSQL.AppendLine("							AND tsF.[BUSINESSDATE] BETWEEN @StartDate AND @EndDate")
        sSQL.AppendLine("							AND tsF.[CODE] = FinCode.[Code]")
        sSQL.AppendLine("							AND FinCode.[POSProgramID] = tmSI.[POSProgramID]")
        sSQL.AppendLine("				GROUP BY")
        sSQL.AppendLine("					 tsF.[StoreID]")
        sSQL.AppendLine("					,tsF.[BUSINESSDATE]")
        sSQL.AppendLine("					,OC.[Column]")
        sSQL.AppendLine("				) AS DAT")
        sSQL.AppendLine("				PIVOT")
        sSQL.AppendLine("				(")
        sSQL.AppendLine("				SUM(DAT.[Amount])")
        sSQL.AppendLine("				FOR DAT.[Column] IN")
        sSQL.AppendLine("					([GiftCardSold]")
        sSQL.AppendLine("					,[GiftCertPromoFin]")
        sSQL.AppendLine("					)")
        sSQL.AppendLine("				) AS [PVT]")
        sSQL.AppendLine("			) AS [Ops]")
        sSQL.AppendLine("			ON FSR.[StoreID] = Ops.[STOREID]")
        sSQL.AppendLine("				AND FSR.[BusinessDate] = Ops.[BusinessDate]")
        sSQL.AppendLine("		LEFT OUTER JOIN [dbo].[tmStoreInvt] AS [STINV] WITH(NOLOCK)")
        sSQL.AppendLine("			ON STINV.[StoreID] = FSR.[StoreID]")
        sSQL.AppendLine("		LEFT OUTER JOIN [dbo].[tmStore] AS [STO] WITH(NOLOCK)")
        sSQL.AppendLine("			ON STO.[StoreID] = FSR.[StoreID]")
        sSQL.AppendLine("		LEFT OUTER JOIN [dbo].[INV_tsSiteStatus] AS [SS] WITH(NOLOCK)")
        sSQL.AppendLine("			ON STO.[StoreID] = SS.[StoreID]")
        sSQL.AppendLine("				AND FSR.[BusinessDate] = SS.[BusinessDate]")
        sSQL.AppendLine("	WHERE FSR.StoreID IN (SELECT [StoreID] FROM @StoreList)")
        sSQL.AppendLine("		AND FSR.BusinessDate BETWEEN @StartDate AND @EndDate")
        sSQL.AppendLine("	) AS [Data]")
        sSQL.AppendLine("ORDER BY")
        sSQL.AppendLine("	 Data.[StoreNumber]")
        sSQL.AppendLine("	,Data.[BusinessDate]")
        sSQL.AppendLine("")
        sSQL.AppendLine("")
        sSQL.AppendLine("")

    End Sub


    Private Function SetSQLWhere(Optional ByVal bUseDate As Boolean = True) As StringBuilder
        Dim sbSQLWhere As New StringBuilder
        sbSQLWhere.Append("WHERE FSR.StoreID IS NOT NULL ")

        Dim sbSelectedStores As New StringBuilder
        Dim SelStores As String = "0"
        With sbSelectedStores

            'Loop through the Store list...
            For Each li As System.Web.UI.WebControls.ListItem In cblStore.Items

                'If a store is selected --> Append it
                If li.Selected Then
                    .AppendLine("    " & If(.Length = 0, " ", ",") & "( " & li.Value & " )")
                    SelStores += If(SelStores.Length = 0, " ", ",") & li.Value
                End If

            Next

        End With

        sbSQLWhere.Append("AND FSR.StoreID IN (")
        sbSQLWhere.Append(SelStores)
        sbSQLWhere.AppendLine(") ")
        sbSQLWhere.Append("AND FSR.BusinessDate BETWEEN '" & dStartDate.ToShortDateString & "' AND '" & dEndDate.ToShortDateString & "' ")


        Return sbSQLWhere

    End Function

    Sub MyDataGrid_ItemDataBound(ByVal source As Object, ByVal e As DataGridItemEventArgs)
        If e.Item.ItemType = ListItemType.Footer Then
            AddTotalRow()
            AddPageBreak()
            BuildTitle(0, 1, True)
            AddHeaderRow()
        ElseIf e.Item.ItemType <> ListItemType.Header And e.Item.ItemType <> ListItemType.Footer Then
            If e.Item.DataItem("Closed") = 0 Then
                e.Item.Cells(1).Text = e.Item.Cells(1).Text & "*"
            End If
            Dim dCalcTax, dTaxDiff As Decimal
            If nCurrentStoreID = 0 Then
                nCurrentStoreID = e.Item.DataItem("StoreID")
                BuildTitle(nCurrentStoreID, 1)
            ElseIf nCurrentStoreID <> e.Item.DataItem("StoreID") Then
                AddTotalRow()
                nCurrentStoreID = e.Item.DataItem("StoreID")
                AddPageBreak()
                BuildTitle(nCurrentStoreID)
                AddHeaderRow(nCurrentStoreID)
            End If

            RowY += 1

            For I As Integer = 0 To 1
                dTotals(I, SalesJournalCol.NetSales) += e.Item.DataItem("NetSales")
                dTotals(I, SalesJournalCol.Coupons) += e.Item.DataItem("Coupons")
                dTotals(I, SalesJournalCol.DiscountsMeals) += e.Item.DataItem("DiscountsMeals")
                dTotals(I, SalesJournalCol.Discounts) += e.Item.DataItem("Discounts")
                dTotals(I, SalesJournalCol.Meals) += e.Item.DataItem("Meals")
                dTotals(I, SalesJournalCol.GCRedeemed) += e.Item.DataItem("GCRedeemed")
                dTotals(I, SalesJournalCol.GCSold) += e.Item.DataItem("GCSold")
                dTotals(I, SalesJournalCol.Promo) += e.Item.DataItem("Promo")
                dTotals(I, SalesJournalCol.SalesTax) += e.Item.DataItem("SalesTax")
                dTotals(I, SalesJournalCol.TaxExempt) += e.Item.DataItem("TaxExempt")
                '2021-04-09 jjc Sprint 79w64 SBOD-2200 So this atttempt to approximately validate tax rates initially counted on the Non-taxable value which was populated by Panasoinc/Xpient, but is not available from Aloha.
                'As a compormoise, if there's a value for "NonTaxable" use that formula, if there is not then use "Taxable" for the calculation.
                If e.Item.DataItem("NonTaxable") > 0 Then
                    dTotals(I, SalesJournalCol.CalcTax) += (e.Item.DataItem("NetSales") - e.Item.DataItem("NonTaxable") + e.Item.DataItem("NetExempt")) * e.Item.DataItem("CalcTax") 'CalcTax
                    dTotals(I, SalesJournalCol.TaxDiff) += e.Item.DataItem("SalesTax") - ((e.Item.DataItem("NetSales") - e.Item.DataItem("NonTaxable") + e.Item.DataItem("NetExempt")) * e.Item.DataItem("CalcTax")) 'TaxDiff
                Else
                    dTotals(I, SalesJournalCol.CalcTax) += e.Item.DataItem("Taxable") * e.Item.DataItem("CalcTax") 'CalcTax
                    dTotals(I, SalesJournalCol.TaxDiff) += e.Item.DataItem("SalesTax") - (e.Item.DataItem("Taxable") * e.Item.DataItem("CalcTax")) 'TaxDiff
                End If
                dTotals(I, SalesJournalCol.PaidOut) += e.Item.DataItem("PaidOut")
                dTotals(I, SalesJournalCol.CashVar) += e.Item.DataItem("CashVar")
                dTotals(I, SalesJournalCol.CashDeposit) += e.Item.DataItem("CashDeposit")
                dTotals(I, SalesJournalCol.CC) += e.Item.DataItem("CC")
                dTotals(I, SalesJournalCol.PUW) += e.Item.DataItem("PUW")
                dTotals(I, SalesJournalCol.CheckCount) += e.Item.DataItem("CheckCount")
                dTotals(I, SalesJournalCol.Taxable) += e.Item.DataItem("Taxable")
                dTotals(I, SalesJournalCol.NonTaxable) += e.Item.DataItem("NonTaxable")
                dTotals(I, SalesJournalCol.AuditPMT) += e.Item.DataItem("AuditPMT")
                dTotals(I, SalesJournalCol.NetExempt) += e.Item.DataItem("NetExempt")
                dTotals(I, SalesJournalCol.MobilePay) += e.Item.DataItem("MobilePay")
                dTotals(I, SalesJournalCol.CCCards) += e.Item.DataItem("CCCards")
                dTotals(I, SalesJournalCol.Delivery) += e.Item.DataItem("Delivery")
            Next

            For I As Integer = MinColumn To MaxColumn
                e.Item.Cells(I).HorizontalAlign = HorizontalAlign.Right
                Select Case I
                    Case SalesJournalCol.CalcTax
                        If e.Item.DataItem("NonTaxable") > 0 Then
                            dCalcTax = (e.Item.DataItem("NetSales") - e.Item.DataItem("NonTaxable") + e.Item.DataItem("NetExempt")) * e.Item.DataItem("CalcTax")
                        Else
                            dCalcTax = e.Item.DataItem("Taxable") * e.Item.DataItem("CalcTax")
                        End If
                        e.Item.Cells(I).Text = FormatNumber(dCalcTax, 2, True, False, True)
                    Case SalesJournalCol.TaxDiff
                        dTaxDiff = e.Item.DataItem("SalesTax") - dCalcTax
                        If Not IsDBNull(e.Item.DataItem("State")) Then
                            If UCase(e.Item.DataItem("State")) = "OH" Or UCase(e.Item.DataItem("State")) = "OHIO" Then
                                bIsOhio = True
                                bIsOhioPage = True
                                e.Item.Cells(I).Text = "0.00"
                            Else
                                bIsOhio = False
                                e.Item.Cells(I).Text = FormatNumber(dTaxDiff, 2, True, False, True)
                                If dTaxDiff < 0 Then
                                    e.Item.Cells(I).BackColor = System.Drawing.ColorTranslator.FromHtml("#F62217")
                                End If
                            End If
                        Else
                            bIsOhio = False
                            e.Item.Cells(I).Text = FormatNumber(dTaxDiff, 2, True, False, True)
                            If dTaxDiff < 0 Then
                                e.Item.Cells(I).BackColor = System.Drawing.ColorTranslator.FromHtml("#F62217")
                            End If
                        End If
                    Case SalesJournalCol.CashDeposit
                        If e.Item.DataItem("DepositCount") > 0 Then
                            If e.Item.DataItem("DepositCount") = e.Item.DataItem("DepositVerifiedCount") Then
                                e.Item.Cells(I).BackColor = cLightGreen
                            Else
                                e.Item.Cells(I).BackColor = cLightRed
                            End If
                        End If
                        If IsExport Then
                            e.Item.Cells(I).Text = FormatNumber(e.Item.DataItem(I), 2, True, False, True)
                        Else
                            e.Item.Cells(I).Text = "<a href=""TotalDeposits.aspx?storeid=" & e.Item.DataItem("StoreID") & "&bdate=" & CDate(e.Item.DataItem("BusinessDate")).ToShortDateString & """>" & FormatNumber(e.Item.DataItem(I), 2, True, False, True) & "</a>"
                        End If
                    Case SalesJournalCol.CheckCount
                        e.Item.Cells(I).Text = FormatNumber(e.Item.DataItem(I), 0, True, False, True)
                    Case SalesJournalCol.State
                        'Text, don't change formatting and don't hit the else clause
                    Case Else
                        e.Item.Cells(I).Text = FormatNumber(e.Item.DataItem(I), 2, True, False, True)
                End Select
            Next

            'MW Bug 1984
            If CDbl(e.Item.DataItem("AuditPMT").ToString) <> 0 Then
                bHasAuditPTM = True
                trAuditPMTWarning.Visible = True
                e.Item.Cells(SalesJournalCol.CashVar).Text = Math.Round(CDbl(e.Item.DataItem(SalesJournalCol.CashVar).ToString), 2).ToString + "*" '14
            End If

        End If

    End Sub

    Friend Sub MyDataGrid_ItemCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.DataGridItemEventArgs)
        e.Item.Cells(SalesJournalCol.BDate).Visible = False
        e.Item.Cells(SalesJournalCol.StoreID).Visible = False
        e.Item.Cells(SalesJournalCol.State).Visible = False
        'Mark Williams
        e.Item.Cells(SalesJournalCol.AuditPMT).Visible = False
        e.Item.Cells(SalesJournalCol.Closed).Visible = False

        e.Item.Cells(SalesJournalCol.DepositCount).Visible = False
        e.Item.Cells(SalesJournalCol.DepositVerifiedCount).Visible = False
        If Not bCombineMeals Then
            e.Item.Cells(SalesJournalCol.Coupons).Visible = False
            e.Item.Cells(SalesJournalCol.DiscountsMeals).Visible = False
        Else
            e.Item.Cells(SalesJournalCol.Discounts).Visible = False
            e.Item.Cells(SalesJournalCol.Meals).Visible = False
        End If


        If (e.Item.ItemType = ListItemType.Header) Then
            e.Item.Cells(SalesJournalCol.StoreID).Text = "StoreID"
            e.Item.Cells(SalesJournalCol.Day).Text = "Day"
            e.Item.Cells(SalesJournalCol.NetSales).Text = "Net Sales"
            e.Item.Cells(SalesJournalCol.Coupons).Text = "Coupons"
            e.Item.Cells(SalesJournalCol.DiscountsMeals).Text = "Disc & Meals"
            e.Item.Cells(SalesJournalCol.Discounts).Text = "Coup & Disc"
            e.Item.Cells(SalesJournalCol.Meals).Text = "Meals"
            e.Item.Cells(SalesJournalCol.GCRedeemed).Text = "GC Red."
            e.Item.Cells(SalesJournalCol.GCSold).Text = "GC Sold"
            e.Item.Cells(SalesJournalCol.Promo).Text = "Promo"
            e.Item.Cells(SalesJournalCol.SalesTax).Text = "Sales Tax"
            e.Item.Cells(SalesJournalCol.TaxExempt).Text = "Tax Exempt"

            Dim tCalcTaxTable As New DataTable
            If IsNothing(InvWSS) Then
                InvWSS = Session("IWSS")
            End If
            InvWSS.GetData("SELECT CalcTaxRate FROM tmStoreInvt WITH(NOLOCK) WHERE StoreID = " & nInitialStoreID, tCalcTaxTable)
            If tCalcTaxTable.Rows.Count > 0 AndAlso Not IsDBNull(tCalcTaxTable.Rows(0).Item(0)) Then
                e.Item.Cells(SalesJournalCol.CalcTax).Text = "Calc Tax<br /><span style=""font-size: 6pt;"">(" & FormatPercent(tCalcTaxTable.Rows(0).Item(0)) & ")</span>"
            Else
                e.Item.Cells(SalesJournalCol.CalcTax).Text = "Calc Tax"
            End If

            e.Item.Cells(SalesJournalCol.TaxDiff).Text = "Tax Diff."
            e.Item.Cells(SalesJournalCol.PaidOut).Text = "Paid Out"
            e.Item.Cells(SalesJournalCol.CashVar).Text = "Cash +/-"
            e.Item.Cells(SalesJournalCol.CashDeposit).Text = "Cash<br />Deposit"
            e.Item.Cells(SalesJournalCol.CC).Text = "CC<br />Deposit"
            e.Item.Cells(SalesJournalCol.PUW).Text = "PUW"
            e.Item.Cells(SalesJournalCol.CheckCount).Text = "Trans.<br />Count"
            e.Item.Cells(SalesJournalCol.Taxable).Text = "Taxable"
            e.Item.Cells(SalesJournalCol.NonTaxable).Text = "Non-Taxable"
            e.Item.Cells(SalesJournalCol.State).Text = "State"
            e.Item.Cells(SalesJournalCol.AuditPMT).Text = "AuditPMT"
            e.Item.Cells(SalesJournalCol.Closed).Text = "Closed"
            e.Item.Cells(SalesJournalCol.NetExempt).Text = "Net$ Exempt"
            e.Item.Cells(SalesJournalCol.MobilePay).Text = "Mobile Pay"
            e.Item.Cells(SalesJournalCol.CCCards).Text = "Campus Cards"
            e.Item.Cells(SalesJournalCol.Delivery).Text = "Delivery"
            e.Item.BackColor = System.Drawing.Color.Beige
            e.Item.HorizontalAlign = HorizontalAlign.Center
        ElseIf (e.Item.ItemType = ListItemType.Footer) Then

            For I As Integer = MinColumn To MaxColumn
                e.Item.Cells(I).HorizontalAlign = HorizontalAlign.Right

                Select Case I
                    Case SalesJournalCol.State, SalesJournalCol.Closed
                        'Text Skip
                    Case SalesJournalCol.TaxExempt
                        If bIsOhioPage Then
                            e.Item.Cells(I).Text = "N/A"
                        Else
                            e.Item.Cells(I).Text = FormatNumber(dTotals(0, I), 2, True, False, True)
                            If e.Item.Cells(I).Text < 0 Then
                                e.Item.Cells(I).BackColor = System.Drawing.ColorTranslator.FromHtml("#F62217")
                            End If
                        End If
                    Case SalesJournalCol.CheckCount, SalesJournalCol.CC
                        e.Item.Cells(I).Text = FormatNumber(dTotals(0, I), 0, True, False, True)
                    Case SalesJournalCol.CashVar
                        If bHasAuditPTM AndAlso IsNumeric(dTotals(0, I)) AndAlso CDbl(dTotals(0, I)) <> 0 Then
                            e.Item.Cells(I).Text = FormatNumber(dTotals(0, I), 2, True, False, True) & "*"
                        Else
                            e.Item.Cells(I).Text = FormatNumber(dTotals(0, I), 2, True, False, True)
                        End If
                    Case Else
                        e.Item.Cells(I).Text = FormatNumber(dTotals(0, I), 2, True, False, True)
                End Select
            Next
            e.Item.BackColor = System.Drawing.Color.Salmon
        End If
    End Sub

    Private Sub AddHeaderRow(Optional ByVal vnStoreID As Integer = 0)
        Try
            Dim oRow As New DataGridItem(1, 5, ListItemType.Header)

            oRow.Cells.Add(CreateCell("StoreID"))
            oRow.Cells.Add(CreateCell("Day"))
            oRow.Cells.Add(CreateCell("Net Sales"))
            oRow.Cells.Add(CreateCell("Coupons"))
            oRow.Cells.Add(CreateCell("Disc & Meals"))
            oRow.Cells.Add(CreateCell("Coup & Disc"))
            oRow.Cells.Add(CreateCell("Meals"))
            oRow.Cells.Add(CreateCell("GC Red."))
            oRow.Cells.Add(CreateCell("GC Sold"))
            oRow.Cells.Add(CreateCell("Promo"))
            oRow.Cells.Add(CreateCell("Sales Tax"))
            oRow.Cells.Add(CreateCell("Tax Exempt"))

            Dim tCalcTaxTable As New DataTable
            InvWSS.GetData("SELECT CalcTaxRate FROM tmStoreInvt WITH(NOLOCK) WHERE StoreID = " & vnStoreID, tCalcTaxTable)
            If tCalcTaxTable.Rows.Count > 0 AndAlso Not IsDBNull(tCalcTaxTable.Rows(0).Item(0)) Then
                oRow.Cells.Add(CreateCell("Calc Tax<br /><span style=""font-size: 6pt;"">(" & FormatPercent(tCalcTaxTable.Rows(0).Item(0)) & ")</span>"))
            Else
                oRow.Cells.Add(CreateCell("Calc Tax"))
            End If

            oRow.Cells.Add(CreateCell("Tax Diff."))
            oRow.Cells.Add(CreateCell("Paid Outs"))
            oRow.Cells.Add(CreateCell("Cash +/-"))
            oRow.Cells.Add(CreateCell("Cash<br />Deposit"))
            oRow.Cells.Add(CreateCell("CC<br />Deposit"))
            oRow.Cells.Add(CreateCell("PUW"))
            oRow.Cells.Add(CreateCell("Trans.<br />Count"))
            oRow.Cells.Add(CreateCell("Taxable"))
            oRow.Cells.Add(CreateCell("Non-Taxable"))
            oRow.Cells.Add(CreateCell("Net$ Exempt"))
            oRow.Cells.Add(CreateCell("Mobile Pay"))  'BDean: Bug#2350 - Grand Totals
            oRow.Cells.Add(CreateCell("Campus Cards"))  '15S/B-02293  Luke
            oRow.Cells.Add(CreateCell("Delivery"))  '15S/B-02293  Luke
            oRow.Cells(SalesJournalCol.StoreID).Visible = False '0
            If Not bCombineMeals Then
                oRow.Cells(SalesJournalCol.Coupons).Visible = False
                oRow.Cells(SalesJournalCol.DiscountsMeals).Visible = False
            Else
                oRow.Cells(SalesJournalCol.Discounts).Visible = False
                oRow.Cells(SalesJournalCol.Meals).Visible = False
            End If
 

            MyDataGrid.Controls(0).Controls.AddAt(RowY + 1, oRow)
            RowY += 1
        Catch ex As Exception
            lblStatus.Text = ex.ToString
            trStatus.Visible = True
        End Try
    End Sub

    Function CreateCell(ByVal sCellText As String, Optional ByVal nAlignment As Integer = HorizontalAlign.Center) As TableCell
        Dim tcell As New TableCell
        tcell.Text = sCellText
        tcell.HorizontalAlign = nAlignment
        tcell.BackColor = System.Drawing.Color.Beige
        Return tcell
    End Function

    Private Sub AddPageBreak()
        Dim oRow As New DataGridItem(1, 5, ListItemType.Item)
        Dim tcell As New TableCell
        tcell.ColumnSpan = nColSpan
        tcell.CssClass = "cellpagebreak"
        tcell.BackColor = Drawing.Color.Transparent
        oRow.Cells.Add(tcell)
        MyDataGrid.Controls(0).Controls.AddAt(RowY + 1, oRow)
        RowY += 1
    End Sub

    Private Sub AddTotalRow()
        Try
            Dim oRow As New DataGridItem(1, 5, ListItemType.Item)
            Dim tcell As TableCell

            'empty cell for StoreID
            tcell = New TableCell
            oRow.Cells.Add(tcell)
            tcell.Text = ""
            tcell.BackColor = System.Drawing.Color.Khaki
            tcell.HorizontalAlign = HorizontalAlign.Right

            'empty cell for Day
            tcell = New TableCell
            oRow.Cells.Add(tcell)
            tcell.Text = Session("rptoprange")
            tcell.BackColor = System.Drawing.Color.Khaki
            tcell.HorizontalAlign = HorizontalAlign.Left

            For I As Integer = MinColumn To MaxColumn
                Select Case I
                    Case SalesJournalCol.State, SalesJournalCol.AuditPMT, SalesJournalCol.Closed, SalesJournalCol.DepositCount, SalesJournalCol.DepositVerifiedCount
                        'Thses Columns are always hidden, skip them
                    Case Else
                        tcell = New TableCell
                        oRow.Cells.Add(tcell)

                        tcell.BackColor = System.Drawing.Color.Khaki
                        tcell.HorizontalAlign = HorizontalAlign.Right

                        Select Case I
                            Case SalesJournalCol.CheckCount
                                tcell.Text = FormatNumber(dTotals(1, I), 0, True, False, True)
                            Case SalesJournalCol.TaxDiff
                                If bIsOhio Then
                                    tcell.Text = "0.00"
                                Else
                                    tcell.Text = FormatNumber(dTotals(1, I), 2, True, False, True)
                                    If tcell.Text < 0 Then
                                        tcell.BackColor = System.Drawing.ColorTranslator.FromHtml("#F62217")
                                    End If
                                End If
                            Case SalesJournalCol.CashVar
                                If IsNumeric(dTotals(1, SalesJournalCol.AuditPMT)) AndAlso CDbl(dTotals(1, SalesJournalCol.AuditPMT)) <> 0 Then
                                    tcell.Text = tcell.Text & "*"
                                Else
                                    tcell.Text = FormatNumber(dTotals(1, I), 2, True, False, True)
                                End If
                            Case Else
                                tcell.Text = FormatNumber(dTotals(1, I), 2, True, False, True)
                        End Select
                End Select

                dTotals(1, I) = 0
            Next

            oRow.Cells(SalesJournalCol.StoreID).Visible = False
            If Not bCombineMeals Then
                oRow.Cells(SalesJournalCol.Coupons).Visible = False
                oRow.Cells(SalesJournalCol.DiscountsMeals).Visible = False
            Else
                oRow.Cells(SalesJournalCol.Discounts).Visible = False
                oRow.Cells(SalesJournalCol.Meals).Visible = False
            End If

            MyDataGrid.Controls(0).Controls.AddAt(RowY + 1, oRow)

            RowY += 1
        Catch ex As Exception

            lblStatus.Text = ex.ToString
            trStatus.Visible = True
        End Try
    End Sub

    Friend Sub export_excel(ByVal sender As Object, ByVal e As EventArgs)
        SetDateRange()
        SetSQL()
        IsExport = True

        BindData()
        GoExcel()
    End Sub

    Private Sub GoExcel()
        Me.EnableViewState = False

        Response.Clear()
        Response.ClearContent()
        Response.ClearHeaders()
        Response.Cookies.Clear()
        Response.Cache.SetCacheability(HttpCacheability.Private)
        Response.CacheControl = "private"
        Response.ContentEncoding = System.Text.UTF8Encoding.UTF8
        Response.AddHeader("content-disposition", "attachment;filename=SalesJournal.xls")
        Response.ContentType = "application/vnd.xls"
        Response.Charset = System.Text.UTF8Encoding.UTF8.WebName
        Response.AppendHeader("Pragma", "cache")
        Response.AppendHeader("Expires", "60")
        Dim sHTMLLegend As String = "<table border=""0"" cellspacing=""2"" cellpadding=""0"" style=""font-size:9pt;"">"
        sHTMLLegend &= "<tr>"
        sHTMLLegend &= "<td style=""padding:3px 0px 3px 25px;background-color:white;border-style:none;text-align:left;"">Legend&nbsp;</td>"
        sHTMLLegend &= "<td style=""padding:3px 0px 3px 25px;background-color:#99FF99;border-style:solid;border-width:thin;border-color:black;text-align:left;"">Deposits Verified&nbsp;&nbsp;</td>"
        sHTMLLegend &= "<td style=""padding:3px 5px 3px 25px;background-color:#FF9999;border-style:solid;border-width:thin;border-color:black;text-align:left;"">Deposits Not Verified&nbsp;&nbsp;</td>"
        sHTMLLegend &= "</tr>"
        sHTMLLegend &= "</table>"

        Dim stringWrite As New System.IO.StringWriter()
        Dim htmlWrite As New HtmlTextWriter(stringWrite)
        htmlWrite.WriteLine("<table><tr><td align=""center"" style=""font-size:12pt;""><b>" & sTitle & "</b></td></tr>")
        htmlWrite.WriteLine("<tr><td align=""center"">" & sHTMLLegend & "</td></tr>")
        htmlWrite.WriteLine("<tr><td>")
        MyDataGrid.RenderControl(htmlWrite)
        htmlWrite.WriteLine("</td></tr></table>")
        Response.Write(stringWrite.ToString())
        Response.End()
    End Sub


    Public Sub ddlRegion_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs)

        lblDistrict.Visible = False
        lblStore.Visible = False

        PopDistricts()
        PopStores()

    End Sub

    Public Sub ddlDistrict_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs)

        lblStore.Visible = False

        PopStores()

    End Sub

    Public Sub ddlStore_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs)

        lblStore.Visible = False

    End Sub


    Public Sub btnSubmit_Click(ByVal sender As Object, ByVal e As EventArgs)

        Dim bStartDate As Boolean = IsDate(txtStartDate.Text)
        Dim bEndDate As Boolean = IsDate(txtEndDate.Text)
        Dim bStoresChecked As Boolean = False

        'Loop through the Store list...
        For Each li As System.Web.UI.WebControls.ListItem In cblStore.Items

            'If a store is selected --> Update the boolean and exit the loop
            If li.Selected Then
                bStoresChecked = True
                Exit For
            End If

        Next

        'If no "Store" was selected --> Display a message
        'ElseIf no "Start Date" and "End Date" was selected --> Display a message
        'ElseIf no "Start Date" was selected --> Display a message
        'ElseIf no "End Date" was selected --> Display a message
        'ElseIf no "Daypart" was selected --> Display a message
        'Else --> Run the report
        If bStoresChecked = False Then

            With lblStore
                .Visible = True
                .Text = "Please select a store."
            End With

            tbShowOpts.Text = "True"

        ElseIf bStartDate = False AndAlso bEndDate = False Then

            With lblDateRange
                .Visible = True
                .Text = "The selected dates are invalid."
            End With

            tbShowOpts.Text = "True"

        ElseIf bStartDate = False Then

            With lblDateRange
                .Visible = True
                .Text = "The ""Start"" date is invalid."
            End With

            tbShowOpts.Text = "True"

        ElseIf bEndDate = False Then

            With lblDateRange
                .Visible = True
                .Text = "The ""End"" date is invalid."
            End With

            tbShowOpts.Text = "True"

        Else

            With lblSubHeader
                .Visible = True
                .Text = txtStartDate.Text & " - " & txtEndDate.Text
            End With

            SetDateRange()
            SetSQL()
            BindData()

            lblDateRange.Visible = False
            lblStore.Visible = False
            tbShowOpts.Text = ""

        End If

    End Sub

    Sub PopRegions()

        ddlRegion.Items.Clear()

        Dim tabRegions As DataTable
        Dim li As System.Web.UI.WebControls.ListItem

        tabRegions = InvWSS.GetPermittedRegions()

        'If only one row is returned --> Hide the <asp:DropDownList>
        'Else --> Add an "All Regions" option
        Select Case tabRegions.Rows.Count

            Case 1

                fsRegion.Visible = False

            Case Else

                li = New System.Web.UI.WebControls.ListItem("All Regions")
                li.Value = -1
                ddlRegion.Items.Add(li)

        End Select

        For Each row As DataRow In tabRegions.Rows

            li = New System.Web.UI.WebControls.ListItem(row("Region"))
            li.Value = row("RegionNum")
            ddlRegion.Items.Add(li)

        Next

        tabRegions.Dispose()
        li = Nothing

    End Sub

    Sub PopDistricts()

        ddlDistrict.Items.Clear()

        Dim tabDistricts As New DataTable
        Dim li As System.Web.UI.WebControls.ListItem

        If Not ddlRegion.SelectedItem Is Nothing Then

            tabDistricts = InvWSS.GetPermittedDistricts(ddlRegion.SelectedItem.Value)

            'If no rows are returned --> Display a message to the user
            'ElseIf one row is returned --> Hide the <asp:DropDownList>
            'Else --> Add an "All Districts" option
            Select Case tabDistricts.Rows.Count

                Case 0

                    lblDistrict.Text = "No Districts found in the selected Region."
                    lblDistrict.Visible = True

                    tbShowOpts.Text = "True"

                Case 1

                    fsDistrict.Visible = False

                Case Else

                    li = New System.Web.UI.WebControls.ListItem("All Districts")
                    li.Value = -1
                    ddlDistrict.Items.Add(li)

            End Select

            For Each row As DataRow In tabDistricts.Rows

                li = New System.Web.UI.WebControls.ListItem(row("District"))
                li.Value = row("DistrictNum")
                ddlDistrict.Items.Add(li)

            Next

        End If

        tabDistricts.Dispose()
        li = Nothing

    End Sub

    Sub PopStores()

        fsStore.Visible = True
        cblStore.Items.Clear()

        Dim sbStores As New StringBuilder
        Dim tabStores As New DataTable
        Dim li As System.Web.UI.WebControls.ListItem

        With sbStores

            .AppendLine("SELECT")
            .AppendLine("    [StoreID]")
            .AppendLine("	,[StoreNum]")
            .AppendLine("	,[StoreDescription]")
            .AppendLine("FROM")
            .AppendLine("	[dbo].[tmStore] WITH(NOLOCK)")
            .AppendLine("WHERE")
            .AppendLine("	[StoreID] IN")
            .AppendLine("	(")
            .AppendLine("	    SELECT")
            .AppendLine("			tmS.[StoreID]")
            .AppendLine("		FROM")
            .AppendLine("	        [dbo].[WEBRPT_vjBinPermissions] AS [vjBP] WITH(NOLOCK)")
            .AppendLine("		    INNER JOIN [dbo].[tmStore] AS [tmS] WITH(NOLOCK)")
            .AppendLine("			    ON vjBP.[UniversalStoreIdentifier] = tmS.[UniversalStoreIdentifier]")
            .AppendLine("		    INNER JOIN [dbo].[tmNode] AS [tmN] WITH(NOLOCK)")
            .AppendLine("			    ON tmS.[UniversalNodeIdentifier] = tmN.[UniversalNodeIdentifier]")
            .AppendLine("		WHERE")
            .AppendLine("			vjBP.[BinLevel] = 1")
            .AppendLine("			AND tmN.[ActiveNode] <> 0")
            .AppendLine("			AND vjBP.[UniversalAccountIdentifier] = '" & Session("AccountID") & "'")

            'If a "Region" is selected --> Append the WHERE clause
            If ddlRegion.SelectedValue <> "-1" Then
                .AppendLine("			AND vjBP.[UniversalRegionIdentifier] = ( SELECT [UniversalRegionIdentifier] FROM [dbo].[tmRegion] WITH(NOLOCK) WHERE [RegionNum] = " & ddlRegion.SelectedValue & " )")
            End If

            'If a "District" is selected --> Append the WHERE clause
            If ddlDistrict.SelectedValue <> "-1" AndAlso ddlDistrict.SelectedValue <> "" Then
                .AppendLine("			AND vjBP.[UniversalDistrictIdentifier] = ( SELECT [UniversalDistrictIdentifier] FROM [dbo].[tmDistrict] WITH(NOLOCK) WHERE [DistrictNum] = " & ddlDistrict.SelectedValue & " )")
            End If

            .AppendLine("   )")
            .AppendLine("ORDER BY")
            .AppendLine("	[StoreNum]")

        End With

        InvWSS.GetData(sbStores.ToString, tabStores)

        If Not IsDBNull(tabStores) AndAlso Not tabStores Is Nothing Then

            'If no rows are returned --> Display a message to the user
            'Else --> Populate the store list
            If tabStores.Rows.Count = 0 Then

                lblStore.Text = "You do not have access to any stores in the selected Region/District."
                lblStore.Visible = True

                tbShowOpts.Text = "True"

            Else

                For Each row As DataRow In tabStores.Rows

                    li = New System.Web.UI.WebControls.ListItem(row("StoreDescription"))
                    li.Value = row("StoreID")
                    cblStore.Items.Add(li)

                Next

                tabStores.Dispose()
                li = Nothing

            End If

        Else

            lblStore.Text = "Error - Store list returned Null/Nothing"
            lblStore.Visible = True

            tbShowOpts.Text = "True"

        End If

    End Sub

    Public Sub RangeChanged(ByVal sender As Object, ByVal e As EventArgs)
        fsSingleDate.Visible = False
        fsStartDate.Visible = False
        fsEndDate.Visible = False
        fsWeek.Visible = False
        fsPeriod.Visible = False
        fsMonth.Visible = False

        Select Case ddRange.SelectedValue

            Case "optDaily"
                fsSingleDate.Visible = True

            Case "optWeekly"
                fsWeek.Visible = True

            Case "optPeriod"
                fsPeriod.Visible = True

            Case "optMonthly"
                fsMonth.Visible = True

            Case "optRange"
                fsStartDate.Visible = True
                fsEndDate.Visible = True
        End Select

    End Sub

    Protected Sub txtSingleDate_TextChanged(sender As Object, e As EventArgs)
        SetDateRange()
    End Sub

    Protected Sub txtStartDate_TextChanged(sender As Object, e As EventArgs)
        SetDateRange()
    End Sub

    Protected Sub txtEndDate_TextChanged(sender As Object, e As EventArgs)
        SetDateRange()
    End Sub

    Protected Sub WeekChanged(sender As Object, e As EventArgs)
        SetDateRange()
    End Sub

    Sub DayRender_Start(ByVal Sender As Object, ByVal e As DayRenderEventArgs)

        Dim cell As TableCell = e.Cell
        Dim day As CalendarDay = e.Day

        If day.Date > Now Then

            With cell.Controls
                .Clear()
                .Add(New LiteralControl("<span style=""font-family: verdana; font-size: 8pt;"">" & day.Date.Day.ToString() & "</span>"))
            End With

        End If

    End Sub

    Private Sub SetDateRange()
        Session("rptopRange") = ""
        Session("rptopDay") = ""
        Session("rptopYear") = ""
        Session("rptopPeriod") = ""
        Session("rptopWeek") = ""
        Session("rptopMonth") = ""
        Session("StartDate") = ""
        Session("EndDate") = ""

        'Dim SelStores As String = "0"
        'For Each li As System.Web.UI.WebControls.ListItem In cblStore.Items
        '    If li.Selected Then
        '        SelStores += If(SelStores.Length = 0, " ", ",") & li.Value
        '    End If
        'Next
        'Session("rptopStore") = SelStores
        Session("rptopStore") = ""
        Session("rptopRegion") = ddlRegion.SelectedValue
        Session("rptopRegionTxt") = ddlRegion.SelectedItem.Text
        Session("rptopDistrict") = ddlDistrict.SelectedValue
        Session("rptopDistrictTxt") = ddlDistrict.SelectedItem.Text
        Select Case ddRange.SelectedValue

            Case "optDaily"
                dStartDate = CDate(txtSingleDate.Text)
                dEndDate = CDate(txtSingleDate.Text)
                lblSubHeader.Text = "Day: " & txtSingleDate.Text
                Session("rptopRange") = "daily"
                Session("rptopday") = txtSingleDate.Text
            Case "optWeekly"
                Dim sWeeks As String() = Split(ddWeek.SelectedValue, "|")
                dStartDate = CDate(sWeeks(0))
                dEndDate = CDate(sWeeks(1))
                lblSubHeader.Text = "Week: " & ddWeek.SelectedValue.Replace("|", " - ")
                Session("rptopRange") = "weekly"
                Session("rptopYear") = InvWSS.Platform.SBODBScalarGet("SBOCore", "tmReportingPeriod", "ReportingYear", "ReportingStartDate = '" & dStartDate.ToShortDateString & "'")
                Session("rptopPeriod") = InvWSS.Platform.SBODBScalarGet("SBOCore", "tmReportingPeriod", "ReportingPeriod", "ReportingStartDate = '" & dStartDate.ToShortDateString & "'")
                Session("rptopWeek") = InvWSS.Platform.SBODBScalarGet("SBOCore", "tmReportingPeriod", "ReportingWeekNum", "ReportingStartDate = '" & dStartDate.ToShortDateString & "'")
            Case "optPeriod"
                Dim sPeriod As String() = Split(ddPeriod.SelectedValue, "|")
                dStartDate = CDate(sPeriod(0))
                dEndDate = CDate(sPeriod(1))
                lblSubHeader.Text = "Period: " & ddPeriod.SelectedItem.Text
                Session("rptopRange") = "period"
                Session("rptopYear") = InvWSS.Platform.SBODBScalarGet("SBOCore", "tmReportingPeriod", "ReportingYear", "ReportingStartDate = '" & dStartDate.ToShortDateString & "'")
                Session("rptopPeriod") = InvWSS.Platform.SBODBScalarGet("SBOCore", "tmReportingPeriod", "ReportingPeriod", "ReportingStartDate = '" & dStartDate.ToShortDateString & "'")
            Case "optMonthly"
                Dim sMonth As String() = Split(ddMonth.SelectedValue, "|")
                dStartDate = CDate(sMonth(1) & "/1/" & sMonth(0))
                dEndDate = DateAdd(DateInterval.Day, -1, DateAdd(DateInterval.Month, 1, dStartDate))
                lblSubHeader.Text = "Month: " & ddMonth.SelectedItem.Text
                Session("rptopRange") = "monthly"
                Session("rptopYear") = dStartDate.Year
                Session("rptopMonth") = MonthName(dStartDate.Month)
                Session("StartDate") = dStartDate.ToShortDateString
                Session("EndDate") = dEndDate.ToShortDateString
            Case "optRange"
                dStartDate = CDate(txtStartDate.Text)
                dEndDate = CDate(txtEndDate.Text)
                lblSubHeader.Text = "Date Range: " & dStartDate.ToShortDateString & " - " & dEndDate.ToShortDateString
                Session("rptopRange") = "range"
                Session("StartDate") = dStartDate.ToShortDateString
                Session("EndDate") = dEndDate.ToShortDateString
        End Select


    End Sub
    Sub PopPeriods()

        ddPeriod.Items.Clear()

        Dim sbSQL As New StringBuilder
        Dim tabPeriods As New DataTable

        With sbSQL

            .AppendLine("SELECT")
            .AppendLine("   MIN(ReportingStartDate) AS ReportingStartDate")
            .AppendLine("  ,MAX(ReportingEndDate) AS ReportingEndDate")
            .AppendLine("  ,MAX(ReportingYear) AS ReportingYear")
            .AppendLine("  ,MAX(ReportingPeriod) AS ReportingPeriod")
            .AppendLine("FROM")
            .AppendLine("  tmReportingPeriod WITH(NOLOCK)")
            .AppendLine("WHERE")
            .AppendLine("  ReportingEndDate >= DATEADD(day,-365,GETDATE()) AND ReportingStartDate <= ")
            .AppendLine(" (SELECT MAX(reportingStartDate) FROM tmReportingPeriod WHERE ReportingPeriod = ")
            .AppendLine("  (SELECT ReportingPeriod FROM tmReportingPeriod WHERE GETDATE() BETWEEN ReportingStartDate AND ReportingEndDate)) ")
            .AppendLine("GROUP BY")
            .AppendLine("  ReportingYear, ReportingPeriod ")
            .AppendLine("ORDER BY ")
            .AppendLine("  ReportingStartDate")

        End With

        InvWSS.GetData(sbSQL.ToString, tabPeriods)

        If Not IsNothing(tabPeriods) AndAlso tabPeriods.Rows.Count > 0 Then

            For Each drPeriod As DataRow In tabPeriods.Rows
                Dim litem = New System.Web.UI.WebControls.ListItem(Format(drPeriod("ReportingStartDate"), "MM/dd/yyyy") & " - " & Format(drPeriod("ReportingEndDate"), "MM/dd/yyyy"))
                litem.Value = drPeriod("ReportingStartDate") & "|" & drPeriod("ReportingEndDate")
                ddPeriod.Items.Add(litem)
            Next

            tabPeriods.Dispose()

        End If

        If ddPeriod.Items.Count Then
            ddPeriod.SelectedIndex = ddPeriod.Items.Count - 1
        End If

    End Sub

    Sub PopMonths()

        'Clear existing items from the dropdown list
        ddMonth.Items.Clear()

        'Build a list of 12 months..
        For y As Integer = Now.Year - 1 To Now.Year

            For I As Integer = 1 To 12
                'Create a list item for each month

                Dim MonthlyItem = New System.Web.UI.WebControls.ListItem(y.ToString & " " & MonthName(I), y.ToString & "|" & I.ToString)
                Dim MonthDate As Date = CDate(I.ToString & "/1/" & y.ToString)

                If MonthDate <= Now Then
                    ddMonth.Items.Add(MonthlyItem)
                End If

            Next

        Next

        If ddMonth.Items.Count Then
            ddMonth.SelectedIndex = ddMonth.Items.Count - 1
        End If

    End Sub

    Sub PopWeeks()
        Dim sSQL As New StringBuilder  'Dim sSQL As String
        Dim tTable As New DataTable
        Dim row As DataRow
        Dim litem As System.Web.UI.WebControls.ListItem

        'BDean & Jason - Bug#2178: Updated the SQL statement in order to return the Reporting Weeks based on the year set by the current Reporting Period, as opposed to the current calendar date
        sSQL.AppendLine("SELECT")
        sSQL.AppendLine("	 tmRP.[ReportingWeekNum]")
        sSQL.AppendLine("	,tmRP.[ReportingPeriodID]")
        sSQL.AppendLine("	,tmRP.[ReportingStartDate]")
        sSQL.AppendLine("	,tmRP.[ReportingEndDate]")
        sSQL.AppendLine(" FROM [dbo].[tmReportingPeriod] AS [tmRP] WITH(NOLOCK) ")
        sSQL.AppendLine(" WHERE tmRP.[ReportingStartDate] >= DATEADD(day,-365,GETDATE()) ")
        sSQL.AppendLine(" AND tmRP.[ReportingStartDate] <= DateAdd(dd,7,GETDATE())")


        InvWSS.GetData(sSQL.ToString, tTable)
        ddWeek.Items.Clear()
        For Each row In tTable.Rows
            litem = New System.Web.UI.WebControls.ListItem(Format(row("ReportingStartDate"), "MM/dd/yyyy") & " - " & Format(row("ReportingEndDate"), "MM/dd/yyyy"))
            litem.Value = row("ReportingStartDate") & "|" & row("ReportingEndDate")
            ddWeek.Items.Add(litem)
        Next
        If ddWeek.Items.Count Then
            ddWeek.SelectedIndex = ddWeek.Items.Count - 1
        End If
    End Sub

End Class
