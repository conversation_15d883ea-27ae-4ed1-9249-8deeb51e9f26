Imports System.IO
Imports System.Net.Http
Imports System.Text
Imports System.Configuration
Imports System.Web.Script.Serialization

Namespace WebServicesAddons

    Public Module SendGridEmailService
        
        Private ReadOnly Property ApiKey As String
            Get
                Return ConfigurationManager.AppSettings("SendGrid-ApiKey")
            End Get
        End Property
        
        Private ReadOnly Property FromEmail As String
            Get
                Return ConfigurationManager.AppSettings("SendGrid-FromEmail")
            End Get
        End Property
        
        Private ReadOnly Property FromName As String
            Get
                Return ConfigurationManager.AppSettings("SendGrid-FromName")
            End Get
        End Property

        ''' <summary>
        ''' Send email using SendGrid API
        ''' </summary>
        ''' <param name="vsRecipient">Recipient email address(es), separated by semicolon</param>
        ''' <param name="vsSubject">Email subject</param>
        ''' <param name="vsBody">Email body content</param>
        ''' <param name="vbSendAsHTML">Whether to send as HTML (default: True)</param>
        ''' <param name="sSender">Sender email address (optional, uses config default)</param>
        ''' <returns>True if successful, False otherwise</returns>
        Public Function SendMail(ByVal vsRecipient As String, ByVal vsSubject As String, ByVal vsBody As String, Optional ByVal vbSendAsHTML As Boolean = True, Optional ByVal sSender As String = "") As Boolean
            Try
                If String.IsNullOrEmpty(ApiKey) Then
                    Throw New InvalidOperationException("SendGrid API key not configured")
                End If

                If String.IsNullOrEmpty(vsRecipient) Then
                    Throw New ArgumentException("Recipient email address is required")
                End If

                ' Use configured sender if not provided
                If String.IsNullOrEmpty(sSender) Then
                    sSender = FromEmail
                End If

                ' Parse recipients
                Dim recipients As String() = vsRecipient.Split(";"c)
                Dim toList As New List(Of Object)

                For Each recipient As String In recipients
                    Dim trimmedRecipient As String = recipient.Trim()
                    If Not String.IsNullOrEmpty(trimmedRecipient) Then
                        toList.Add(New With {.email = trimmedRecipient})
                    End If
                Next

                If toList.Count = 0 Then
                    Throw New ArgumentException("No valid recipient email addresses found")
                End If

                ' Create SendGrid email object
                Dim emailData = New With {
                    .personalizations = New Object() {
                        New With {
                            .to = toList.ToArray()
                        }
                    },
                    .from = New With {
                        .email = sSender,
                        .name = FromName
                    },
                    .subject = vsSubject,
                    .content = New Object() {
                        New With {
                            .type = If(vbSendAsHTML, "text/html", "text/plain"),
                            .value = vsBody
                        }
                    }
                }

                ' Serialize to JSON
                Dim serializer As New JavaScriptSerializer()
                Dim jsonContent As String = serializer.Serialize(emailData)

                ' Send via SendGrid API
                Return SendEmailViaSendGrid(jsonContent)

            Catch ex As Exception
                ' Log error (you might want to implement proper logging)
                System.Diagnostics.Debug.WriteLine($"SendGrid email error: {ex.Message}")
                Return False
            End Try
        End Function

        ''' <summary>
        ''' Send email with file attachment using SendGrid API
        ''' </summary>
        ''' <param name="vsRecipient">Recipient email address(es)</param>
        ''' <param name="vsSubject">Email subject</param>
        ''' <param name="vsBody">Email body content</param>
        ''' <param name="vfsAttachmentStream">File stream for attachment</param>
        ''' <param name="sAttachmentName">Name for the attachment</param>
        ''' <param name="vbSendAsHTML">Whether to send as HTML</param>
        ''' <param name="sSender">Sender email address</param>
        ''' <returns>True if successful, False otherwise</returns>
        Public Function SendMail(ByVal vsRecipient As String, ByVal vsSubject As String, ByVal vsBody As String, ByVal vfsAttachmentStream As FileStream, Optional ByVal sAttachmentName As String = "", Optional ByVal vbSendAsHTML As Boolean = True, Optional ByVal sSender As String = "") As Boolean
            Try
                If String.IsNullOrEmpty(ApiKey) Then
                    Throw New InvalidOperationException("SendGrid API key not configured")
                End If

                If String.IsNullOrEmpty(vsRecipient) Then
                    Throw New ArgumentException("Recipient email address is required")
                End If

                ' Use configured sender if not provided
                If String.IsNullOrEmpty(sSender) Then
                    sSender = FromEmail
                End If

                ' Parse recipients
                Dim recipients As String() = vsRecipient.Split(";"c)
                Dim toList As New List(Of Object)

                For Each recipient As String In recipients
                    Dim trimmedRecipient As String = recipient.Trim()
                    If Not String.IsNullOrEmpty(trimmedRecipient) Then
                        toList.Add(New With {.email = trimmedRecipient})
                    End If
                Next

                If toList.Count = 0 Then
                    Throw New ArgumentException("No valid recipient email addresses found")
                End If

                ' Prepare attachment if provided
                Dim attachments As New List(Of Object)
                If vfsAttachmentStream IsNot Nothing AndAlso vfsAttachmentStream.Length > 0 Then
                    ' Read file content and encode as base64
                    Dim fileBytes(vfsAttachmentStream.Length - 1) As Byte
                    vfsAttachmentStream.Read(fileBytes, 0, fileBytes.Length)
                    Dim base64Content As String = Convert.ToBase64String(fileBytes)

                    ' Get filename from stream or use provided name
                    If String.IsNullOrEmpty(sAttachmentName) AndAlso Not String.IsNullOrEmpty(vfsAttachmentStream.Name) Then
                        Dim uri As New Uri(vfsAttachmentStream.Name)
                        sAttachmentName = uri.Segments.Last()
                    End If

                    If String.IsNullOrEmpty(sAttachmentName) Then
                        sAttachmentName = "attachment.pdf"
                    End If

                    attachments.Add(New With {
                        .content = base64Content,
                        .filename = sAttachmentName,
                        .type = "application/pdf",
                        .disposition = "attachment"
                    })
                End If

                ' Create SendGrid email object with attachment
                Dim emailData = New With {
                    .personalizations = New Object() {
                        New With {
                            .to = toList.ToArray()
                        }
                    },
                    .from = New With {
                        .email = sSender,
                        .name = FromName
                    },
                    .subject = vsSubject,
                    .content = New Object() {
                        New With {
                            .type = If(vbSendAsHTML, "text/html", "text/plain"),
                            .value = vsBody
                        }
                    },
                    .attachments = attachments.ToArray()
                }

                ' Serialize to JSON
                Dim serializer As New JavaScriptSerializer()
                Dim jsonContent As String = serializer.Serialize(emailData)

                ' Send via SendGrid API
                Return SendEmailViaSendGrid(jsonContent)

            Catch ex As Exception
                ' Log error (you might want to implement proper logging)
                System.Diagnostics.Debug.WriteLine($"SendGrid email error: {ex.Message}")
                Return False
            End Try
        End Function

        ''' <summary>
        ''' Send email with file attachment using file path
        ''' </summary>
        Public Function SendMail(ByVal vsRecipient As String, ByVal vsSubject As String, ByVal vsBody As String, ByVal vsAttachmentPath As String, Optional ByVal vbSendAsHTML As Boolean = True, Optional ByVal sSender As String = "") As Boolean
            If File.Exists(vsAttachmentPath) Then
                Using fsFile As FileStream = File.OpenRead(vsAttachmentPath)
                    Dim fileName As String = Path.GetFileName(vsAttachmentPath)
                    Return SendMail(vsRecipient, vsSubject, vsBody, fsFile, fileName, vbSendAsHTML, sSender)
                End Using
            Else
                Return SendMail(vsRecipient, vsSubject, vsBody, vbSendAsHTML, sSender)
            End If
        End Function

        ''' <summary>
        ''' Internal method to send email via SendGrid API
        ''' </summary>
        Private Function SendEmailViaSendGrid(jsonContent As String) As Boolean
            Try
                Using client As New HttpClient()
                    client.DefaultRequestHeaders.Add("Authorization", $"Bearer {ApiKey}")
                    client.DefaultRequestHeaders.Add("User-Agent", "SBOnet/1.0")

                    Dim content As New StringContent(jsonContent, Encoding.UTF8, "application/json")
                    Dim response = client.PostAsync("https://api.sendgrid.com/v3/mail/send", content).Result

                    Return response.IsSuccessStatusCode
                End Using
            Catch ex As Exception
                System.Diagnostics.Debug.WriteLine($"SendGrid API error: {ex.Message}")
                Return False
            End Try
        End Function

    End Module

End Namespace
