﻿Option Explicit On

Imports System.Data
Imports System.IO
Imports System.Net
Imports OfficeOpenXml
Imports iTextSharp.text
Imports iTextSharp.text.pdf

'2020-01-20 jjc Sprint 76 SBOD-1350/1383 Adding Pre-cut lettuce
'2020-04-24 jjc Sprint 79w17 SBOD-1857-1859 Adding Emergency DT Only
'2021-06-03 jjc Sprint 79w67 SBOD-2240/2241 Adding Guide And Prep label printer.  Changed to blanking values not applicable to the stores's selected guide.
'2021-11-11 jjc Sprint 79w68 SBOD-2240/2241 Adding Opeations Tablet

Public Class Scheduler_LaborMatrixOptionsReport
    Inherits System.Web.UI.Page
    Dim InvWSS As InventoryWebSiteServices.InventoryServices

    Dim serverPath As String

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        'If the user is not logged in --> Redirect to the login page
        If Len(Session("UserID")) = 0 Then
            Response.Redirect("login.aspx?timeout=1")
        End If

        InvWSS = Session("IWSS")

        If Not IsPostBack Then

            PopStores()

        End If

    End Sub

#Region "Populate Report Options"

    Private Sub PopStores()

        Try

            Dim sbSQL As New StringBuilder
            Dim tabStores As New DataTable

            With sbSQL

                .AppendLine("DECLARE @Username AS VARCHAR(25) = '" & GetUsername() & "'")
                .AppendLine("")
                .AppendLine("SELECT")
                .AppendLine("	 s.[Store_ID]")
                .AppendLine("	,s.[Store_Name]")
                .AppendLine("	,(")
                .AppendLine("		CASE ")
                .AppendLine("			WHEN s.[Store_ID] = u.[Default_Store] THEN 1")
                .AppendLine("			ELSE 0")
                .AppendLine("		END")
                .AppendLine("	 ) AS [Default_Store]")
                .AppendLine("FROM")
                .AppendLine("	{0}.[STORES] AS [s]")
                .AppendLine("	INNER JOIN {0}.[USERS_STORES] AS [us]")
                .AppendLine("		ON s.[Store_ID] = us.[Store_ID]")
                .AppendLine("	INNER JOIN {0}.[USERS] AS [u]")
                .AppendLine("		ON us.[User_ID] = u.[User_ID]")
                .AppendLine("WHERE")
                .AppendLine("	u.[UName] = @Username")
                .AppendLine("	AND s.[StoreActive] = 1")
                .AppendLine("ORDER BY")
                .AppendLine("   s.[Store_Name]")

            End With

            InvWSS.GetData(String.Format(sbSQL.ToString(),Session("DSS_Path")), tabStores)

            If Not IsDBNull(tabStores) AndAlso Not tabStores Is Nothing Then

                'If no rows are returned --> Display a message to the user
                'Else --> Populate the store list
                If tabStores.Rows.Count = 0 Then

                    lblStore.Text = "You do not have access to any stores."
                    lblStore.Visible = True

                Else

                    For Each row As DataRow In tabStores.Rows

                        Dim li As System.Web.UI.WebControls.ListItem = New System.Web.UI.WebControls.ListItem(row("Store_Name"))
                        li.Value = row("Store_ID")
                        cblStore.Items.Add(li)

                    Next

                    tabStores.Dispose()

                End If

            Else

                lblStore.Text = "Error - Store list returned Null/Nothing"
                lblStore.Visible = True

            End If

        Catch ex As Exception

            'Ignore errors...

        End Try

    End Sub

    Private Function GetUsername() As String

        Dim username As String = ""

        Try

            Dim sbSQL As New StringBuilder
            Dim tabUsername As New DataTable

            With sbSQL

                .AppendLine("SELECT")
                .AppendLine("	[UserID] AS [Username]")
                .AppendLine("FROM")
                .AppendLine("	[dbo].[tmAccount] WITH(NOLOCK)")
                .AppendLine("WHERE")
                .AppendLine("	[UniversalAccountIdentifier] = '" & Session("AccountID") & "'")

            End With

            InvWSS.GetData(sbSQL.ToString(), tabUsername)

            If tabUsername.Rows.Count > 0 Then
                username = tabUsername.Rows(0).Item("Username")
            End If

        Catch ex As Exception

            'Ignore errors...

        End Try

        Return username

    End Function

#End Region

#Region "Event Handlers"

    Public Sub btnSubmit_click(ByVal sender As Object, ByVal e As EventArgs)

        With lblStore
            .Text = ""
            .Visible = False
        End With

        If ValidateStoreSelection() Then
            BindGrid()
        Else

            With lblStore
                .Text = "Please select a store."
                .Visible = True
            End With

            tbShowOpts.Text = "True"

        End If

    End Sub

    Public Sub btnExcel_Click(ByVal sender As Object, ByVal e As EventArgs)

        BindGrid()
        GoEPPlus(gvLaborMatrixOptions)

    End Sub

#End Region

#Region "Data Validation"

    Private Function ValidateStoreSelection() As Boolean

        Dim bStoreSelected As Boolean = False

        For Each li As System.Web.UI.WebControls.ListItem In cblStore.Items

            If li.Selected Then
                bStoreSelected = True
                Exit For
            End If

        Next

        Return bStoreSelected

    End Function

#End Region

#Region "Populate Grid"

    Private Sub BindGrid()

        'Clear the grid's datasource
        With gvLaborMatrixOptions
            .DataSource = Nothing
            .DataBind()
        End With

        'Clear/Hide the grid label
        With lblGrid
            .Text = ""
            .Visible = False
        End With

        'Hide the "Excel" button
        btnExcel.Visible = False

        Try

            'Build a list of selected stores
            Dim sbSelectedStores As New StringBuilder

            With sbSelectedStores

                'Loop through the Store list...
                For Each li As System.Web.UI.WebControls.ListItem In cblStore.Items

                    'If a store is selected --> Append it
                    If li.Selected Then
                        .AppendLine("    " & If(.Length = 0, " ", ",") & "( '" & li.Value & "' )")
                    End If

                Next

            End With

            Dim sbSQL As New StringBuilder
            Dim tabResults As New DataTable

            With sbSQL

                .AppendLine("DECLARE @Stores TABLE")
                .AppendLine("(")
                .AppendLine("	[StoreID] VARCHAR(31)")
                .AppendLine(");")
                .AppendLine("")
                .AppendLine("INSERT INTO @Stores ( [StoreID] )")
                .AppendLine("VALUES")
                .AppendLine(sbSelectedStores.ToString())
                .AppendLine(";")
	            .AppendLine()
	            .AppendLine("DECLARE @EnumReference TABLE")
	            .AppendLine("	([Tag] VARCHAR(20)")
	            .AppendLine("	,[Column] VARCHAR(24)")
	            .AppendLine("	)")
	            .AppendLine()
	            .AppendLine()
	            .AppendLine("INSERT @EnumReference([Tag],[Column]) VALUES('CrewProductivity', 'CrewProductivityDP1')")
	            .AppendLine("INSERT @EnumReference([Tag],[Column]) VALUES('CrewProductivity', 'CrewProductivityDP2')")
	            .AppendLine("INSERT @EnumReference([Tag],[Column]) VALUES('CrewProductivity', 'CrewProductivityDP3')")
	            .AppendLine("INSERT @EnumReference([Tag],[Column]) VALUES('CrewProductivity', 'CrewProductivityDP4')")
	            .AppendLine("INSERT @EnumReference([Tag],[Column]) VALUES('CrewProductivity', 'CrewProductivityDP1_Wknd')")
	            .AppendLine("INSERT @EnumReference([Tag],[Column]) VALUES('CrewProductivity', 'CrewProductivityDP2_Wknd')")
	            .AppendLine("INSERT @EnumReference([Tag],[Column]) VALUES('CrewProductivity', 'CrewProductivityDP3_Wknd')")
	            .AppendLine("INSERT @EnumReference([Tag],[Column]) VALUES('CrewProductivity', 'CrewProductivityDP4_Wknd')")
                .AppendLine(";")
	            .AppendLine()
                .AppendLine("")
                .AppendLine("SELECT")
                .AppendLine("	 PVT.[StoreNum]")
                .AppendLine("	,PVT.[StoreDescription]")
                .AppendLine("	,PVT.[GuideID]")
                .AppendLine("	,PVT.[GuideDescription]")
                .AppendLine("	,PVT.[ExchangeRate]")
                .AppendLine("	,PVT.[CounterType]")
                .AppendLine("	,PVT.[StoreType]")
                .AppendLine("	,PVT.[KitchenType]")
                .AppendLine("	,PVT.[DiningRoomType]")
                .AppendLine("	,PVT.[DiningRoomFloorType]")
                .AppendLine("	,PVT.[DiningRoomDrinksType]")
                .AppendLine("	,PVT.[GrillType]")
                .AppendLine("	,PVT.[Powersoak]")
                .AppendLine("	,PVT.[CashScales]")
                .AppendLine("	,PVT.[GreaseExtraction]")
                .AppendLine("	,PVT.[WareWash]")
                .AppendLine("	,PVT.[ElectricSaladSpinner]")
                .AppendLine("	,PVT.[PreChopLettuce]")
                .AppendLine("	,CASE WHEN PVT.[GuideID] = 1")
                .AppendLine("		THEN PVT.[Kiosk]")
                .AppendLine("	END AS [Kiosk]")
                .AppendLine("	,CASE WHEN PVT.[GuideID] >= 10")
                .AppendLine("		THEN PVT.[DigitalType]")
                .AppendLine("	END AS [DigitalType]")
                .AppendLine("	,CASE WHEN PVT.[GuideID] >= 10")
                .AppendLine("		THEN PVT.[OrderTechnology]")
                .AppendLine("	END AS [OrderTechnology]")
                .AppendLine("	,CASE WHEN PVT.[GuideID] >= 10")
                .AppendLine("		THEN PVT.[DriveThruMix]")
                .AppendLine("	END AS [DriveThruMix]")
                .AppendLine("	,PVT.[BillValidationSafe]")
                .AppendLine("	,CASE WHEN PVT.[GuideID] = 1")
                .AppendLine("		THEN PVT.[BackOfficeForecastPrep]")
                .AppendLine("	END AS [BackOfficeForecastPrep]")
                .AppendLine("	,CASE WHEN PVT.[GuideID] = 1")
                .AppendLine("		THEN PVT.[BackOfficeInventory]")
                .AppendLine("	END AS [BackOfficeInventory]")
                .AppendLine("	,CASE WHEN PVT.[GuideID] = 1")
                .AppendLine("		THEN PVT.[BackOfficeLaborScheduler]")
                .AppendLine("	END AS [BackOfficeLaborScheduler]")
                .AppendLine("	,CASE WHEN PVT.[GuideID] >=2")
                .AppendLine("		THEN PVT.[PrepLabelPrinter]")
                .AppendLine("	END AS [PrepLabelPrinter]")
                .AppendLine("	,CASE WHEN PVT.[GuideID] >=3")
                .AppendLine("		THEN PVT.[OperationsTablet]")
                .AppendLine("	END AS [OperationsTablet]")
                .AppendLine("	,CASE WHEN PVT.[GuideID] >=10")
                .AppendLine("		THEN PVT.[LettuceTest]")
                .AppendLine("	END AS [LettuceTest]")
                .AppendLine("	,CASE WHEN PVT.[GuideID] >=10")
                .AppendLine("		THEN PVT.[AmbientBacon]")
                .AppendLine("	END AS [AmbientBacon]")
                .AppendLine("	,PVT.[CrewProductivityDP1]")
                .AppendLine("	,PVT.[CrewProductivityDP2]")
	            .AppendLine("	,PVT.[CrewProductivityDP3]")
	            .AppendLine("	,PVT.[CrewProductivityDP4]")
	            .AppendLine("	,PVT.[CrewProductivityDP1_Wknd]")
	            .AppendLine("	,PVT.[CrewProductivityDP2_Wknd]")
	            .AppendLine("	,PVT.[CrewProductivityDP3_Wknd]")
	            .AppendLine("	,PVT.[CrewProductivityDP4_Wknd]")
	            .AppendLine("	,PVT.[EmergencyDTOnly]")
                .AppendLine("FROM")
                .AppendLine("(")
                .AppendLine("	SELECT")
                .AppendLine("		 UPVT.[StoreNum]")
                .AppendLine("		,UPVT.[StoreDescription]")
                .AppendLine("		,UPVT.[ExchangeRate]")
                .AppendLine("		,UPVT.[GuideID]")
                .AppendLine("		,UPVT.[Powersoak]")
                .AppendLine("		,UPVT.[CashScales]")
                .AppendLine("		,UPVT.[GreaseExtraction]")
                .AppendLine("	    ,UPVT.[WareWash]")
                .AppendLine("	    ,UPVT.[ElectricSaladSpinner]")
                .AppendLine("		,UPVT.[BillValidationSafe]")
                .AppendLine("		,UPVT.[BackOfficeForecastPrep]")
                .AppendLine("		,UPVT.[BackOfficeInventory]")
                .AppendLine("		,UPVT.[BackOfficeLaborScheduler]")
                .AppendLine("		,UPVT.[PreChopLettuce]")
                .AppendLine("		,UPVT.[PrepLabelPrinter]")
                .AppendLine("		,UPVT.[OperationsTablet]")
                .AppendLine("		,UPVT.[LettuceTest]")
                .AppendLine("		,UPVT.[AmbientBacon]")
                .AppendLine("		,UPVT.[EmergencyDTOnly]")
                .AppendLine("		,UPVT.[ColumnName]")
                .AppendLine("		,Enum.[Value]")
                .AppendLine("		--,Ref.[Column]")
	            .AppendLine("		--,Ref.[Tag]")
	            .AppendLine("		--,ISNULL(Ref.[Tag], UPVT.[ColumnName]) AS [Tag]")
	            .AppendLine("	FROM")
                .AppendLine("	(")
                .AppendLine("		SELECT")
                .AppendLine("			 tmS.[StoreNum]")
                .AppendLine("			,tmS.[StoreDescription]")
                .AppendLine("			,tmSL.[GuideID]")
                .AppendLine("			,tmSL.[GuideID] AS [GuideDescription]")
                .AppendLine("			,tmSL.[ExchangeRate]")
                .AppendLine("			,tmSL.[CounterType]")
                .AppendLine("			,tmSL.[StoreType]")
                .AppendLine("			,tmSL.[KitchenType]")
                .AppendLine("			,tmSL.[DiningRoomType]")
                .AppendLine("			,tmSL.[DiningRoomFloorType]")
                .AppendLine("			,tmSL.[DiningRoomDrinksType]")
                .AppendLine("			,tmSL.[GrillType]")
                .AppendLine("			,tmSL.[Powersoak]")
                .AppendLine("			,tmSL.[CashScales]")
                .AppendLine("			,tmSL.[GreaseExtraction]")
                .AppendLine("	        ,tmSL.[WareWash]")
                .AppendLine("	        ,tmSL.[ElectricSaladSpinner]")
                .AppendLine("	        ,tmSL.[Kiosk]")
                .AppendLine("	        ,tmSL.[DigitalType]")
                .AppendLine("	        ,tmSL.[OrderTechnology]")
                .AppendLine("	        ,tmSL.[DriveThruMix]")
                .AppendLine("			,tmSL.[BillValidationSafe]")
                .AppendLine("			,tmSL.[BackOfficeForecastPrep]")
                .AppendLine("			,tmSL.[BackOfficeInventory]")
                .AppendLine("			,tmSL.[BackOfficeLaborScheduler]")
                .AppendLine("		    ,tmSL.[PreChopLettuce]")
                .AppendLine("		    ,tmSL.[PrepLabelPrinter]")
                .AppendLine("		    ,tmSL.[OperationsTablet]")
                .AppendLine("		    ,tmSL.[LettuceTest]")
                .AppendLine("		    ,tmSL.[AmbientBacon]")
                .AppendLine("			,tmSL.[CrewProductivityDP1]")
                .AppendLine("			,tmSL.[CrewProductivityDP2]")
                .AppendLine("			,tmSL.[CrewProductivityDP3]")
                .AppendLine("			,tmSL.[CrewProductivityDP4]")
                .AppendLine("			,tmSL.[CrewProductivityDP1_Wknd]")
                .AppendLine("			,tmSL.[CrewProductivityDP2_Wknd]")
                .AppendLine("			,tmSL.[CrewProductivityDP3_Wknd]")
                .AppendLine("			,tmSL.[CrewProductivityDP4_Wknd]")
                .AppendLine("			,tmSL.[EmergencyDTOnly]")
                .AppendLine("		FROM")
                .AppendLine("			[dbo].[tmStore] AS [tmS] WITH(NOLOCK)")
                .AppendLine("			INNER JOIN @Stores AS [s]")
                .AppendLine("				ON tmS.[DSS_Store_ID] = s.[StoreID]")
                .AppendLine("			LEFT OUTER JOIN [dbo].[tmStoreLabor] AS [tmSL] WITH(NOLOCK)")
                .AppendLine("				ON tmS.[StoreID] = tmSL.[StoreID]")
                .AppendLine("	) AS [RawData]")
                .AppendLine("	UNPIVOT")
                .AppendLine("	(")
                .AppendLine("		[Value]")
                .AppendLine("		FOR [ColumnName] IN ")
                .AppendLine("		(")
                .AppendLine("			 RawData.[GuideDescription]")
                .AppendLine("			,RawData.[CounterType]")
                .AppendLine("			,RawData.[StoreType]")
                .AppendLine("			,RawData.[KitchenType]")
                .AppendLine("			,RawData.[DiningRoomType]")
                .AppendLine("			,RawData.[DiningRoomFloorType]")
                .AppendLine("			,RawData.[DiningRoomDrinksType]")
                .AppendLine("			,RawData.[GrillType]")
                .AppendLine("			,RawData.[Kiosk]")
                .AppendLine("			,RawData.[DigitalType]")
                .AppendLine("			,RawData.[OrderTechnology]")
                .AppendLine("			,RawData.[DriveThruMix]")
                .AppendLine("			,RawData.[CrewProductivityDP1]")
	            .AppendLine("			,RawData.[CrewProductivityDP2]")
	            .AppendLine("			,RawData.[CrewProductivityDP3]")
	            .AppendLine("			,RawData.[CrewProductivityDP4]")
	            .AppendLine("			,RawData.[CrewProductivityDP1_Wknd]")
	            .AppendLine("			,RawData.[CrewProductivityDP2_Wknd]")
	            .AppendLine("			,RawData.[CrewProductivityDP3_Wknd]")
	            .AppendLine("			,RawData.[CrewProductivityDP4_Wknd]")
                .AppendLine("		)")
                .AppendLine("	) AS [UPVT]")
	            .AppendLine("	LEFT OUTER JOIN @EnumReference AS [Ref]")
	            .AppendLine("		ON UPVT.[ColumnName] = Ref.[Column]")
                .AppendLine("	INNER JOIN")
                .AppendLine("	(")
                .AppendLine("		SELECT")
                .AppendLine("			 tmE.[Tag]")
                .AppendLine("			,tdE.[Key]")
                .AppendLine("			,tdE.[Value]")
                .AppendLine("		FROM")
                .AppendLine("			[dbo].[tmEnum] AS [tmE] WITH(NOLOCK)")
                .AppendLine("			INNER JOIN [dbo].[tdEnum] AS [tdE] WITH(NOLOCK)")
                .AppendLine("				ON tmE.[Tag] = tdE.[Tag]")
                .AppendLine("")
                .AppendLine("		UNION")
                .AppendLine("")
                .AppendLine("		SELECT")
                .AppendLine("			 'GuideDescription' AS [Tag]")
                .AppendLine("			,[GuideID] AS [Key]")
                .AppendLine("			,[GuideDescription] AS [Value]")
                .AppendLine("		FROM [dbo].[Labor_tmGuideVersion]")
                .AppendLine("	) AS [Enum]")
	            .AppendLine("		ON ISNULL(Ref.[Tag], UPVT.[ColumnName]) = Enum.[Tag]")
                .AppendLine("			AND UPVT.[Value] = Enum.[Key]")
                .AppendLine(") AS [Data]")
                .AppendLine("PIVOT")
                .AppendLine("(")
                .AppendLine("	MAX( Data.[Value] )")
                .AppendLine("	FOR Data.[ColumnName] IN")
                .AppendLine("	(")
                .AppendLine("		 [GuideDescription]")
                .AppendLine("		,[CounterType]")
                .AppendLine("		,[StoreType]")
                .AppendLine("		,[KitchenType]")
                .AppendLine("		,[DiningRoomType]")
                .AppendLine("		,[DiningRoomFloorType]")
                .AppendLine("		,[DiningRoomDrinksType]")
                .AppendLine("		,[GrillType]")
                .AppendLine("		,[Kiosk]")
                .AppendLine("		,[DigitalType]")
                .AppendLine("		,[OrderTechnology]")
                .AppendLine("		,[DriveThruMix]")
                .AppendLine("		,[CrewProductivityDP1]")
	            .AppendLine("		,[CrewProductivityDP2]")
	            .AppendLine("		,[CrewProductivityDP3]")
	            .AppendLine("		,[CrewProductivityDP4]")
	            .AppendLine("		,[CrewProductivityDP1_Wknd]")
	            .AppendLine("		,[CrewProductivityDP2_Wknd]")
	            .AppendLine("		,[CrewProductivityDP3_Wknd]")
	            .AppendLine("		,[CrewProductivityDP4_Wknd]")
                .AppendLine("	)")
                .AppendLine(") AS [PVT]")
                .AppendLine("ORDER BY")
                .AppendLine("	PVT.[StoreNum];")

            End With

            InvWSS.GetData(sbSQL.ToString(), tabResults)

            If tabResults.Rows.Count > 0 Then

                With gvLaborMatrixOptions
                    .DataSource = tabResults
                    .DataBind()
                End With

                'Add the 2nd "Header Row" to the <asp:GridView>
                gvLaborMatrixOptions.Controls(0).Controls.AddAt(0, BuildGroupHeaderRow())

                'Show the "Excel" button if the grid is populated with data
                btnExcel.Visible = True

            Else

                'Display a message to the user
                With lblGrid
                    .Text = "No data found for the selected store(s)."
                    .Visible = True
                End With

            End If

        Catch ex As Exception

            'Display a message to the user
            With lblGrid
                .Text = "The following error occurred while trying to populate the grid: <br /><br />" & ex.ToString()
                .Visible = True
            End With

        End Try

    End Sub

        Friend Sub MyDataGrid_ItemCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs)

        Select Case e.Row.RowType
            Case DataControlRowType.DataRow
                For x As Integer = 0 To e.Row.Cells.Count - 1
                    Dim CheckBox As CheckBoxField = TryCast(TryCast(e.Row.Cells(x), System.Web.UI.WebControls.DataControlFieldCell).ContainingField, System.Web.UI.WebControls.CheckBoxField)
                    If not CheckBox Is nothing Then                                               
                        Dim DataField As String = CheckBox.DataField
                        If e.Row.DataItem(DataField) Is DBNull.Value
                            If  e.Row.Cells(x).Controls.Count > 0
                                e.Row.Cells(x).Controls.Removeat(0)
                            End If
                        End If
                    End If
                Next
        End Select
    End Sub


    Private Function BuildGroupHeaderRow() As GridViewRow

        'Create a 2nd "Header Row" with the "Group" information
        Dim row As GridViewRow = New GridViewRow(0, -1, DataControlRowType.Header, DataControlRowState.Normal)

        Dim blankSpacer As TableCell = New TableHeaderCell With {.ColumnSpan = 2, .BackColor = System.Drawing.Color.White, .BorderWidth = 0}
        Dim restaurantInformation As TableCell = New TableHeaderCell With {.ColumnSpan = 11, .Text = "Restaurant Information", .CssClass = "group-header"}
        Dim productivityEnhancements As TableCell = New TableHeaderCell With {.ColumnSpan = 12, .Text = "Productivity Enhancements", .CssClass = "group-header"}
        Dim crewProductivity As TableCell = New TableHeaderCell With {.ColumnSpan = 8, .Text = "Crew Productivity", .CssClass = "group-header"}

        With row.Cells
            .Add(blankSpacer)
            .Add(restaurantInformation)
            .Add(productivityEnhancements)
            .Add(crewProductivity)
        End With

        Return row

    End Function

#End Region

#Region "Excel Export"

    Private Sub GoEPPlus(ByRef gv As GridView)

        Me.EnableViewState = False

        Dim ExcelMemoryStream As New MemoryStream()
        Dim oExcelPackagePlus As New ExcelPackage(ExcelMemoryStream)

        Dim ws As ExcelWorksheet = oExcelPackagePlus.Workbook.Worksheets.Add("Sheet1")

        Dim wsCurrentRow As Integer = 2

        Dim wsStartCol As Integer = 1
        Dim wsEndCol As Integer = gv.Columns.Count

        'Insert the report title
        With ws.Cells(wsCurrentRow, wsStartCol, wsCurrentRow, wsEndCol)

            .Merge = True
            .Value = "Labor Matrix Options Report"
            .Style.Font.Size = 18
            .Style.HorizontalAlignment = Style.ExcelHorizontalAlignment.Center

        End With

        wsCurrentRow += 2

        'Insert the group headers
        With ws.Cells(wsCurrentRow, 4, wsCurrentRow, 10)

            .Merge = True
            .Value = "Restaurant Information"
            .Style.HorizontalAlignment = Style.ExcelHorizontalAlignment.Center
            .Style.Fill.PatternType = Style.ExcelFillStyle.Solid
            .Style.Fill.BackgroundColor.SetColor(System.Drawing.ColorTranslator.FromHtml("#cccccc"))
            .Style.Font.Bold = True
            .Style.Border.BorderAround(Style.ExcelBorderStyle.Thin, System.Drawing.Color.Black)

        End With

        With ws.Cells(wsCurrentRow, 11, wsCurrentRow, 17)

            .Merge = True
            .Value = "Productivity Enhancements"
            .Style.HorizontalAlignment = Style.ExcelHorizontalAlignment.Center
            .Style.Fill.PatternType = Style.ExcelFillStyle.Solid
            .Style.Fill.BackgroundColor.SetColor(System.Drawing.ColorTranslator.FromHtml("#cccccc"))
            .Style.Font.Bold = True
            .Style.Border.BorderAround(Style.ExcelBorderStyle.Thin, System.Drawing.Color.Black)

        End With

        With ws.Cells(wsCurrentRow, 18, wsCurrentRow, 21)

            .Merge = True
            .Value = "Crew Productivity"
            .Style.HorizontalAlignment = Style.ExcelHorizontalAlignment.Center
            .Style.Fill.PatternType = Style.ExcelFillStyle.Solid
            .Style.Fill.BackgroundColor.SetColor(System.Drawing.ColorTranslator.FromHtml("#cccccc"))
            .Style.Font.Bold = True
            .Style.Border.BorderAround(Style.ExcelBorderStyle.Thin, System.Drawing.Color.Black)

        End With

        wsCurrentRow += 1

        'Insert the grid header
        Dim gvHeaderRow As GridViewRow = gv.HeaderRow
        Dim gvHeaderCellCount As Integer = gvHeaderRow.Cells.Count - 1

        For gvCellIndex As Integer = 0 To gvHeaderCellCount

            Dim gvCell As TableCell = gvHeaderRow.Cells(gvCellIndex)

            With ws.Cells(wsCurrentRow, gvCellIndex + 1)

                .Value = gvCell.Text.Replace("&amp;", "&")
                .Style.HorizontalAlignment = Style.ExcelHorizontalAlignment.Center
                .Style.Fill.PatternType = Style.ExcelFillStyle.Solid
                .Style.Fill.BackgroundColor.SetColor(System.Drawing.ColorTranslator.FromHtml("#cccccc"))
                .Style.Font.Bold = True
                .Style.Border.BorderAround(Style.ExcelBorderStyle.Thin, System.Drawing.Color.Black)

            End With

        Next

        wsCurrentRow += 1

        'Insert the grid data
        Dim gvRowCount As Integer = gv.Rows.Count - 1

        For gvRowIndex As Integer = 0 To gvRowCount

            Dim gvDataRow As GridViewRow = gv.Rows(gvRowIndex)
            Dim gvDataCellCount As Integer = gvDataRow.Cells.Count - 1

            For gvCellIndex As Integer = 0 To gvDataCellCount

                Dim gvCell As TableCell = gvDataRow.Cells(gvCellIndex)

                With ws.Cells(wsCurrentRow, gvCellIndex + 1)

                    If gvCell.HasControls Then

                        Dim chk As CheckBox = TryCast(gvCell.Controls(0), CheckBox)

                        If chk.Checked Then
                            .Value = "x"
                        Else
                            .Value = ""
                        End If

                    Else
                        .Value = gvCell.Text
                    End If

                    .Style.HorizontalAlignment = If(gvCellIndex <= 9, Style.ExcelHorizontalAlignment.Left, Style.ExcelHorizontalAlignment.Center)
                    .Style.Fill.PatternType = Style.ExcelFillStyle.Solid
                    .Style.Fill.BackgroundColor.SetColor(If(wsCurrentRow Mod 2 = 0, System.Drawing.ColorTranslator.FromHtml("#eeeeee"), System.Drawing.ColorTranslator.FromHtml("#ffffff")))
                    .Style.Border.BorderAround(Style.ExcelBorderStyle.Thin, System.Drawing.Color.Black)

                End With

            Next

            wsCurrentRow += 1

        Next

        'Misc formatting
        With ws

            .View.ShowGridLines = False

            For i As Integer = 4 To wsCurrentRow - 1
                .Row(i).Height = 20
            Next

            .Cells(.Dimension.Address).Style.VerticalAlignment = Style.ExcelVerticalAlignment.Center
            .Cells(.Dimension.Address).AutoFitColumns()

        End With

        'Save the workbook
        oExcelPackagePlus.Save()

        With Response

            .Clear()
            .ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            .AddHeader("content-disposition", "attachment;filename=LaborMatrixOptions.xlsx")
            .BinaryWrite(ExcelMemoryStream.ToArray())
            .End()

        End With

    End Sub

#End Region

End Class