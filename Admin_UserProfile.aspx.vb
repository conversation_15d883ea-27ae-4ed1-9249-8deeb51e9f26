Option Explicit On

Imports System.Data.SqlClient
Imports System.Net
Imports System.Net.Mail
Imports AmazonUtilities.Mail.SimpleEmailService
Imports WebServicesAddons.SendGridEmailService

'2017-06-08 jjc Sprint 35 // D-001239 // TK-03565 Inform the user that there is a currently valid reset request pending
'2017-06-13 jjc Sprint 35 // D-001239 // TK-03565 Change Message color to Red.
'2021-06-07 jjc Sprint79w67 SBOD-2254 Updating <NAME_EMAIL> to <EMAIL>
'2021-12-21 jjc Removing references to dumacwebservices.com


Partial Class Admin_UserProfile
    Inherits System.Web.UI.Page

    Dim InvWSS As New InventoryWebSiteServices.InventoryServices

    Dim sPackageCode As String = "MaintainUsers"
    Dim uid As String = ""
    Dim cutable As New DataTable
    Dim URank As Integer

    Sub Page_Load(ByVal sender As Object, ByVal e As EventArgs) Handles Me.Load
        If Len(Session("UserID")) = 0 Then
            Response.Redirect("login.aspx?timeout=1")
        End If

        InvWSS = Session("IWSS")

        If Request.QueryString("GUID") <> "newuser" Then
            uid = Request.QueryString("GUID")
        End If

        If Session("UserRank") = 900 Then
            trInHouseUser.Visible = True
            trTOSVersion.Visible = True
            trTOSSignatory.Visible = True
        Else
            trInHouseUser.Visible = False
            trTOSVersion.Visible = False
            trTOSSignatory.Visible = False
        End If

        If Not IsPostBack Then
            SBOMaster.TryUserRank(Session("AccountID"), Session("UserRank"), Session("RoleID"))
            URank = CInt(Session("UserRank"))
            GetCurrentUserPerms()
            PopRoles()
            PopTrainers()

            'BDean: Story 1 // B-01999.3 // TK-01029 - Call to Luke's sub to pre-populate DM email addresses
            PopDMEmailAdresses()

            'BDean: Story 1 // B-01999.3 // TK-01029 - Moved the "Password GUID" generation code out of the "btnSave_Click()" sub in order to populate the hyperlink in the "Delivery Method" popup
            Dim sServerName As String = Request.ServerVariables("SERVER_NAME")

            ViewState("PasswordGUID") = Replace(Guid.NewGuid().ToString, "-", "")
            aPasswordHyperlink.HRef = "https://" & sServerName & "/PasswordReset.aspx?GUID=" & ViewState("PasswordGUID")
            aPasswordHyperlink.InnerText = aPasswordHyperlink.HRef

            Dim tTable As New DataTable
            Dim rsFirstName As String = ""
            Dim rsLastName As String = ""
            Dim rsUserID As String = ""
            Dim rsLanguagePreference As String = ""
            Dim rsAccountID As String = ""
            Dim bIsActive As Boolean
            Dim sPassword As String = ""

            If Request.QueryString("GUID") = "newuser" Then
                txtNewUser.Text = "newuser"
                InvWSS.NewUserProfile(rsAccountID, rsFirstName, rsLastName, rsUserID, sPassword, rsLanguagePreference, bIsActive, tTable)
                sPassword = Right(Guid.NewGuid().ToString, 10)
                txtPassword.Attributes.Add("value", sPassword)
                txtAccountID.Text = rsAccountID
                If StoreCount2020() > 0 Then
                    cb2020User.Checked = True
                    cb2020User.Enabled = True
                Else
                    cb2020User.Checked = False
                    cb2020User.Enabled = False
                    'Mark Williams Bug something
                    cb2020EditGuide.Enabled = False
                    cb2020EditSalary.Enabled = False
                End If

                calPWLinkExp.DateMin = DateAdd(DateInterval.Day, 3, DateTime.Now())
                calPWLinkExp.DateMax = DateAdd(DateInterval.Day, 14, DateTime.Now())
                calPWLinkExp.SelectedDate = DateAdd(DateInterval.Day, 3, DateTime.Now())
                calPWLinkExp.DateFirstMonth = DateAdd(DateInterval.Day, 3, DateTime.Now())
            Else
                InvWSS.GetUserProfile(Request.QueryString("GUID"), rsFirstName, rsLastName, rsUserID, sPassword, rsLanguagePreference, bIsActive, tTable)
                txtAccountID.Text = Request.QueryString("GUID")
                Dim iURank As Integer
                Dim sRoleID As String = ""
                UserRank(Request.QueryString("GUID"), iURank, sRoleID)
                ddRole.SelectedValue = sRoleID

                If uid = Session("AccountID") Then
                    If iURank < 900 Then
                        ddRole.Enabled = False
                    End If
                End If
                If Request.QueryString("PWReset") = "1" Then
                    calPWLinkExp.DateMin = DateAdd(DateInterval.Day, 3, DateTime.Now())
                    calPWLinkExp.DateMax = DateAdd(DateInterval.Day, 14, DateTime.Now())
                    calPWLinkExp.SelectedDate = DateAdd(DateInterval.Day, 3, DateTime.Now())
                    calPWLinkExp.DateFirstMonth = DateAdd(DateInterval.Day, 3, DateTime.Now())
                End If
                calPWLinkExp.Enabled = False

                'BDean: Sprint 1 // B-01999.1 // TK-01040 - Updating the "PW Link Exp" textbox & calendar icon so that it looks disabled
                txtPWLinkExp.Enabled = False
                calPWLinkExp.CSSDatePickerButton = "disabled_calendar"

                GetPWDLinkStatus(rsUserID)
            End If
            txtCachedPassword.Text = sPassword
            Session("TempTable") = tTable
            txtFirstName.Text = rsFirstName
            txtLastName.Text = rsLastName
            txtUserID.Text = rsUserID
            If Len(rsUserID) Then
                txtUserID.Enabled = False
            End If
            PopEmailAlertsOptions()

            GetUserInfo(Request.QueryString("GUID"))

            'The password from the platform is encrypted
            'so there is no point in showing it. 
            'DAF 2015-07-30 Sprint 1 / B-01999 / TK-01039
            'txtPassword.Text = ""
            chkIsActive.Checked = bIsActive

            PopRegionGrid()
            PopDistrictGrid()
            PopStoreGrid()
            PopEmailAlertsGrid()
            DisplayOptions()

        End If
    End Sub

    Sub RoleChanged(ByVal sender As Object, ByVal e As EventArgs)
        DisplayOptions()
        PopEmailAlertsGrid()
    End Sub

    Function UserIDExistsInRoot(ByVal sUserID As String) As Boolean
        Dim SQLCmd As New SqlDataAdapter
        Dim dbTable As New DataTable
        Dim row As DataRow
        Dim bResult As Boolean = False
        Try
            SQLCmd = New SqlDataAdapter("SELECT * FROM tbUserOrganization WITH(NOLOCK) WHERE UserID = '" & sUserID & "'", InvWSS.Platform.SBODBConnection("SBORoot"))
            SQLCmd.Fill(dbTable)
            For Each row In dbTable.Rows
                bResult = True
            Next
        Catch eGetDataTable As Exception
            InvWSS.Platform.LogError(eGetDataTable, "Admin_UserProfile.aspx.vb", "UserIDExistsInRoot")
        Finally
            If Not SQLCmd Is Nothing Then
                SQLCmd.Dispose()
            End If
        End Try
        Return bResult
    End Function

    Sub PopEmailAlertsOptions()
        Dim iCount As Integer
        Dim litem As ListItem

        For iCount = 6 To 11
            litem = New ListItem(iCount & ":00 AM EST", iCount)
            ddEmailAlertsTime.Items.Add(litem)
            litem = New ListItem(iCount & ":30 AM EST", iCount + 0.5)
            ddEmailAlertsTime.Items.Add(litem)
        Next
        litem = New ListItem("12:00 PM EST", 12)
        ddEmailAlertsTime.Items.Add(litem)
        litem = New ListItem("12:30 PM EST", 12.5)
        ddEmailAlertsTime.Items.Add(litem)
        For iCount = 13 To 15
            litem = New ListItem(iCount - 12 & ":00 PM EST", iCount)
            ddEmailAlertsTime.Items.Add(litem)
            litem = New ListItem(iCount - 12 & ":30 PM EST", iCount + 0.5)
            ddEmailAlertsTime.Items.Add(litem)
        Next
        ddEmailAlertsTime.SelectedValue = 6

        litem = New ListItem("HTML", 0)
        ddEmailAlertsFormat.Items.Add(litem)

        litem = New ListItem("TAB", 1)
        ddEmailAlertsFormat.Items.Add(litem)
        litem = New ListItem("SPACE", 2)
        ddEmailAlertsFormat.Items.Add(litem)
        ddEmailAlertsFormat.SelectedValue = 0
    End Sub

    Sub DisplayOptions()
        lblRegion.Visible = False
        gridRegion.Visible = False
        lblDistrict.Visible = False
        gridDistrict.Visible = False
        lblStore.Visible = False
        gridStore.Visible = False

        Dim iRoleRank As Integer = GetRoleRank(ddRole.SelectedValue)

        If iRoleRank <= 200 And iRoleRank > 100 Then       'District Mgr Level - Display Store Options
            lblStore.Visible = True
            gridStore.Visible = True
        End If
        If iRoleRank <= 300 And iRoleRank > 200 Then       'District Mgr Level - Display Store Options
            lblDistrict.Visible = True
            gridDistrict.Visible = True
        End If
        If iRoleRank <= 400 And iRoleRank > 300 Then       'District Mgr Level - Display Store Options
            lblRegion.Visible = True
            gridRegion.Visible = True
        End If
        If iRoleRank > 400 Then
            lblRegion.Visible = True
            gridRegion.Visible = True
        End If

    End Sub

    Sub GetCurrentUserPerms()
        Dim rsFirstName As String = ""
        Dim rsLastName As String = ""
        Dim rsUserID As String = ""
        Dim rsLanguagePreference As String = ""
        Dim bIsActive As Boolean
        Dim sPassword As String = ""
        InvWSS.GetUserProfile(Session("AccountID"), rsFirstName, rsLastName, rsUserID, sPassword, rsLanguagePreference, bIsActive, cutable)
    End Sub

    Sub UserRank(ByVal UAI As String, ByRef iUserRank As Integer, ByRef sRoleID As String)
        Dim tbRoles As New DataTable
        Dim row As DataRow
        Dim iRoleRank As Integer
        InvWSS.GetListUserRoles(UAI, tbRoles)
        For Each row In tbRoles.Rows
            iRoleRank = GetRoleRank(row("ItemID"))
            If row("IsMember") And iRoleRank > iUserRank Then
                iUserRank = iRoleRank
                sRoleID = row("itemid")
            End If
        Next
        tbRoles.Dispose()
    End Sub

    Function GetRoleRank(ByVal URI As String) As Integer
        Dim sSQL As String = "SELECT Rank FROM tmRole WITH(NOLOCK) WHERE UniversalRoleIdentifier = '" & URI & "'"
        Dim tTable As New DataTable
        Dim row As DataRow
        Dim iRank As Integer
        InvWSS.GetData(sSQL, tTable)
        For Each row In tTable.Rows
            iRank = row("Rank")
        Next
        tTable.Dispose()
        Return iRank
    End Function

    Sub PopRoles()

        Dim sSQL As String
        'Bug #2687 DAF
        If Session("UserRank") = 800 Then
            sSQL = "SELECT * FROM tmRole WITH(NOLOCK) WHERE Rank <= 200"
        ElseIf Session("UserRank") < 900 Then
            If uid = Session("AccountID") Then
                sSQL = "SELECT * FROM tmRole WITH(NOLOCK) WHERE Rank <= " & Session("UserRank").ToString
            Else
                sSQL = "SELECT * FROM tmRole WITH(NOLOCK) WHERE Rank <= " & Session("UserRank").ToString 'LY added "=" (BZ 2225)
            End If
        Else
            sSQL = "SELECT * FROM tmRole"
        End If

        Dim tTable As New DataTable
        Dim litem As ListItem

        InvWSS.GetData(sSQL, tTable)
        For Each row As DataRow In tTable.Rows
            litem = New ListItem(row("Description"))
            litem.Value = row("UniversalRoleIdentifier")
            ddRole.Items.Add(litem)
        Next
    End Sub

    Sub PopStoreGrid()
        Dim tbStores As New DataTable
        Dim pStores As New DataTable
        Dim tbRow As DataRow
        Dim pRow As DataRow
        pStores = InvWSS.GetPermittedStores()

        tbStores = InvWSS.GetUserStorePermissionList(uid)

        Dim dv As New DataView(tbStores)
        dv.Sort = "StoreDescription"

        For Each tbRow In tbStores.Rows
            tbRow("ReadOnly") = True
            If uid <> Session("AccountID") Or Session("UserRank") >= 900 Then
                For Each pRow In pStores.Rows
                    If pRow("StoreID") = tbRow("StoreID") Then
                        tbRow("ReadOnly") = False
                        Exit For
                    End If
                Next
            End If
        Next
        StoreGrid.DataSource = dv   'tbStores.DefaultView
        StoreGrid.DataBind()
        tbStores.Dispose()
        pStores.Dispose()
    End Sub

    Sub PopRegionGrid()
        Dim tbRegions As New DataTable
        Dim tbRow As DataRow
        tbRegions = InvWSS.SBOWebSiteServices.GetUserRegionPermissionList(uid)

        Dim dv As New DataView(tbRegions)
        dv.Sort = "Region"

        For Each tbRow In tbRegions.Rows
            tbRow("ReadOnly") = True
            If uid <> Session("AccountID") Or Session("UserRank") >= 900 Then
                tbRow("ReadOnly") = False
            End If
        Next

        RegionGrid.DataSource = dv  'tbRegions.DefaultView
        RegionGrid.DataBind()
        tbRegions.Dispose()
    End Sub

    Sub PopDistrictGrid()
        Dim tbDistricts As New DataTable
        Dim pDistricts As New DataTable
        Dim pRow As DataRow
        Dim sSQL As String = "SELECT DS.DistrictNum FROM dbo.tmBin BIN WITH(NOLOCK) "
        sSQL &= "INNER JOIN dbo.vtPermissionsAssigned PERMA WITH(NOLOCK) ON BIN.UniversalBinIdentifier = PERMA.UniversalObjectIdentifier "
        sSQL &= "INNER JOIN dbo.tmDistrict DS WITH(NOLOCK) ON BIN.UniversalDistrictIdentifier = DS.UniversalDistrictIdentifier "
        sSQL &= "WHERE (PERMA.ExecutePermission = 1 OR PERMA.ViewPermission = 1) and "
        sSQL &= "Binlevel = 2 and PERMA.UniversalAccountIdentifier = '" & Session("AccountID") & "' ORDER BY DS.District"

        InvWSS.GetData(sSQL, pDistricts)
        tbDistricts = InvWSS.SBOWebSiteServices.GetUserDistrictPermissionList("", uid)

        Dim dv As New DataView(tbDistricts)
        dv.Sort = "District"

        For Each tbRow As DataRow In tbDistricts.Rows
            tbRow("ReadOnly") = True

            If Session("UserRank") >= 900 Then
                tbRow("ReadOnly") = False
            Else
                If uid <> Session("AccountID") Then
                    For Each pRow In pDistricts.Rows
                        If pRow("DistrictNum") = tbRow("DistrictNum") Then
                            tbRow("ReadOnly") = False
                            Exit For
                        End If
                    Next
                End If
            End If

        Next

        DistrictGrid.DataSource = dv      'tbDistricts.DefaultView
        DistrictGrid.DataBind()
        tbDistricts.Dispose()
    End Sub

    Sub PopTrainers()
        ddlSBOnetTrainer.Items.Add(New ListItem("", -1))
        ddlSchedulerTrainer.Items.Add(New ListItem("", -1))

        ddlSBOnetTrainer.AppendDataBoundItems = True
        ddlSchedulerTrainer.AppendDataBoundItems = True

        Dim sSQL As String = "SELECT [UniversalAccountIdentifier],[LastName] + ', ' + [FirstName] AS [Name]"
        sSQL &= "FROM [dbo].[tmAccount] WITH(NOLOCK) WHERE [InHouseUser] IS NOT NULL ORDER BY [LastName] + ', ' + [FirstName]"

        Dim tTable As New DataTable
        InvWSS.GetData(sSQL, tTable)

        ddlSBOnetTrainer.DataSource = tTable.DefaultView
        ddlSBOnetTrainer.DataTextField = "Name"
        ddlSBOnetTrainer.DataValueField = "UniversalAccountIdentifier"
        ddlSBOnetTrainer.DataBind()

        ddlSchedulerTrainer.DataSource = tTable.DefaultView
        ddlSchedulerTrainer.DataTextField = "Name"
        ddlSchedulerTrainer.DataValueField = "UniversalAccountIdentifier"
        ddlSchedulerTrainer.DataBind()

        tTable.Dispose()
    End Sub

    Sub PopDMEmailAdresses()    'LYeiser 31JUL2015 Sprint 1S/B-01999.3/TK-01044  Pre-Populate DM Email Adress List

        'BDean: Story 1 // B-01999.3 // TK-01029 - Added a check for blank [EmailAddress] in the WHERE clause
        Dim sSQL As String
        sSQL = "select acc.LastName + ', ' + acc.FirstName AS [Name], acc.UserID, acc.EmailAddress from tsUserRole ur "
        sSQL &= "INNER JOIN tmAccount acc ON ur.UniversalAccountIdentifier = acc.UniversalAccountIdentifier "
        sSQL &= "where UniversalRoleIdentifier = '39D536A5B027D2408A69E3D2A40A60E1' "
        sSQL &= "AND acc.EmailAddress IS NOT NULL AND acc.EmailAddress <> ''"
        Dim tTable As New DataTable
        InvWSS.GetData(sSQL, tTable)

        With ddlDMEmailAddress
            .DataSource = tTable.DefaultView
            .DataTextField = "Name"
            .DataValueField = "EmailAddress"
            .DataBind()
        End With

        'BDean: Story 1 // B-01999.3 // TK-01029 - Added a blank item to the list, and selected it by default
        ddlDMEmailAddress.Items.Insert(0, New ListItem("", ""))
        ddlDMEmailAddress.SelectedIndex = 0

    End Sub

    Function UserMenuOptionPermission(ByVal RoleID As String, ByVal MenuOp As String) As Boolean
        Dim bResult As Boolean = False
        Dim tMenuOptions As New DataTable
        'Dim sSQL As String = "SELECT ISNULL(rp.ExecutePermission,0) AS ExecutePermission FROM tsRolePermission rp INNER JOIN tmRole rr ON rp.UniversalRoleIdentifier = rr.UniversalRoleIdentifier WHERE rr.UniversalRoleIdentifier = '" & RoleID & "' AND rp.UniversalObjectIdentifier = (SELECT cc.ClassID FROM tbComponentClass cc WHERE cc.MenuItemName='" & MenuOp & "')"

        Dim sbSQL As New StringBuilder
        sbSQL.AppendLine("SELECT ")
        sbSQL.AppendLine("	ISNULL(rp.ExecutePermission,0) AS [ExecutePermission]")
        sbSQL.AppendLine("FROM ")
        sbSQL.AppendLine("	[tsRolePermission] AS [rp] ")
        sbSQL.AppendLine("	INNER JOIN [tmRole] AS [rr] ON [rp].[UniversalRoleIdentifier] = [rr].[UniversalRoleIdentifier] ")
        sbSQL.AppendLine("WHERE ")
        sbSQL.AppendLine("	[rr].[UniversalRoleIdentifier] = '" & RoleID & "'")
        sbSQL.AppendLine("	AND [rp].[UniversalObjectIdentifier] = ")
        sbSQL.AppendLine("	(")
        sbSQL.AppendLine("		SELECT ")
        sbSQL.AppendLine("			[cc].[ClassID] ")
        sbSQL.AppendLine("		FROM ")
        sbSQL.AppendLine("			[tbComponentClass] AS [cc]")
        sbSQL.AppendLine("		WHERE ")
        sbSQL.AppendLine("			[cc].MenuItemName = '" & MenuOp & "'")
        sbSQL.AppendLine("			AND [cc].[PackageCode] = ")
        sbSQL.AppendLine("			(")
        sbSQL.AppendLine("				SELECT TOP 1")
        sbSQL.AppendLine("                    [TP].[PackageCode]")
        sbSQL.AppendLine("                FROM ")
        sbSQL.AppendLine("					[dbo].[tbPackage] AS [TP] WITH(NOLOCK)")
        sbSQL.AppendLine("                WHERE")
        sbSQL.AppendLine("                    [TP].[PackageName] = 'WebInventory'")
        sbSQL.AppendLine("			)")
        sbSQL.AppendLine("	)")

        InvWSS.GetData(sbSQL.ToString, tMenuOptions)
        For Each row As DataRow In tMenuOptions.Rows
            If row("ExecutePermission") <> 0 Then
                bResult = True
            End If
        Next

        Return bResult
    End Function


    Sub PopEmailAlertsGrid()
        Dim sbSQL As New StringBuilder
        sbSQL.AppendLine("SELECT")
        sbSQL.AppendLine("	tme.[EmailMessageID]")
        sbSQL.AppendLine("	,tme.[Title]")
        sbSQL.AppendLine("	,0 AS [Enabled]")
        sbSQL.AppendLine("	,(")
        sbSQL.AppendLine("		SELECT ")
        sbSQL.AppendLine("			COUNT(*) ")
        sbSQL.AppendLine("		FROM ")
        sbSQL.AppendLine("			[tdEmailMessages] tde WITH(NOLOCK) ")
        sbSQL.AppendLine("		WHERE ")
        sbSQL.AppendLine("			tde.[EmailMessageID] = tme.[EmailMessageID]")
        sbSQL.AppendLine("			AND tde.[UniversalAccountIdentifier] = '" & txtAccountID.Text & "'")
        sbSQL.AppendLine("	) AS [Checked]")
        sbSQL.AppendLine("FROM")
        sbSQL.AppendLine("	[tmEmailMessages] tme")
        sbSQL.AppendLine("")

        Dim tTable As New DataTable
        InvWSS.GetData(sbSQL.ToString, tTable)

        Dim iUserRank As Integer
        Dim sRoleID As String = ""
        UserRank(Session("AccountID"), iUserRank, sRoleID)

        For Each row As DataRow In tTable.Rows
            Select Case CStr(row("Title"))
                Case "Weekly Flash Report"
                    row("Enabled") = UserMenuOptionPermission(sRoleID, "Flash Report") And UserMenuOptionPermission(ddRole.SelectedValue, "Flash Report")
                Case "Weekly Flash Report (Districts)"
                    row("Enabled") = UserMenuOptionPermission(sRoleID, "Flash Report (Districts)") And UserMenuOptionPermission(ddRole.SelectedValue, "Flash Report (Districts)")
                Case "Weekly Late Night"
                    row("Enabled") = UserMenuOptionPermission(sRoleID, "Late Night") And UserMenuOptionPermission(ddRole.SelectedValue, "Late Night")
                Case "Weekly Scorecard"
                    row("Enabled") = UserMenuOptionPermission(sRoleID, "Scorecard") And UserMenuOptionPermission(ddRole.SelectedValue, "Scorecard")
                Case Else
                    row("Enabled") = 1
            End Select
        Next
        EmailAlertsGrid.DataSource = tTable.DefaultView
        EmailAlertsGrid.DataBind()

    End Sub

    Sub btnSave_click(ByVal sender As Object, ByVal e As EventArgs)
        'Invalidate the menu cache.
        SBOMaster.MenuCacheValid = False

        'BDean: Story 1 // B-01999.5 // TK-01052 - Check for the "PWReset=1" parameter in the query string
        Dim bPWReset As Boolean = IIf(Request.QueryString("PWReset") = "1", True, False)
        If bPWReset Then

            'BDean: Story 1 - Adjust the calendar date 3 days in the future if resetting an existing user's password
            calPWLinkExp.SelectedDate = DateAdd(DateInterval.Day, 3, Now())

            UpdatePWDLinkExp(txtUserID.Text)
            PasswordDeliveryHandler()
            Response.Redirect("admin_users.aspx")

        Else

            Dim i As Integer
            Dim _item As DataGridItem
            Dim SomethingFailed As Boolean
            Dim sPassword As String
            Dim iMinimumPasswordLength As Integer = 0
            'Get user profile always returns an empty string as the
            'password. Post user profile will alter the password only
            'if the password argument is not empty. It expects an
            'unencrypted password which it will encrypt and write.  

            If txtNewUser.Text = "newuser" Then
                If UserIDExistsInRoot(txtUserID.Text) Or User2020Exists(txtUserID.Text) Then
                    trErrorLabel.Visible = True
                    lblErrorMessage.Text = "This UserID is already in use."
                    Exit Sub
                End If
            End If

            sPassword = txtPassword.Text.Trim
            If sPassword.Length > 0 Then
                iMinimumPasswordLength = InvWSS.Platform.PersistentValue("BusinessRules", "MinimumPasswordLength")
                If sPassword.Length < iMinimumPasswordLength Then
                    Dim sGuid As String = Guid.NewGuid.ToString.Replace("-", "").ToUpper
                    trErrorLabel.Visible = True
                    lblErrorMessage.Text = "Password must contain at least " & iMinimumPasswordLength.ToString & " characters"
                    Exit Sub
                End If
            End If

            'When the page loads, it generates a new UniversalAccountIdentifier, however when PostUserProfile is called, it creates another new UniversalAccountIdentifier causing havoc and subsequently screws everything up. 
            Dim sNewGuid As String = txtAccountID.Text
            InvWSS.PostUserProfile(sNewGuid, Replace(txtFirstName.Text, "'", ""), Replace(txtLastName.Text, "'", ""), Replace(txtUserID.Text, "'", ""), sPassword, "English", chkIsActive.Checked, Session("TempTable"))
            txtAccountID.Text = sNewGuid

            Dim ermsg As String = InvWSS.ErrorMessage
            If Len(ermsg) Then
                SomethingFailed = True
                trErrorLabel.Visible = True
                lblErrorMessage.Text = ermsg.ToString
                Exit Sub
            End If

            Try
                SaveUserInfo()
                SaveEmailAlerts()
            Catch ex As Exception
                SomethingFailed = True
                trErrorLabel.Visible = True
                lblErrorMessage.Text = ex.ToString
            End Try

            If Session("UserRank") < 900 Then
                If Session("AccountID") = txtAccountID.Text Then
                    If Not SomethingFailed Then
                        Response.Redirect("Admin_Users.aspx")
                    End If
                    Exit Sub
                End If
            End If

            Try
                RemoveAllPermissions(txtAccountID.Text)

                Dim AcctID As String = txtAccountID.Text
                Dim SID As Integer
                Dim Permit As Boolean
                Dim bResult As Boolean
                If gridStore.Visible Then
                    For i = 0 To StoreGrid.Items.Count - 1
                        _item = StoreGrid.Items(i)
                        Dim Permission As CheckBox = _item.FindControl("Permitted")
                        Dim StoreID As Label = _item.FindControl("StoreID")
                        If Permission.Enabled Then
                            SID = Val(StoreID.Text)
                            Permit = Permission.Checked
                            GrantUserPermission(1, SID, AcctID, Permit)
                        End If
                    Next i
                End If

                If gridRegion.Visible Then
                    For i = 0 To RegionGrid.Items.Count - 1
                        _item = RegionGrid.Items(i)
                        Dim Permission As CheckBox = _item.FindControl("Permitted")
                        Dim RegionNum As Label = _item.FindControl("RegionNum")
                        If Permission.Enabled Then
                            SID = Val(RegionNum.Text)
                            Permit = Permission.Checked
                            GrantUserPermission(3, SID, AcctID, Permit)
                            GrantDistrictPermissions(SID, AcctID, Permit)
                        End If
                    Next i
                End If

                If gridDistrict.Visible Then
                    For i = 0 To DistrictGrid.Items.Count - 1
                        _item = DistrictGrid.Items(i)
                        Dim Permission As CheckBox = _item.FindControl("Permitted")
                        Dim DistrictID As Label = _item.FindControl("DistrictID")
                        If Permission.Enabled Then
                            SID = Val(DistrictID.Text)
                            Permit = Permission.Checked
                            GrantUserPermission(2, SID, AcctID, Permit)
                            GrantStorePermissions(SID, AcctID, Permit)
                        End If
                    Next i
                End If

                Dim litem As ListItem
                For Each litem In ddRole.Items
                    If litem.Selected Then
                        bResult = InvWSS.RoleAddUser(AcctID, litem.Value)

                        Dim tabFavs As New DataTable
                        InvWSS.GetData("SELECT * FROM tsUserPreference WITH(NOLOCK) WHERE ObjectName = 'Favorites' AND UniversalAccountIdentifier = '" & AcctID & "'", tabFavs)
                        If tabFavs.Rows.Count = 0 Then
                            CreateInitalFavorites(GetRoleRank(litem.Value), AcctID)
                        End If
                        tabFavs.Dispose()

                        If GetRoleRank(litem.Value) >= 900 Then
                            SendEmailNotice()
                        End If
                    Else
                        bResult = InvWSS.RoleDropUser(AcctID, litem.Value)
                    End If
                Next litem

                SaveEmailAlerts()

            Catch ex As Exception
                SomethingFailed = True
                trErrorLabel.Visible = True
                lblErrorMessage.Text = ex.ToString
            End Try

            If SomethingFailed = False Then
                If txtNewUser.Text = "newuser" Then
                    Insert2020User()
                End If

                If (chkInHouseUser.Checked) Then
                    'Don't create a 2020 user or update an existing one
                    Response.Redirect("admin_users.aspx")
                ElseIf cb2020User.Checked Then

                    Update2020UserStores()
                    updateSchedulerEditPerm()
                    'Mark Williams Bug 2460 Sync Passwords
                    If Not txtPassword.Text = "" Then
                        updateEmployeeSchedulerPassword()
                    End If

                    'Mark Williams Sync Active
                    If chkIsActive.Checked = False Then
                        setSchedulerUserInActive()
                    Else
                        setSchedulerUserActive()
                    End If
                Else
                    'Check to see if user's stores has changed to include a scheduler store.
                    Dim tempTable As New DataTable
                    tempTable = Get2020UserStores()
                    If Not tempTable Is Nothing Then
                        If tempTable.Rows.Count Then
                            'User has a scheduler store
                            'Check to if the user already exists
                            Dim i2020UserID As Integer = User2020Exists(txtUserID.Text)
                            If i2020UserID > 0 Then
                                getEmployeePassword()
                                updateEmployeeSchedulerPassword()
                                updateSBONetSchedulerInformation()
                                Update2020UserStores()
                            Else
                                'make a fresh user
                                getEmployeePassword()
                                Insert2020User()
                                updateSBONetSchedulerInformation()
                                Update2020UserStores()
                            End If

                            'Mark Williams Sync Active
                            If chkIsActive.Checked = False Then
                                setSchedulerUserInActive()
                            Else
                                setSchedulerUserActive()
                            End If
                        End If
                    End If

                End If

                'BDean: Story 1 // B-01999.3 // TK-01047 - If the password reset link needs to be emailed --> Send it, Else --> Do nothing
                If txtNewUser.Text = "newuser" Then
                    UpdatePWDLinkExp(txtUserID.Text)
                    PasswordDeliveryHandler()
                End If

                Response.Redirect("admin_users.aspx")
            End If

        End If

    End Sub

    Sub SaveEmailAlerts()
        Dim _item As DataGridItem
        Dim sSQL As String
        Dim i As Integer
        Try
            sSQL = "DELETE FROM tdEmailMessages WHERE UniversalAccountIdentifier = '" & txtAccountID.Text & "'"
            RunCommand(sSQL)
            For i = 0 To EmailAlertsGrid.Items.Count - 1
                _item = EmailAlertsGrid.Items(i)
                Dim chkPermitted As CheckBox = _item.FindControl("Permitted")
                Dim lblEmailMessageID As Label = _item.FindControl("emailmessageid")
                If chkPermitted.Checked Then
                    sSQL = "INSERT INTO tdEmailMessages (EmailMessageID, UniversalAccountIdentifier) VALUES (" & lblEmailMessageID.Text & ", '" & txtAccountID.Text & "')"
                    RunCommand(sSQL)
                End If
            Next
        Catch eSaveEmailAlerts As Exception
            lblErrorMessage.Text = eSaveEmailAlerts.ToString
            trErrorLabel.Visible = True
        End Try
    End Sub

    Sub MyDataGrid_ItemDataBound(ByVal source As Object, ByVal e As DataGridItemEventArgs)
        Select Case e.Item.ItemType
            Case ListItemType.Item, ListItemType.AlternatingItem
        End Select
    End Sub

    Sub GrantStorePermissions(ByVal DistrictID As Integer, ByVal AcctID As String, ByVal Permit As Boolean)
        Dim sSQL As String = "SELECT StoreID FROM tmStore WITH(NOLOCK) WHERE DistrictNum = " & DistrictID
        Dim tTable As New DataTable
        InvWSS.GetData(sSQL, tTable)
        Dim row As DataRow
        For Each row In tTable.Rows
            GrantUserPermission(1, row("StoreID"), AcctID, Permit)
        Next
        tTable.Dispose()
    End Sub

    Sub GrantDistrictPermissions(ByVal RegionID As Integer, ByVal AcctID As String, ByVal Permit As Boolean)
        Dim sSQL As String = "SELECT DISTINCT DistrictNum FROM tmStore WITH(NOLOCK) WHERE RegionNum = " & RegionID
        Dim tTable As New DataTable
        InvWSS.GetData(sSQL, tTable)
        Dim row As DataRow
        For Each row In tTable.Rows
            GrantUserPermission(2, row("DistrictNum"), AcctID, Permit)
            GrantStorePermissions(row("DistrictNum"), AcctID, Permit)
        Next
        tTable.Dispose()
    End Sub

    Sub RemoveAllPermissions(ByVal sUserAccountID As String)
        Dim tTable As New DataTable
        Dim row As DataRow
        Dim sSQL As String

        sSQL = "SELECT StoreID FROM tmStore WITH(NOLOCK)"
        tTable = New DataTable
        InvWSS.GetData(sSQL, tTable)
        For Each row In tTable.Rows
            GrantUserPermission(1, row("StoreID"), sUserAccountID, False)
        Next

        sSQL = "SELECT DistrictNum FROM tmDistrict WITH(NOLOCK)"
        tTable = New DataTable
        InvWSS.GetData(sSQL, tTable)
        For Each row In tTable.Rows
            GrantUserPermission(2, row("DistrictNum"), sUserAccountID, False)
        Next

        sSQL = "SELECT RegionNum FROM tmRegion"
        InvWSS.GetData(sSQL, tTable)
        For Each row In tTable.Rows
            GrantUserPermission(3, row("RegionNum"), sUserAccountID, False)
        Next

        tTable.Dispose()
    End Sub


    Sub GrantUserPermission(ByVal BinLevel As Integer, ByVal RDSID As Integer, ByVal UserAccountID As String, ByVal vbAllow As Boolean)

        'BinLevel 1 = Store
        'BinLevel 2 = District
        'BinLevel 3 = Region
        Dim sObjectGUID As String = ""
        Dim SQLCmd As New SqlCommand
        Dim SQLCon As New SqlConnection(Session("ConnectString"))

        SQLCon.Open()

        Dim sSQLUBID As String = ""
        Dim row As DataRow
        Dim tTable As New DataTable
        Select Case BinLevel
            Case 1
                sSQLUBID = "Select UniversalBinIdentifier FROM dbo.tmBin WITH(NOLOCK) LEFT OUTER JOIN tmStore ON tmBin.UniversalStoreIdentifier = tmStore.UniversalStoreIdentifier WHERE BinLevel = 1 AND StoreID = " & RDSID
            Case 2
                sSQLUBID = "Select UniversalBinIdentifier FROM dbo.tmBin WITH(NOLOCK) LEFT OUTER JOIN tmDistrict ON tmBin.UniversalDistrictIdentifier = tmDistrict.UniversalDistrictIdentifier WHERE BinLevel = 2 AND DistrictNum = " & RDSID
            Case 3
                sSQLUBID = "Select UniversalBinIdentifier FROM dbo.tmBin WITH(NOLOCK) LEFT OUTER JOIN tmRegion ON tmBin.UniversalRegionIdentifier = tmRegion.UniversalRegionIdentifier WHERE BinLevel = 3 AND RegionNum = " & RDSID
            Case 4
                sSQLUBID = "Select UniversalBinIdentifier FROM dbo.tmBin WITH(NOLOCK) LEFT OUTER JOIN tmFranchise ON tmBin.UniversalFranchiseIdentifier = tmFranchise.UniversalFranchiseIdentifier WHERE BinLevel = 4 AND FranchiseNum = " & RDSID
            Case 5
                sSQLUBID = "Select UniversalBinIdentifier FROM dbo.tmBin WITH(NOLOCK) LEFT OUTER JOIN tmOwner ON tmBin.UniversalOwnerIdentifier = tmOwner.UniversalOwnerIdentifier WHERE BinLevel = 5 AND OwnerNum = " & RDSID
            Case 6
                sSQLUBID = "Select UniversalBinIdentifier FROM dbo.tmBin WITH(NOLOCK) WHERE BinLevel = 6"
            Case Else
        End Select
        InvWSS.GetData(sSQLUBID, tTable)

        With SQLCmd
            .CommandText = "ps_sm_GrantUserPermission"
            .CommandType = CommandType.StoredProcedure
            .Connection = SQLCon
            .Parameters.Add("@RequestedBy", SqlDbType.VarChar, &H20)
            .Parameters.Add("@RequestingNode", SqlDbType.VarChar, &H20)
            .Parameters.Add("@RequestingWorkstation", SqlDbType.VarChar, 50)
            .Parameters.Add("@UniversalAccountIdentifier", SqlDbType.VarChar, 32)
            .Parameters.Add("@UniversalObjectIdentifier", SqlDbType.VarChar, 32)
            .Parameters.Add("@ViewPermission", SqlDbType.Bit)
            .Parameters.Add("@CreatePermission", SqlDbType.Bit)
            .Parameters.Add("@UpdatePermission", SqlDbType.Bit)
            .Parameters.Add("@DeletePermission", SqlDbType.Bit)
            .Parameters.Add("@ExecutePermission", SqlDbType.Bit)
        End With
        For Each row In tTable.Rows
            'sObjectGUID = GetUniversalObjectID(BinLevel, RDSID)
            sObjectGUID = row("UniversalBinIdentifier")
            With SQLCmd
                .Parameters.Item("@RequestedBy").Value = "5EDD5CEB8A8311D5948800105A738B10"
                .Parameters.Item("@RequestingNode").Value = "********************************"
                .Parameters.Item("@RequestingWorkstation").Value = "ASP-WSS-1"
                .Parameters.Item("@UniversalAccountIdentifier").Value = UserAccountID
                .Parameters.Item("@UniversalObjectIdentifier").Value = sObjectGUID
                .Parameters.Item("@ViewPermission").Value = vbAllow
                .Parameters.Item("@CreatePermission").Value = 0
                .Parameters.Item("@UpdatePermission").Value = vbAllow
                .Parameters.Item("@DeletePermission").Value = 0
                .Parameters.Item("@ExecutePermission").Value = vbAllow
                'Response.Write("BinLevel=" & BinLevel  & "    RDSID=" & RDSID & "    ObjectID=" & sObjectGUID & "     Allow=" & vbAllow.ToString & "<br>")
                .ExecuteNonQuery()
            End With
        Next

        SQLCmd.Dispose()
        SQLCon.Dispose()
    End Sub

    Function GetUniversalObjectID(ByVal iBinLevel As Integer, ByVal iRDSID As Integer) As String
        Dim sSQL As String = ""
        Dim sObjectGUID As String = ""
        Dim sRDSGUID As String
        sRDSGUID = GetObjectGUID(iBinLevel, iRDSID)

        Select Case iBinLevel
            Case 1
                sSQL = "Select UniversalBinIdentifier FROM dbo.tmBin WITH(NOLOCK) WHERE BinLevel = 1 AND UniversalStoreIdentifier = '" & sRDSGUID & "'"
            Case 2
                sSQL = "Select UniversalBinIdentifier FROM dbo.tmBin WITH(NOLOCK) WHERE BinLevel = 2 AND UniversalDistrictIdentifier = '" & sRDSGUID & "'"
            Case 3
                sSQL = "Select UniversalBinIdentifier FROM dbo.tmBin WITH(NOLOCK) WHERE BinLevel = 3 AND UniversalRegionIdentifier = '" & sRDSGUID & "'"
            Case 4
                sSQL = "Select UniversalBinIdentifier FROM dbo.tmBin WITH(NOLOCK) WHERE BinLevel = 4 AND UniversalFranchiseIdentifier = '" & sRDSGUID & "'"
            Case 5
                sSQL = "Select UniversalBinIdentifier FROM dbo.tmBin WITH(NOLOCK) WHERE BinLevel = 5 AND UniversalOwnerIdentifier = '" & sRDSGUID & "'"
            Case 6
                sSQL = "Select UniversalBinIdentifier FROM dbo.tmBin WITH(NOLOCK) WHERE BinLevel = 6"
            Case Else
        End Select

        Dim tTable As New DataTable
        Dim row As DataRow
        InvWSS.GetData(sSQL, tTable)
        For Each row In tTable.Rows
            sObjectGUID = row("UniversalBinIdentifier")
        Next
        tTable = Nothing
        Return sObjectGUID
    End Function

    Function GetObjectGUID(ByVal iBinLevel As Integer, ByVal iRDSID As Integer) As String
        Dim sSQL As String = ""
        Dim sOGUID As String = ""
        Select Case iBinLevel
            Case 1
                sSQL = "Select UniversalStoreIdentifier As UID FROM dbo.tmStore WITH(NOLOCK) WHERE StoreID = " & iRDSID
            Case 2
                sSQL = "Select UniversalDistrictIdentifier As UID FROM dbo.tmDistrict WITH(NOLOCK) WHERE DistrictNum = " & iRDSID
            Case 3
                sSQL = "Select UniversalRegionIdentifier As UID FROM dbo.tmRegion WITH(NOLOCK) WHERE RegionNum = " & iRDSID
            Case 4
                sSQL = "Select UniversalBinIdentifier As UID FROM dbo.tmFranchise WITH(NOLOCK) WHERE FranchiseNum = " & iRDSID
            Case Else
        End Select
        Dim tTable As New DataTable
        Dim row As DataRow
        InvWSS.GetData(sSQL, tTable)
        For Each row In tTable.Rows
            sOGUID = row("UID")
        Next
        tTable = Nothing
        Return sOGUID
    End Function

    Sub SaveUserInfo()
        Dim sSQL As String = "UPDATE tmAccount SET "

        If Len(txtEmailAddress.Text.Trim) = 0 Then
            sSQL &= "EmailAddress = NULL "
        Else
            sSQL &= "EmailAddress = '" & txtEmailAddress.Text.Trim & "' "
        End If

        sSQL &= ",ActiveAccount = " & IIf(chkIsActive.Checked = True, -1, 0).ToString & " "
        sSQL &= ",InHouseUser = " & IIf(chkInHouseUser.Checked = True, -1, 0).ToString & " "

        If Len(txtPhoneNumber.Text.Trim) = 0 Then
            sSQL &= ",PhoneNumber = NULL "
        Else
            sSQL &= ",PhoneNumber = '" & txtPhoneNumber.Text.Trim & "' "
        End If

        If Len(txtCellPhone.Text.Trim) = 0 Then
            sSQL &= ",CellPhone = NULL "
        Else
            sSQL &= ",CellPhone = '" & txtCellPhone.Text.Trim & "' "
        End If

        If Len(txtSBOnetTrainingDate.Text.Trim) = 0 Then
            sSQL &= ",SBOnetTrainingDate = NULL "
        Else
            sSQL &= ",SBOnetTrainingDate = '" & txtSBOnetTrainingDate.Text.Trim & "' "
        End If

        If Len(ddlSBOnetTrainer.SelectedValue) = 0 Then
            sSQL &= ",SBOnetTrainer = NULL "
        Else
            sSQL &= ",SBOnetTrainer = '" & ddlSBOnetTrainer.SelectedValue & "' "
        End If

        If Len(txtSchedulerTrainingDate.Text.Trim) = 0 Then
            sSQL &= ",SchedulerTrainingDate = NULL "
        Else
            sSQL &= ",SchedulerTrainingDate = '" & txtSchedulerTrainingDate.Text.Trim & "' "
        End If

        If Len(ddlSchedulerTrainer.SelectedValue) = 0 Then
            sSQL &= ",SchedulerTrainer = NULL "
        Else
            sSQL &= ",SchedulerTrainer = '" & ddlSchedulerTrainer.SelectedValue & "' "
        End If
        sSQL &= ",EmailStoreAlerts = " & IIf(cbEmailAlerts.Checked, 1, 0).ToString
        sSQL &= ",EmailStoreAlertsTime = " & ddEmailAlertsTime.SelectedValue
        sSQL &= ",EmailStoreAlertsFormat = " & ddEmailAlertsFormat.SelectedValue

        sSQL &= ",ShowFavoritesMenu = " & IIf(cbShowFavorites.Checked = True, -1, 0).ToString & " "
        sSQL &= ",TOSSignatory = " & IIf(chkTOSSignatory.Checked = True, -1, 0).ToString & " "

        sSQL &= ",StaffSchedulerEditSalaries = " & IIf(cb2020EditSalary.Checked = True, 1, 0).ToString & " "
        sSQL &= ",StaffSchedulerEditLaborGuide = " & IIf(cb2020EditGuide.Checked = True, 1, 0).ToString & " "

        sSQL &= ",ThumbPrint = '" & Session("AccountID") & "' "
        sSQL &= "WHERE UniversalAccountIdentifier = '" & txtAccountID.Text & "'"
        RunCommand(sSQL)
        update2020CanReopenSched()

    End Sub
    Private Sub GetPWDLinkStatus(ByVal sUserID As String)

        Dim sbSQL As New StringBuilder
        Dim Cmd As SqlCommand
        Dim dtUserOrganization As DataTable

        'sbSQL.AppendLine("DECLARE @UserID NVARCHAR(30)")
        'sbSQL.AppendLine("")
        sbSQL.AppendLine("SELECT")
        sbSQL.AppendLine("	 tbUO.[UserID]")
        sbSQL.AppendLine("	,tbUO.[PassWord]")
        sbSQL.AppendLine("	,tbUO.[OrganizationNumber]")
        sbSQL.AppendLine("	,tbUO.[PasswordGUID]")
        sbSQL.AppendLine("	,tbUO.[PasswordResetExpiration]")
        sbSQL.AppendLine("	,tbUO.[PasswordLinkEmail]")
        sbSQL.AppendLine("FROM [SboRoot].[SboRoot].[dbo].[tbUserOrganization] AS [tbUO]")
        sbSQL.AppendLine("	INNER JOIN [SBORoot].[SBORoot].[dbo].[tbOrganizationDatabase] AS [tbOD]")
        sbSQL.AppendLine("		ON tbUO.[OrganizationNumber]  = tbOD.[OrganizationNumber]")
        sbSQL.AppendLine("			AND tbOD.[LocalDatabaseName] = DB_NAME()")
        sbSQL.AppendLine("WHERE [UserID] = @UserID")
        sbSQL.AppendLine("	AND [PasswordResetExpiration] > GETDATE()")
        sbSQL.AppendLine("	AND NOT [PasswordGUID] IS NULL")
        sbSQL.AppendLine("")
        sbSQL.AppendLine("")
        sbSQL.AppendLine("")

        'InvWSS.Platform.SBODBExecNonQuerySQL("SBOCore", sbSQL.ToString)

        Cmd = New SqlCommand(sbSQL.ToString)
        Cmd.Parameters.AddWithValue("@UserID", sUserID)

        dtUserOrganization = InvWSS.Platform.SBODBExecQuerySQLCommand("SBOcore", Cmd)

        If Not dtUserOrganization Is Nothing And dtUserOrganization.Rows.Count > 0 Then
            If dtUserOrganization.Rows(0).Item("PasswordLinkEmail") Is DBNull.Value Then
                lblActiveReset.Text = String.Format("There is an active reset request that is vaild until: {0}", dtUserOrganization.Rows(0).Item("PasswordResetExpiration"))
            Else
                lblActiveReset.Text = String.Format("There is an active reset request that is vaild until: {0}  Sent to: {1}", dtUserOrganization.Rows(0).Item("PasswordResetExpiration"), dtUserOrganization.Rows(0).Item("PasswordLinkEmail"))
            End If
        Else
            lblActiveReset.Text = String.Empty
        End If

    End Sub

    Private Sub UpdatePWDLinkExp(ByVal sUserID As String)

        Dim pwEmailAddress As String = GetPasswordDeliveryEmail()
        Dim sbSQL As New StringBuilder
        Dim dPWLinkExp As DateTime

        sbSQL.AppendLine("DECLARE @EmailAddress AS VARCHAR(100)")

        If pwEmailAddress.Length > 0 Then
            sbSQL.AppendLine("SET @EmailAddress = '" & pwEmailAddress & "'")
        End If

        If Not Date.TryParse(calPWLinkExp.SelectedDate, dPWLinkExp) Then
            dPWLinkExp = DateAdd(DateInterval.Day, 3, DateTime.Now())
        End If


        sbSQL.AppendLine("UPDATE [SBORoot].[SBORoot].[dbo].[tbUserOrganization]")
        sbSQL.AppendLine("SET")
        sbSQL.AppendLine("   [PasswordResetExpiration] = '" & dPWLinkExp.ToString("yyyy-MM-dd") & "'")
        sbSQL.AppendLine("	,[PasswordLinkEmail] = @EmailAddress")
        sbSQL.AppendLine("  ,[PasswordGUID] = '" & ViewState("PasswordGUID") & "'")
        sbSQL.AppendLine("WHERE")
        sbSQL.AppendLine("  [UserID] = '" & Replace(sUserID, "'", "") & "'")
        sbSQL.AppendLine("	AND [OrganizationNumber] = (SELECT [OrganizationNumber] FROM [SBORoot].[SBORoot].[dbo].[tbOrganizationDatabase] WHERE [LocalDatabaseName] = DB_NAME())")

        InvWSS.Platform.SBODBExecNonQuerySQL("SBOCore", sbSQL.ToString)

    End Sub

    Sub GetUserInfo(ByVal UserAccountID As String)
        Dim row As DataRow
        Dim tTable As New DataTable
        Dim sbSQL As New StringBuilder
        sbSQL.AppendLine("SELECT")
        sbSQL.AppendLine("  [EmailAddress]")
        sbSQL.AppendLine("  ,[InHouseUser]")
        sbSQL.AppendLine("  ,[PhoneNumber]")
        sbSQL.AppendLine("  ,[CellPhone]")
        sbSQL.AppendLine("  ,[SBOnetTrainingDate]")
        sbSQL.AppendLine("  ,[SBOnetTrainer]")
        sbSQL.AppendLine("  ,[SchedulerTrainingDate]")
        sbSQL.AppendLine("  ,[SchedulerTrainer]")
        sbSQL.AppendLine("  ,[EmailStoreAlerts]")
        sbSQL.AppendLine("  ,[EmailStoreAlertsTime]")
        sbSQL.AppendLine("  ,[EmailStoreAlertsFormat]")
        sbSQL.AppendLine("  ,[EmailScorecard]")
        sbSQL.AppendLine("  ,[ShowFavoritesMenu]")
        sbSQL.AppendLine("  ,[TOSRevsionID]")
        sbSQL.AppendLine("  ,[TOSSignDate]")
        sbSQL.AppendLine("  ,[TOSSignatory]")
        sbSQL.AppendLine("  ,[StaffSchedulerEditSalaries]")
        sbSQL.AppendLine("  ,[StaffSchedulerEditLaborGuide]")
        sbSQL.AppendLine("FROM")
        sbSQL.AppendLine("  tmAccount WITH(NOLOCK)")
        sbSQL.AppendLine("WHERE")
        sbSQL.AppendLine("  UniversalAccountIdentifier = '" & UserAccountID & "'")
        Dim sEmailAddress As String = ""


        InvWSS.GetData(sbSQL.ToString, tTable)
        For Each row In tTable.Rows
            If Not IsDBNull(row("EmailAddress")) Then
                txtEmailAddress.Text = row("EmailAddress")
            End If
            If Not IsDBNull(row("InHouseUser")) Then
                chkInHouseUser.Checked = row("InHouseUser")
            End If
            If Not IsDBNull(row("PhoneNumber")) Then
                txtPhoneNumber.Text = row("PhoneNumber")
            End If
            If Not IsDBNull(row("CellPhone")) Then
                txtCellPhone.Text = row("CellPhone")
            End If
            If Not IsDBNull(row("SBOnetTrainingDate")) Then
                txtSBOnetTrainingDate.Text = CDate(row("SBOnetTrainingDate")).ToShortDateString
            End If
            If Not IsDBNull(row("SBOnetTrainer")) Then
                SetTrainer(row("SBOnetTrainer"), ddlSBOnetTrainer)
            End If
            If Not IsDBNull(row("SchedulerTrainingDate")) Then
                txtSchedulerTrainingDate.Text = CDate(row("SchedulerTrainingDate")).ToShortDateString
            End If
            If Not IsDBNull(row("SchedulerTrainer")) Then
                SetTrainer(row("SchedulerTrainer"), ddlSchedulerTrainer)
            End If
            If Not IsDBNull(row("EmailStoreAlerts")) Then
                If row("EmailStoreAlerts") <> 0 Then
                    cbEmailAlerts.Checked = True
                End If
            End If
            If Not IsDBNull(row("EmailStoreAlertsTime")) Then
                ddEmailAlertsTime.SelectedValue = row("EmailStoreAlertsTime")
            End If
            If Not IsDBNull(row("EmailStoreAlertsFormat")) Then
                ddEmailAlertsFormat.SelectedValue = row("EmailStoreAlertsFormat")
            End If

            If Not IsDBNull(row("ShowFavoritesMenu")) Then
                cbShowFavorites.Checked = IIf(row("ShowFavoritesMenu") <> 0, True, False)
            End If
            If Not IsDBNull(row("TOSSignatory")) Then
                chkTOSSignatory.Checked = IIf(row("TOSSignatory") <> 0, True, False)
                If Not IsDBNull(row("TOSRevsionID")) Then
                    lblTOS.Text = "ver: " & row("TOSRevsionID") & " - " & row("TOSSignDate")
                    If URank = 900 Then
                        trTOSVersion.Visible = True
                    End If
                Else
                    trTOSVersion.Visible = False
                End If
            Else
                trTOSVersion.Visible = False
            End If
            If Not IsDBNull(row("StaffSchedulerEditSalaries")) Then
                cb2020EditSalary.Checked = IIf(row("StaffSchedulerEditSalaries") <> 0, True, False)
            End If
            If Not IsDBNull(row("StaffSchedulerEditLaborGuide")) Then
                cb2020EditGuide.Checked = IIf(row("StaffSchedulerEditLaborGuide") <> 0, True, False)
            End If
        Next
        If User2020Exists(txtUserID.Text) > 0 Then
            cb2020User.Checked = True
        Else
            cb2020User.Checked = False
            'Mark Williams Bug something
            cb2020EditGuide.Enabled = False
            cb2020EditSalary.Enabled = False
        End If
        cb2020User.Enabled = False
        cb2020CanReopenSched.Checked = CanReopenSched(txtUserID.Text)
        If URank < 300 Then
            cb2020CanReopenSched.Enabled = False
        Else
            cb2020CanReopenSched.Enabled = True
        End If

        sbSQL = New StringBuilder
        sbSQL.AppendLine("SELECT")
        sbSQL.AppendLine("  [PasswordResetExpiration]")
        sbSQL.AppendLine("FROM")
        sbSQL.AppendLine("  tbUserOrganization")
        sbSQL.AppendLine("WHERE")
        sbSQL.AppendLine("  UserID = '" & txtUserID.Text & "'")

        Dim tabPasswordExp As New DataTable
        tabPasswordExp = InvWSS.Platform.SBODBExecQuerySQL("SBORoot", sbSQL.ToString)
        If Not IsNothing(tabPasswordExp) AndAlso tabPasswordExp.Rows.Count > 0 AndAlso IsDate(tabPasswordExp.Rows(0).Item(0)) Then
            calPWLinkExp.SelectedDate = CDate(tabPasswordExp.Rows(0).Item(0))
            txtPWLinkExp.Text = CDate(tabPasswordExp.Rows(0).Item(0)).ToShortDateString
        End If
    End Sub

    Sub SetTrainer(ByVal sGUID As String, ByVal ddlName As DropDownList)
        For Each item As ListItem In ddlName.Items
            If sGUID = item.Value Then
                item.Selected = True
            End If
        Next
    End Sub

    Sub SendEmailNotice()
        Dim sbMessage As New StringBuilder
        sbMessage.AppendLine("This is an automated message from SBOnet.<br /><br />")

        sbMessage.AppendLine("A user account has been added or modified having a Dealer role.<br /><br />")

        sbMessage.AppendLine("Organization: " & Session("OrganizationName") & "<br/>")
        sbMessage.AppendLine("Author UserID: " & Session("UserID") & "<br/>")
        sbMessage.AppendLine("Author Account: " & Session("AccountID") & "<br/><br/>")

        sbMessage.AppendLine("New/Modified Account Information.<br /><br />")

        sbMessage.AppendLine("Account ID: " & Replace(txtAccountID.Text, "'", "") & "<br/>")
        sbMessage.AppendLine("First Name: " & Replace(txtFirstName.Text, "'", "") & "<br/>")
        sbMessage.AppendLine("Last Name: " & Replace(txtLastName.Text, "'", "") & "<br/>")

        'Send email w/ SendGrid
        'WebServicesAddons.AmazonSES.SendMail("<EMAIL>", "Alert - Dealer Added", sbMessage.ToString)
        WebServicesAddons.SendGridEmailService.SendMail(System.Configuration.ConfigurationManager.AppSettings("EmailAddress.SBOWebWork"), "Alert - Dealer Added", sbMessage.ToString)
    End Sub

    Sub RunCommand(ByVal SQL As String)
        InvWSS.Platform.SBODBExecNonQuerySQL("SBOCore", SQL)
    End Sub

    Sub CreateInitalFavorites(ByVal iUserRank As Integer, ByVal sUserID As String)
        Dim sSQL As String
        Dim nCounter As Integer = 0
        Dim tabDefaultFavorites As New DataTable

        InvWSS.GetData("SELECT * FROM tmDefaultFavorites WITH(NOLOCK) WHERE Rank = " & iUserRank, tabDefaultFavorites)

        For Each rDataRow As DataRow In tabDefaultFavorites.Rows
            nCounter += 1
            sSQL = "INSERT INTO tsUserPreference (UniversalAccountIdentifier, ObjectName, Property, DataType, [Value]) VALUES ('" & sUserID & "','Favorites','URL" & Format(nCounter, "00#") & "',0,'" & rDataRow.Item("ClassName") & "')"
            RunCommand(sSQL)
        Next
        tabDefaultFavorites.Dispose()
    End Sub

    Private Function Insert2020User() As String
        Dim i2020UserID As Integer = User2020Exists(txtUserID.Text)
        If i2020UserID > 0 Then
            'Exit Function
            Return Nothing
        End If

        Dim sDefault2020Store As String = ""
        Dim tStores As DataTable = Get2020UserStores()

        If Not IsNothing(tStores) AndAlso tStores.Rows.Count > 0 Then
            Dim row As DataRow = tStores.Rows(0)
            sDefault2020Store = row("STORE_ID")
            tStores.Dispose()
        End If

        If sDefault2020Store = String.Empty Then
            'Exit Function
            Return Nothing
        End If

        Dim db2020Con As New SqlConnection(ConString2020().ToString)
        Dim sResult As String = ""
        Try
            db2020Con.Open()
            'Creating a user in SBONet should create a user in the scheduler database but not an employee
            'created a new stored procedure in the SA-Scheuler database called CreateUserSBONet and calling that sp instead of CreateEmployeeSBONet
            'Bug 2635 2015-02-12 BT
            Dim cmdEmployee As New SqlCommand("CreateUserSBONet", db2020Con)
            cmdEmployee.CommandType = CommandType.StoredProcedure
            cmdEmployee.Parameters.Add("@UName", SqlDbType.VarChar)
            cmdEmployee.Parameters.Add("@PWord", SqlDbType.VarChar)
            cmdEmployee.Parameters.Add("@EMail", SqlDbType.VarChar)
            cmdEmployee.Parameters.Add("@StoreID", SqlDbType.VarChar)
            cmdEmployee.Parameters.Add("@FirstName", SqlDbType.VarChar)
            cmdEmployee.Parameters.Add("@LastName", SqlDbType.VarChar)
            cmdEmployee.Parameters("@UName").Value = txtUserID.Text
            cmdEmployee.Parameters("@PWord").Value = txtPassword.Text
            cmdEmployee.Parameters("@EMail").Value = txtEmailAddress.Text
            cmdEmployee.Parameters("@StoreID").Value = sDefault2020Store
            cmdEmployee.Parameters("@FirstName").Value = txtFirstName.Text
            cmdEmployee.Parameters("@LastName").Value = txtLastName.Text

            cmdEmployee.ExecuteNonQuery()
            db2020Con.Close()
            db2020Con.Dispose()
            update2020CanReopenSched()
        Catch ex As Exception
            sResult = ex.ToString
        End Try

        Return sResult
    End Function

    Private Sub Update2020UserStores()
        Dim i2020UserID As Integer
        i2020UserID = User2020Exists(txtUserID.Text)
        If i2020UserID > 0 Then
            Set2020UserStores(i2020UserID)
        End If
    End Sub

    Private Function Run2020Command(ByVal SQL As String) As String
        Dim dbConString As String = ConString2020().ToString
        Dim dbConnect As New SqlConnection(dbConString)
        Dim cmdSQL As SqlCommand
        Dim sResult As String = ""
        Try
            dbConnect.Open()
            cmdSQL = New SqlCommand(SQL, dbConnect)
            With cmdSQL
                .CommandTimeout = 0
                .ExecuteNonQuery()
                .Dispose()
            End With
            dbConnect.Close()
            dbConnect.Dispose()
        Catch ex As Exception
            sResult = ex.ToString
        End Try
        Return sResult
    End Function

    Private Sub Set2020UserStores(ByVal int2020UserID As Integer)
        Dim sSQL As String
        Dim sSQL_DefaultStore As String
        Dim row As DataRow
        If int2020UserID > 0 Then
            sSQL = "DELETE FROM USERS_STORES WHERE User_ID = " & int2020UserID
            Run2020Command(sSQL)
            Dim t2020Stores As DataTable = Get2020UserStores()

            If Not IsNothing(t2020Stores) AndAlso t2020Stores.Rows.Count > 0 Then
                For Each row In t2020Stores.Rows
                    sSQL = "INSERT INTO USERS_STORES (Store_ID, User_ID) VALUES ('" & row("STORE_ID") & "'," & int2020UserID & ")"
                    If Run2020Command(sSQL) = String.Empty Then
                        'LY 08OCT2014 BZ2460 - Added below code to set 2020 Users Default_Store
                        sSQL_DefaultStore = "UPDATE USERS SET Default_Store = '" & row("STORE_ID") & "' WHERE User_ID = " & int2020UserID
                        Run2020Command(sSQL_DefaultStore)
                    End If
                Next
            End If
        End If
    End Sub

    Private Function Get2020UserStores() As DataTable
        Dim sSQL As String
        Dim tTable As New DataTable
        Dim tPermittedStores As New DataTable
        Dim row As DataRow
        Dim sPermStores As String = "SELECT * FROM tmStore WHERE STOREID IN (SELECT tmStore.StoreID from WEBRPT_vjBinPermissions bp "
        sPermStores &= "INNER JOIN tmStore ON bp.UniversalStoreIdentifier = tmStore.UniversalStoreIdentifier "
        sPermStores &= "INNER JOIN tmNode ON tmStore.UniversalNodeIdentifier = tmNode.UniversalNodeIdentifier "
        sPermStores &= "WHERE bp.BinLevel = 1 AND tmNode.ActiveNode <> 0 "
        sPermStores &= "and bp.UniversalAccountIdentifier = '"
        sPermStores &= txtAccountID.Text
        sPermStores &= "') "
        InvWSS.GetData(sPermStores, tPermittedStores)
        Dim sStoreList As String = ""
        For Each row In tPermittedStores.Rows
            sStoreList &= IIf(Len(sStoreList) > 0, "," & row("StoreID"), row("StoreID"))
        Next

        sSQL = "select stos.STORE_ID from tmstore sto LEFT OUTER JOIN " & Session("DSS_Path") & ".[STORES] stos on LEFT(sto.UniversalNodeIdentifier,31) = stos.Store_ID "
        sSQL &= " where stos.Store_ID IS NOT NULL AND stos.StoreActive <> 0 "
        sSQL &= " AND sto.StoreID IN (" & sStoreList & ") "
        sSQL &= " ORDER BY sto.StoreNum"
        InvWSS.GetData(sSQL, tTable)
        Return tTable
    End Function

    Private Function StoreCount2020() As Integer
        Dim tTable As New DataTable
        Dim sSQL As String
        Dim iStoreCount As Integer = 0

        sSQL = "select COUNT(*) AS StoreCount from tmstore sto LEFT OUTER JOIN " & Session("DSS_Path") & ".[STORES] stos on LEFT(sto.UniversalNodeIdentifier,31) = stos.Store_ID "
        sSQL &= " where stos.Store_ID IS NOT NULL AND stos.StoreActive <> 0 "

        InvWSS.GetData(sSQL, tTable)
        For Each row As DataRow In tTable.Rows
            iStoreCount = row("StoreCount")
        Next
        Return iStoreCount
    End Function

    Private Function User2020Exists(ByVal s2020UserID As String) As Integer
        Dim dbConString As String = ConString2020().ToString

        Dim sSQL As String = "SELECT User_ID FROM USERS WITH(NOLOCK) WHERE UName = '" & s2020UserID & "'"
        Dim tTable As New DataTable
        Dim sResult As String = GetTable(dbConString, sSQL, tTable)
        Dim row As DataRow
        Dim iUserID As Integer
        If sResult = String.Empty Then
            For Each row In tTable.Rows
                iUserID = row("User_ID")
            Next
        End If
        Return iUserID
    End Function

    Private Function CanReopenSched(ByVal s2020UserID As String) As Boolean
        Dim bCanReopenSched As Integer
        Try
            Dim dbConString As String = ConString2020().ToString
            Dim sSQL As String = "SELECT CanReopenSched FROM USERS WITH(NOLOCK) WHERE UName = '" & s2020UserID & "'"
            Dim tTable As New DataTable
            Dim sResult As String = GetTable(dbConString, sSQL, tTable)
            Dim row As DataRow

            If sResult = String.Empty Then
                For Each row In tTable.Rows
                    bCanReopenSched = row("CanReopenSched")
                Next
            End If
        Catch ex As Exception

        End Try

        Return bCanReopenSched
    End Function

    Private Function ConString2020() As System.Data.Common.DbConnectionStringBuilder

        Dim sbConString As New System.Data.Common.DbConnectionStringBuilder
        sbConString.ConnectionString = Session("DSS_ConString")

        Return sbConString
    End Function

    Private Function GetTable(ByVal DBConString As String, ByVal SQLToRun As String, ByRef dbTable As DataTable) As String
        Dim SQLCon As New SqlConnection
        Dim SQLCmd As New SqlDataAdapter
        Dim sResult As String = ""
        Try
            SQLCon = New SqlConnection(DBConString)
            SQLCon.Open()
            SQLCmd = New SqlDataAdapter(SQLToRun, SQLCon)
            SQLCmd.Fill(dbTable)
            SQLCon.Close()
        Catch eGetDataTable As Exception
            sResult = eGetDataTable.Message
        Finally
            If Not SQLCmd Is Nothing Then
                SQLCmd.Dispose()
            End If
            If Not SQLCon Is Nothing Then
                SQLCon.Dispose()
            End If
        End Try
        Return sResult
    End Function

    Private Sub updateEmployeeSchedulerPassword()
        Dim i2020UserID As Integer
        Dim sSQL As New StringBuilder

        i2020UserID = User2020Exists(txtUserID.Text)

        If i2020UserID > 0 Then
            'Update the Password in the SA Scheduler database
            If Len(txtPassword.Text) > 0 Then
                sSQL.AppendLine("UPDATE ")
                sSQL.AppendLine("   [USERS] ")
                sSQL.AppendLine("SET ")
                sSQL.AppendLine("   [PWord] = '" & txtPassword.Text & "' ")
                sSQL.AppendLine("WHERE ")
                sSQL.AppendLine("   [User_ID] = " & i2020UserID)
                Run2020Command(sSQL.ToString())
            End If
            'Update the Staff Scheduler user info in SBONet
            updateSBONetSchedulerInformation()
        End If
    End Sub

    Private Sub update2020CanReopenSched()
        Dim i2020UserID As Integer
        Dim sSQL As New StringBuilder

        i2020UserID = User2020Exists(txtUserID.Text)

        If i2020UserID > 0 Then

            sSQL.AppendLine("UPDATE ")
            sSQL.AppendLine("   [USERS] ")
            sSQL.AppendLine("SET ")
            sSQL.AppendLine("   [CanReopenSched] = " & IIf(cb2020CanReopenSched.Checked, 1, 0))
            sSQL.AppendLine("WHERE ")
            sSQL.AppendLine("   [User_ID] = " & i2020UserID)
            Run2020Command(sSQL.ToString())
        End If
    End Sub

    Private Sub updateSBONetSchedulerInformation()
        Dim sSQL As New StringBuilder
        'Update the Password in the SA Scheduler database
        'DAF 2015-07-30 Sprint 1 / B-01999 / TK-01039
        Dim sCachedPassword As String = txtCachedPassword.Text
        If Len(txtUserID.Text) > 0 And Len(txtPassword.Text) > 0 Then
            sSQL.AppendLine("UPDATE ")
            sSQL.AppendLine("   [dbo].[tmAccount] ")
            sSQL.AppendLine("SET ")
            sSQL.AppendLine("   [StaffSchedulerUserID] = '" & txtUserID.Text & "' ")
            sSQL.AppendLine("   ,[StaffSchedulerPassword] = '" & txtPassword.Text & "' ")
            sSQL.AppendLine("WHERE ")
            sSQL.AppendLine("   [UserID] = '" & txtUserID.Text & "'")

            RunCommand(sSQL.ToString())
        End If

    End Sub

    'Pulls the employee password from the database to update the password in SA-Scheduler
    Private Sub getEmployeePassword()
        Dim sSQL As New StringBuilder
        Dim tPassword As New DataTable

        sSQL.AppendLine("SELECT ")
        sSQL.AppendLine("   [Password] ")
        sSQL.AppendLine("FROM ")
        sSQL.AppendLine("   tmAccount ")
        sSQL.AppendLine("WHERE ")
        sSQL.AppendLine("   UserID = '" & txtUserID.Text & "'")

        InvWSS.GetData(sSQL.ToString(), tPassword)

        txtPassword.Text = tPassword.Rows(0).Item(0).ToString()
    End Sub

    'Sets the user to inactive in SA-Scheduler
    Private Sub setSchedulerUserInActive()
        Dim i2020UserID As Integer
        Dim sSQL As New StringBuilder

        i2020UserID = User2020Exists(txtUserID.Text)

        If i2020UserID > 0 Then
            'Update the Password in the SA Scheduler database
            sSQL.AppendLine("UPDATE ")
            sSQL.AppendLine("   [USERS] ")
            sSQL.AppendLine("SET ")
            sSQL.AppendLine("   [Active] = 0 ")
            sSQL.AppendLine("WHERE ")
            sSQL.AppendLine("   [User_ID] = " & i2020UserID)
            Run2020Command(sSQL.ToString())

            'Update the Staff Scheduler user info in SBONet
            updateSBONetSchedulerInformation()

        End If
    End Sub

    'Sets the user to active in SA-Scheduler
    Private Sub setSchedulerUserActive()
        Dim i2020UserID As Integer
        Dim sSQL As New StringBuilder

        i2020UserID = User2020Exists(txtUserID.Text)

        If i2020UserID > 0 Then
            'Update the Password in the SA Scheduler database
            sSQL.AppendLine("UPDATE ")
            sSQL.AppendLine("   [USERS] ")
            sSQL.AppendLine("SET ")
            sSQL.AppendLine("   [Active] = 1 ")
            sSQL.AppendLine("WHERE ")
            sSQL.AppendLine("   [User_ID] = " & i2020UserID)
            Run2020Command(sSQL.ToString())

            'Update the Staff Scheduler user info in SBONet
            updateSBONetSchedulerInformation()

        End If
    End Sub

    Private Sub updateSchedulerEditPerm()
        Dim i2020UserID As Integer
        Dim sSQL As New StringBuilder

        i2020UserID = User2020Exists(txtUserID.Text)

        If i2020UserID > 0 Then
            'Update the Password in the SA Scheduler database
            sSQL.AppendLine("UPDATE ")
            sSQL.AppendLine("   [USERS] ")
            sSQL.AppendLine("SET ")
            sSQL.AppendLine("   [Edit_Labour_Guide] = " & IIf(cb2020EditGuide.Checked = True, 1, 0))
            sSQL.AppendLine("   ,[Edit_Salaries] = " & IIf(cb2020EditSalary.Checked = True, 1, 0))
            sSQL.AppendLine("WHERE ")
            sSQL.AppendLine("   [User_ID] = " & i2020UserID)
            Run2020Command(sSQL.ToString())

        End If
    End Sub

    'BDean: Story 1 // B-01999.3 // TK-01047
    Sub PasswordDeliveryHandler()

        'Get the email address that the password reset link should be emailed to
        Dim pwEmailAddress As String = GetPasswordDeliveryEmail()

        'If an email address is returned (the user selected an "email" option) --> Build/Send the email to the address
        If pwEmailAddress <> "" Then

            'Build the email body
            Dim emailBody As String = CreatePasswordResetEmail(txtFirstName.Text & " " & txtLastName.Text, txtUserID.Text, IIf(txtPhoneNumber.Text <> "", txtPhoneNumber.Text, "N/A"), calPWLinkExp.SelectedDate)

            'Send the email
            SendMessage(pwEmailAddress, emailBody, "SBOnet Password Reset Link", True)

            'BDean: This will call the commented out eManager mail send procedure at the bottom of the page - Was not needed, as we were able to get the above working
            'eManagerSend(pwEmailAddress, emailBody, "SBOnet Password Reset Link")

        End If

    End Sub

    'BDean: Story 1 // B-01999.3 // TK-01047
    Private Function GetPasswordDeliveryEmail() As String

        Dim pwDeliveryMethod As String = tbSelectedDeliveryMethod.Text
        Dim pwEmailAddress As String = ""

        Select Case pwDeliveryMethod

            Case "user"
                pwEmailAddress = txtEmailAddress.Text

            Case "alternate"
                pwEmailAddress = tbAlternateEmailAddress.Text

        End Select

        Return pwEmailAddress

    End Function

    Private Function CreatePasswordResetEmail(ByVal userFullName As String, ByVal userID As String, ByVal userPhone As String, ByVal expirationDate As String) As String

        Dim sbEmailContent As New StringBuilder
        Dim sServerName As String = Request.ServerVariables("SERVER_NAME")
        dim SServerPort As String = Request.ServerVariables("SERVER_PORT")
        If InStr(Request.ServerVariables("SERVER_NAME").ToLower, "localhost")
            sServerName = "http://" + sServerName
        Else
            sServerName = "https://" + sServerName
        End If

        IF Not ((SServerPort = 80 Or SServerPort = 443))
            sServerName += ":" & SServerPort
        End if

        With sbEmailContent
            .AppendLine("<!DOCTYPE html PUBLIC ""-//W3C//DTD XHTML 1.0 Transitional//EN"" ""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"">")
            .AppendLine("<html xmlns = ""http://www.w3.org/1999/xhtml"" >")
            .AppendLine("<head>")
            .AppendLine("<meta http-equiv=""Content-Type"" content=""text/html; charset=UTF-8"" />")
            .AppendLine("<div>")
            .AppendLine(userFullName & ",<br />")
            .AppendLine("<br /><br />")
            .AppendLine("You have requested to reset your SBOnet login credentials. Please use the hyperlink listed below to update your password.")
            .AppendLine("<br /><br />")
            .AppendLine("Organization: " & Session("OrganizationName") & "<br />")
            .AppendLine("UserID: " & userID & "<br />")
            .AppendLine("Phone #: " & userPhone)
            .AppendLine("<br /><br />")
            .AppendLine("Reset Link: <a href=""" & sServerName & "/PasswordReset.aspx?GUID=" & ViewState("PasswordGUID") & """>" & sServerName & "/PasswordReset.aspx?GUID=" & ViewState("PasswordGUID") & "</a>")
            .AppendLine("<br /><br />")
            .AppendLine("This reset link will expire on: " & expirationDate)
            .AppendLine("<br /><br />")
            .AppendLine("Thank you,<br />")
            .AppendLine("SBOnet Web Services Support Team")
            .AppendLine("</div>")
            .AppendLine("</head>")

        End With

        Return sbEmailContent.ToString

    End Function

    'BDean: Story 1 // B-01999.3 // TK-01047
    Sub SendMessage(ByVal recipient As String, ByVal msg As String, ByVal subject As String, Optional ByVal bHTML As Boolean = True)

        'Dim objMsg As New MailMessage("<EMAIL>", recipient) With {
        '    .From = New MailAddress("<EMAIL>", "SBOnet Password Reset"),
        '    .Body = msg,
        '    .Subject = subject,
        '.IsBodyHtml = bHTML
        '}

        'Dim smtpClient As New SmtpClient("***********") With {
        '    .UseDefaultCredentials = False,
        '    .Credentials = New NetworkCredential("Sboalerts", "Alerts006!")
        '}

        'smtpClient.Send(objMsg)
        WebServicesAddons.SendGridEmailService.SendMail(recipient, subject, msg, True)

    End Sub

End Class
