﻿Imports System
Imports System.Data
Imports System.Data.SqlClient
Imports System.IO

Public Class Scheduler_LaborMatrixOptions
    '2016-08-25 lay sprint 23 // B-02495 Add functionality to save Thumbprint and LastModified when Submit/Save is
    'processed.  Thumprint and LastModfied are stored in SA-Scheduler.Store_Scheduler_Settings table.

    '2016-07-29 jjc Sprint 22 // D-01142 // TK-02203  (R-01269) Move populating of tbIPT.Text into check for existance
    'of data in [Store_Scheduler_Settings] (was previously in a check for data in [tmStoreLabor]) and default to False
    'if no Data exists

    '2017-10-05 jjc Sprint 40 // B-03117 // TK-04009 Labor Matrix 4.1
    '   Add Checkboxes :  "Warewash", "Electronic Salad Spinner"
    '   Modify Productivity For New "Expert", "Good", "Average", "Beginner" nomenclature
    '   Add New entry for Weekend Productivity
    '   Modify data loading And saving procedures accordingly.

    '2017-11-22 jjc Sprint 42 // B-03194 // TK-04250 Add Pre-Cut Bacon 
    '   And reversing it cause Wendy's isn't ready yet.

    '2020-01-20 jjc Sprint 76 SBOD-1350/1383 Adding Pre-cut lettuce

    '2020-03-20 jjc Sprint 79 Adding option for DT only operations

    '2021-05-17 jjc Rev 4 Sprint 79w67 SOBD-2240/2241 Updates to Wendy's LM (4.3?)
    '   Adding Matrix version
    '   Adding Prep lable Printer
    '   Making Back-Office Forecast/Prep, Back-Office Inventory, Back-Office Labor Scheduler, Electric Salad Spinner, Pre-chopped lettuce, and Kiosk Hidden for GuideID > 1
    '   Making Prep Label Printer Hidden for GuideID < 2

    '2021-06-28 jjc Sprint 79w67 SOBD-2240/2241 Updates to Wendy's LM (4.3?) Prep label printer and re-runing SetOptionAvailabitlity after save
    '2021-11-11 jjc Sprint 79w68 SBOD-2240/2241 Adding Opeations Tablet

    Inherits System.Web.UI.Page
    Dim InvWSS As InventoryWebSiteServices.InventoryServices

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        'If the user is not logged in --> Redirect to the login page
        If Len(Session("UserID")) = 0 Then
            Response.Redirect("login.aspx?timeout=1")
        End If

        InvWSS = Session("IWSS")

        'Check to see if the user is logged into "LukeTest"
        If Not IsPostBack Then

            'Populate the "Restaurant Information" dropdown lists
            PopRestaurantInformation()

            'Populate the "Store" list & select the user's "default" store
            PopStores()

            'Select items
            LoadSelections()
            cbUseDSS.Enabled = False
            cbUseIPT.Enabled = False
        End If

    End Sub

    Private Sub PopRestaurantInformation()

        Try

            Dim sbSQL As New StringBuilder
            Dim tabResults As New DataTable

            With sbSQL

                .AppendLine("SELECT")
                .AppendLine("	 tmE.[Tag]")
                .AppendLine("	,tdE.[Key]")
                .AppendLine("	,tdE.[Value]")
                .AppendLine("FROM [dbo].[tmEnum] AS [tmE] WITH(NOLOCK)")
                .AppendLine("	INNER JOIN [dbo].[tdEnum] AS [tdE] WITH(NOLOCK)")
                .AppendLine("		ON tmE.[Tag] = tdE.[Tag]")
                .AppendLine("WHERE tmE.[Tag] IN ('CounterType', 'CrewProductivity', 'DiningRoomDrinksType', 'DiningRoomFloorType', 'DiningRoomType', 'GrillType', 'Kiosk', 'Digital', 'KitchenType', 'StoreType', 'DigitalType', 'OrderTechnology', 'DriveThruMix')")
                .AppendLine("")
                .AppendLine("UNION")
                .AppendLine("")
                .AppendLine("SELECT")
                .AppendLine("	 'GuideID' AS [Tag]")
                .AppendLine("	,[GuideID] AS [Key]")
                .AppendLine("	,[GuideName] AS [Value]")
                .AppendLine("FROM [dbo].[Labor_tmGuideVersion]")
                .AppendLine("ORDER BY")
                .AppendLine("	 [Tag]")
                .AppendLine("	,[Key]")
                .AppendLine("")

            End With

            InvWSS.GetData(sbSQL.ToString(), tabResults)

            If tabResults.Rows.Count > 0 Then

                'Loop through each row, and add the items to the appropriate dropdown lists
                For Each row As DataRow In tabResults.Rows

                    Select Case row.Item("Tag")

                        Case "CounterType"
                            ddlCounterType.Items.Add(New ListItem(row.Item("Value"), row.Item("Key")))

                        Case "StoreType"
                            ddlPUWType.Items.Add(New ListItem(row.Item("Value"), row.Item("Key")))

                        Case "KitchenType"
                            ddlKitchen.Items.Add(New ListItem(row.Item("Value"), row.Item("Key")))

                        Case "DiningRoomType"
                            ddlDiningRoom.Items.Add(New ListItem(row.Item("Value"), row.Item("Key")))

                        Case "DiningRoomFloorType"
                            ddlFlooring.Items.Add(New ListItem(row.Item("Value"), row.Item("Key")))

                        Case "DiningRoomDrinksType"
                            ddlDrinks.Items.Add(New ListItem(row.Item("Value"), row.Item("Key")))

                        Case "GrillType"
                            ddlGrill.Items.Add(New ListItem(row.Item("Value"), row.Item("Key")))

                        Case "Kiosk"
                            ddlKiosk.Items.Add(New ListItem(row.Item("Value"), row.Item("Key")))

                        Case "Digital"
                            ddlDigital.Items.Add(New ListItem(row.Item("Value"), row.Item("Key")))

                        Case "GuideID"
                            ddlMatrixVersion.Items.Add(New ListItem(row.Item("Value"), row.Item("Key")))

                        Case "DigitalType"
                            ddlDigitalType.Items.Add(New ListItem(row.Item("Value"), row.Item("Key")))

                        Case "OrderTechnology"
                            ddlOrderTechnology.Items.Add(New ListItem(row.Item("Value"), row.Item("Key")))

                        Case "DriveThruMix"
                            ddlDriveThruMix.Items.Add(New ListItem(row.Item("Value"), row.Item("Key")))

                        Case "CrewProductivity"
                            ddlProductivityDP1.Items.Add(New ListItem(row.Item("Value"), row.Item("Key")))
                            ddlProductivityDP23.Items.Add(New ListItem(row.Item("Value"), row.Item("Key")))
                            ddlProductivityDP45.Items.Add(New ListItem(row.Item("Value"), row.Item("Key")))
                            ddlProductivityDP6.Items.Add(New ListItem(row.Item("Value"), row.Item("Key")))
                            ddlProductivityDP1Wknd.Items.Add(New ListItem(row.Item("Value"), row.Item("Key")))
                            ddlProductivityDP23Wknd.Items.Add(New ListItem(row.Item("Value"), row.Item("Key")))
                            ddlProductivityDP45Wknd.Items.Add(New ListItem(row.Item("Value"), row.Item("Key")))
                            ddlProductivityDP6Wknd.Items.Add(New ListItem(row.Item("Value"), row.Item("Key")))

                    End Select

                Next

            End If

            sbSQL = New StringBuilder

            With sbSQL


            End With

            InvWSS.GetData(sbSQL.ToString(), tabResults)

        Catch

            'Eat it...

        End Try

    End Sub

    Private Sub PopStores()

        Try

            Dim sbSQL As New StringBuilder
            Dim tabStores As New DataTable

            With sbSQL

                .AppendLine("DECLARE @Username AS VARCHAR(25)")
                .AppendLine("SET @Username = '" & GetUsername() & "'")
                .AppendLine("")
                .AppendLine("SELECT")
                .AppendLine("	 s.[Store_ID]")
                .AppendLine("	,s.[Store_Name]")
                .AppendLine("	,(")
                .AppendLine("		CASE ")
                .AppendLine("			WHEN s.[Store_ID] = u.[Default_Store] THEN 1")
                .AppendLine("			ELSE 0")
                .AppendLine("		END")
                .AppendLine("	 ) AS [Default_Store]")
                .AppendLine("FROM")
                .AppendLine("	{0}.[STORES] AS [s]")
                .AppendLine("	INNER JOIN {0}.[USERS_STORES] AS [us]")
                .AppendLine("		ON s.[Store_ID] = us.[Store_ID]")
                .AppendLine("	INNER JOIN {0}.[USERS] AS [u]")
                .AppendLine("		ON us.[User_ID] = u.[User_ID]")
                .AppendLine("WHERE")
                .AppendLine("	u.[UName] = @Username")
                .AppendLine("	AND s.[StoreActive] = 1")
                .AppendLine("ORDER BY")
                .AppendLine("   s.[Store_Name]")

            End With

            InvWSS.GetData(String.Format(sbSQL.ToString(), Session("DSS_Path")), tabStores)

            If tabStores.Rows.Count > 0 Then

                'Find the user's "Default" store
                Dim numRows As Integer = tabStores.Rows.Count - 1
                Dim defaultStore As String = ""

                For Each row As DataRow In tabStores.Rows

                    If row.Item("Default_Store") = 1 Then
                        defaultStore = row.Item("Store_ID")
                    End If

                Next

                'Populate the "Source" dropdownlist, and select the user's "Default" store
                With ddlStore
                    .DataSource = tabStores
                    .DataValueField = "Store_ID"
                    .DataTextField = "Store_Name"
                    .DataBind()
                    .SelectedValue = defaultStore
                End With

            End If

        Catch ex As Exception

            'Eat it...

        End Try

    End Sub

    Private Sub LoadSelections()

        Try

            Dim sbIPT As New StringBuilder
            Dim tabIPT As New DataTable

            With sbIPT

                .AppendLine("SELECT")
                .AppendLine("	[UseIPTLaborMatrix],[Thumbprint],[LastModified]")
                .AppendLine("FROM")
                .AppendLine("	{0}.[Store_Scheduler_Settings] WITH(NOLOCK)")
                .AppendLine("WHERE")
                .AppendLine("	[Store_ID] = '" & ddlStore.SelectedValue & "'")

            End With

            InvWSS.GetData(String.Format(sbIPT.ToString(), Session("DSS_Path")), tabIPT)

            If tabIPT.Rows.Count > 0 Then

                'Use IPT Labor Matrix
                If Not IsDBNull(tabIPT.Rows(0).Item("UseIPTLaborMatrix")) Then
                    cbUseIPT.Checked = tabIPT.Rows(0).Item("UseIPTLaborMatrix")
                Else
                    cbUseIPT.Checked = False
                End If


                If Not IsDBNull(tabIPT.Rows(0).Item("Thumbprint")) Then
                    ShowLastUser(tabIPT.Rows(0).Item("Thumbprint"), CStr(tabIPT.Rows(0).Item("LastModified")))
                Else
                    lblLastUpdate.Text = ""
                End If
            End If


            Dim sbSQL As New StringBuilder
            Dim tabSelections As New DataTable

            With sbSQL

                .AppendLine("DECLARE @dss_store_id AS VARCHAR(31) = '" & ddlStore.SelectedValue & "'")
                .AppendLine("")
                .AppendLine("SELECT")
                .AppendLine("	 tmSL.[StoreID]")
                .AppendLine("	,tmSL.[GuideID]")
                .AppendLine("	,tmSL.[ExchangeRate]")
                .AppendLine("	,tmSL.[CounterType]")
                .AppendLine("	,tmSL.[StoreType]")
                .AppendLine("	,tmSL.[KitchenType]")
                .AppendLine("	,tmSL.[DiningRoomType]")
                .AppendLine("	,tmSL.[DiningRoomFloorType]")
                .AppendLine("	,tmSL.[DiningRoomDrinksType]")
                .AppendLine("	,tmSL.[GrillType]")
                .AppendLine("	,tmSL.[Powersoak]")
                .AppendLine("	,tmSL.[CashScales]")
                .AppendLine("	,tmSL.[GreaseExtraction]")
                .AppendLine("	,tmSL.[BillValidationSafe]")
                .AppendLine("	,tmSL.[BackOfficeForecastPrep]")
                .AppendLine("	,tmSL.[BackOfficeInventory]")
                .AppendLine("	,tmSL.[BackOfficeLaborScheduler]")
                .AppendLine("	,tmSL.[CrewProductivityDP1]")
                .AppendLine("	,tmSL.[CrewProductivityDP2]")
                .AppendLine("	,tmSL.[CrewProductivityDP3]")
                .AppendLine("	,tmSL.[CrewProductivityDP4]")
                .AppendLine("	,tmSL.[CrewProductivityDP1_Wknd]")
                .AppendLine("	,tmSL.[CrewProductivityDP2_Wknd]")
                .AppendLine("	,tmSL.[CrewProductivityDP3_Wknd]")
                .AppendLine("	,tmSL.[CrewProductivityDP4_Wknd]")
                .AppendLine("	,tmSL.[Kiosk]")
                .AppendLine("	,tmSL.[DigitalType]")
                .AppendLine("	,tmSL.[OrderTechnology]")
                .AppendLine("	,tmSL.[DriveThruMix]")
                .AppendLine("	,tmSL.[WareWash]")
                .AppendLine("	,tmSL.[ElectricSaladSpinner]")
                .AppendLine("	,tmSL.[PreCutBacon]")
                .AppendLine("	,tmSL.[PreChopLettuce]")
                .AppendLine("	,tmSL.[EmergencyDTOnly]")
                .AppendLine("	,tmSL.[PrepLabelPrinter]")
                .AppendLine("	,tmSL.[OperationsTablet]")
                .AppendLine("	,tmSL.[LettuceTest]")
                .AppendLine("	,tmSL.[AmbientBacon]")
                .AppendLine("	,tmSI.[DoSASExport]")
                .AppendLine("FROM")
                .AppendLine("	[dbo].[tmStore] AS [tmS] WITH(NOLOCK)")
                .AppendLine("	LEFT JOIN [dbo].[tmStoreLabor] AS [tmSL] WITH(NOLOCK)")
                .AppendLine("		ON tmS.[StoreID] = tmSL.[StoreID]")
                .AppendLine("	LEFT OUTER JOIN [dbo].[tmStoreInvt] AS [tmSI] WITH(NOLOCK)")
                .AppendLine("		ON tmS.[StoreID] = tmSI.[StoreID]")
                .AppendLine("WHERE")
                .AppendLine("	tms.DSS_Store_ID = @dss_store_id")

            End With

            InvWSS.GetData(sbSQL.ToString(), tabSelections)

            If tabSelections.Rows.Count > 0 Then

                Dim row As DataRow = tabSelections.Rows(0)

                cbUseDSS.Checked = row.Item("DoSASExport")
                If Not IsDBNull(row.Item("CrewProductivityDP1")) Then
                    'Matrix Version
                    ddlMatrixVersion.SelectedValue = row.Item("GuideID")

                    'Restaurant Information
                    ddlCounterType.SelectedValue = row.Item("CounterType")
                    ddlPUWType.SelectedValue = row.Item("StoreType")
                    ddlKitchen.SelectedValue = row.Item("KitchenType")
                    ddlDiningRoom.SelectedValue = row.Item("DiningRoomType")
                    ddlFlooring.SelectedValue = row.Item("DiningRoomFloorType")
                    ddlDrinks.SelectedValue = row.Item("DiningRoomDrinksType")
                    ddlGrill.SelectedValue = row.Item("GrillType")
                    ddlKiosk.SelectedValue = row.Item("Kiosk")
                    ddlDigital.SelectedValue = row.Item("Kiosk")
                    ddlDigitalType.SelectedValue = row.Item("DigitalType")
                    ddlOrderTechnology.SelectedValue = row.Item("OrderTechnology")
                    ddlDriveThruMix.SelectedValue = row.Item("DriveThruMix")

                    'Productivity Enhancements
                    cblProductivityEnhancements.Items.FindByText("Powersoak").Selected = row.Item("Powersoak")
                    cblProductivityEnhancements.Items.FindByText("Cash Scales").Selected = row.Item("CashScales")
                    cblProductivityEnhancements.Items.FindByText("Grease Extraction").Selected = row.Item("GreaseExtraction")
                    cblProductivityEnhancements.Items.FindByText("Bill Validation Safe").Selected = row.Item("BillValidationSafe")
                    cblProductivityEnhancements.Items.FindByText("Back-Office Forecast/Prep").Selected = row.Item("BackOfficeForecastPrep")
                    cblProductivityEnhancements.Items.FindByText("Back-Office Inventory").Selected = row.Item("BackOfficeInventory")
                    cblProductivityEnhancements.Items.FindByText("Back-Office Labor Scheduler").Selected = row.Item("BackOfficeLaborScheduler")
                    cblProductivityEnhancements.Items.FindByText("WareWash").Selected = row.Item("WareWash")
                    cblProductivityEnhancements.Items.FindByText("Electric Salad Spinner").Selected = row.Item("ElectricSaladSpinner")
                    cblProductivityEnhancements.Items.FindByText("Pre-chopped lettuce").Selected = row.Item("PreChopLettuce")
                    cblProductivityEnhancements.Items.FindByText("Prep Label Printer").Selected = row.Item("PrepLabelPrinter")
                    cblProductivityEnhancements.Items.FindByText("Operations Tablet").Selected = row.Item("OperationsTablet")
                    cblProductivityEnhancements.Items.FindByText("Lettuce Test").Selected = row.Item("LettuceTest")
                    cblProductivityEnhancements.Items.FindByText("Ambient Bacon").Selected = row.Item("AmbientBacon")

                    'Holding off on Bacon for now per wendys
                    'cblProductivityEnhancements.Items.FindByText("Pre Cut Bacon").Selected = row.Item("PreCutBacon")

                    'Exchange Rate
                    tbExchangeRate.Text = FormatNumber(row.Item("ExchangeRate"), 2)

                    'Crew Productivity
                    'tbDP1.Text = row.Item("CrewProductivityDP1")
                    'tbDP2_3.Text = row.Item("CrewProductivityDP2")
                    'tbDP4_5.Text = row.Item("CrewProductivityDP3")
                    'tbDP6.Text = row.Item("CrewProductivityDP4")

                    ddlProductivityDP1.SelectedValue = row.Item("CrewProductivityDP1")
                    ddlProductivityDP23.SelectedValue = row.Item("CrewProductivityDP2")
                    ddlProductivityDP45.SelectedValue = row.Item("CrewProductivityDP3")
                    ddlProductivityDP6.SelectedValue = row.Item("CrewProductivityDP4")
                    ddlProductivityDP1Wknd.SelectedValue = row.Item("CrewProductivityDP1_Wknd")
                    ddlProductivityDP23Wknd.SelectedValue = row.Item("CrewProductivityDP2_Wknd")
                    ddlProductivityDP45Wknd.SelectedValue = row.Item("CrewProductivityDP3_Wknd")
                    ddlProductivityDP6Wknd.SelectedValue = row.Item("CrewProductivityDP4_Wknd")
                Else
                    tbExchangeRate.Text = "1.00"
                End If


            Else

                'Exchange Rate
                tbExchangeRate.Text = "1.00"

            End If

            SetOptionAvailabitlity()

        Catch

            'Eat it...

        End Try

    End Sub

    Private Sub SetOptionAvailabitlity()
        Try

            Select Case ddlMatrixVersion.SelectedValue
                Case 1 '4.2
                    ddlKiosk.Enabled = True
                    divKiosk.Visible = True
                    ddlDigital.Enabled = False
                    divDigital.Visible = False
                    ddlDigitalType.Enabled = False
                    divDigitalType.Visible = False
                    ddlOrderTechnology.Enabled = False
                    divOrderTechnology.Visible = False
                    ddlDriveThruMix.Enabled = False
                    divDriveThruMix.Visible = False
                    cblProductivityEnhancements.Items.FindByText("Back-Office Forecast/Prep").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Back-Office Forecast/Prep").Attributes.Remove("class")
                    cblProductivityEnhancements.Items.FindByText("Back-Office Inventory").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Back-Office Inventory").Attributes.Remove("class")
                    cblProductivityEnhancements.Items.FindByText("Back-Office Labor Scheduler").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Back-Office Labor Scheduler").Attributes.Remove("class")
                    cblProductivityEnhancements.Items.FindByText("Electric Salad Spinner").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Electric Salad Spinner").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Pre-chopped lettuce").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Pre-chopped lettuce").Attributes.Remove("class")
                    cblProductivityEnhancements.Items.FindByText("Prep Label Printer").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Prep Label Printer").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Operations Tablet").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Operations Tablet").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Powersoak").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Grease Extraction").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("WareWash").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Lettuce Test").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Lettuce Test").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Ambient Bacon").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Ambient Bacon").Attributes.Add("class", "hideme")
                Case 2, 3 '4.3
                    ddlKiosk.Enabled = False
                    divKiosk.Visible = False
                    ddlDigital.Enabled = False
                    divDigital.Visible = False
                    ddlDigitalType.Enabled = False
                    divDigitalType.Visible = False
                    ddlOrderTechnology.Enabled = False
                    divOrderTechnology.Visible = False
                    ddlDriveThruMix.Enabled = False
                    divDriveThruMix.Visible = False
                    cblProductivityEnhancements.Items.FindByText("Back-Office Forecast/Prep").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Back-Office Forecast/Prep").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Back-Office Inventory").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Back-Office Inventory").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Back-Office Labor Scheduler").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Back-Office Labor Scheduler").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Electric Salad Spinner").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Electric Salad Spinner").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Pre-chopped lettuce").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Pre-chopped lettuce").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Prep Label Printer").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Prep Label Printer").Attributes.Remove("class")
                    cblProductivityEnhancements.Items.FindByText("Operations Tablet").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Operations Tablet").Attributes.Remove("class")
                    cblProductivityEnhancements.Items.FindByText("Powersoak").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Grease Extraction").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("WareWash").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Lettuce Test").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Lettuce Test").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Ambient Bacon").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Ambient Bacon").Attributes.Add("class", "hideme")
                Case 4, 5 '4.3.1
                    ddlKiosk.Enabled = False
                    divKiosk.Visible = False
                    ddlDigital.Enabled = False
                    divDigital.Visible = False
                    ddlDigitalType.Enabled = False
                    divDigitalType.Visible = False
                    ddlOrderTechnology.Enabled = False
                    divOrderTechnology.Visible = False
                    ddlDriveThruMix.Enabled = False
                    divDriveThruMix.Visible = False
                    cblProductivityEnhancements.Items.FindByText("Back-Office Forecast/Prep").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Back-Office Forecast/Prep").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Back-Office Inventory").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Back-Office Inventory").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Back-Office Labor Scheduler").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Back-Office Labor Scheduler").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Electric Salad Spinner").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Electric Salad Spinner").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Pre-chopped lettuce").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Pre-chopped lettuce").Attributes.Add("class", "hideme")

                    cblProductivityEnhancements.Items.FindByText("Powersoak").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Powersoak").Attributes.Remove("class")
                    'Cash Scales
                    cblProductivityEnhancements.Items.FindByText("Grease Extraction").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Grease Extraction").Attributes.Remove("class")
                    'Bill Validation Safe
                    cblProductivityEnhancements.Items.FindByText("Prep Label Printer").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Prep Label Printer").Attributes.Remove("class")
                    cblProductivityEnhancements.Items.FindByText("WareWash").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("WareWash").Attributes.Remove("class")
                    cblProductivityEnhancements.Items.FindByText("Operations Tablet").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Operations Tablet").Attributes.Remove("class")
                    cblProductivityEnhancements.Items.FindByText("Lettuce Test").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Lettuce Test").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Ambient Bacon").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Ambient Bacon").Attributes.Add("class", "hideme")

                Case 6, 7, 8, 9 '6-4.4(US), 7-4.4 Canada(Future?) ?No, 8-4.5(US), 9-4.5 Canada(Future?)
                    ddlKiosk.Enabled = False
                    divKiosk.Visible = False
                    ddlDigital.Enabled = True
                    divDigital.Visible = True
                    ddlDigitalType.Enabled = False
                    divDigitalType.Visible = False
                    ddlOrderTechnology.Enabled = False
                    divOrderTechnology.Visible = False
                    ddlDriveThruMix.Enabled = False
                    divDriveThruMix.Visible = False
                    cblProductivityEnhancements.Items.FindByText("Back-Office Forecast/Prep").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Back-Office Forecast/Prep").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Back-Office Inventory").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Back-Office Inventory").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Back-Office Labor Scheduler").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Back-Office Labor Scheduler").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Electric Salad Spinner").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Electric Salad Spinner").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Pre-chopped lettuce").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Pre-chopped lettuce").Attributes.Add("class", "hideme")

                    cblProductivityEnhancements.Items.FindByText("Powersoak").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Powersoak").Attributes.Remove("class")
                    'Cash Scales
                    cblProductivityEnhancements.Items.FindByText("Grease Extraction").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Grease Extraction").Attributes.Remove("class")
                    'Bill Validation Safe
                    cblProductivityEnhancements.Items.FindByText("Prep Label Printer").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Prep Label Printer").Attributes.Remove("class")
                    cblProductivityEnhancements.Items.FindByText("WareWash").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("WareWash").Attributes.Remove("class")
                    cblProductivityEnhancements.Items.FindByText("Operations Tablet").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Operations Tablet").Attributes.Remove("class")
                    cblProductivityEnhancements.Items.FindByText("Lettuce Test").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Lettuce Test").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Ambient Bacon").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Ambient Bacon").Attributes.Add("class", "hideme")

                Case 10, 11 '10-4.5.1(US), 11-4.5 Canada(Future?)
                    ddlKiosk.Enabled = False
                    divKiosk.Visible = False
                    ddlDigital.Enabled = False
                    divDigital.Visible = False
                    ddlDigitalType.Enabled = True
                    divDigitalType.Visible = True
                    ddlOrderTechnology.Enabled = True
                    divOrderTechnology.Visible = True
                    ddlDriveThruMix.Enabled = True
                    divDriveThruMix.Visible = True
                    cblProductivityEnhancements.Items.FindByText("Back-Office Forecast/Prep").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Back-Office Forecast/Prep").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Back-Office Inventory").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Back-Office Inventory").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Back-Office Labor Scheduler").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Back-Office Labor Scheduler").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Electric Salad Spinner").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Electric Salad Spinner").Attributes.Add("class", "hideme")
                    cblProductivityEnhancements.Items.FindByText("Pre-chopped lettuce").Enabled = False
                    cblProductivityEnhancements.Items.FindByText("Pre-chopped lettuce").Attributes.Add("class", "hideme")

                    cblProductivityEnhancements.Items.FindByText("Powersoak").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Powersoak").Attributes.Remove("class")
                    'Cash Scales
                    cblProductivityEnhancements.Items.FindByText("Grease Extraction").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Grease Extraction").Attributes.Remove("class")
                    'Bill Validation Safe
                    cblProductivityEnhancements.Items.FindByText("Prep Label Printer").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Prep Label Printer").Attributes.Remove("class")
                    cblProductivityEnhancements.Items.FindByText("WareWash").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("WareWash").Attributes.Remove("class")
                    cblProductivityEnhancements.Items.FindByText("Operations Tablet").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Operations Tablet").Attributes.Remove("class")
                    cblProductivityEnhancements.Items.FindByText("Lettuce Test").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Lettuce Test").Attributes.Remove("class")
                    cblProductivityEnhancements.Items.FindByText("Ambient Bacon").Enabled = True
                    cblProductivityEnhancements.Items.FindByText("Ambient Bacon").Attributes.Remove("class")

            End Select

        Catch

            'Eat it...

        End Try
    End Sub


    Public Sub StoreSelectionChanged(sender As Object, e As EventArgs)

        ResetItems()
        LoadSelections()

    End Sub

    Public Sub MatrixSelectionChanged(sender As Object, e As EventArgs)

        SetOptionAvailabitlity()

    End Sub

    Private Sub ResetItems()

        'Matrix Vesrion
        ddlMatrixVersion.ClearSelection()

        'Restaurant Information
        ddlCounterType.ClearSelection()
        ddlPUWType.ClearSelection()
        ddlKitchen.ClearSelection()
        ddlDiningRoom.ClearSelection()
        ddlFlooring.ClearSelection()
        ddlDrinks.ClearSelection()
        ddlGrill.ClearSelection()
        ddlKiosk.ClearSelection()
        ddlDigital.ClearSelection()
        ddlDigitalType.ClearSelection()
        ddlOrderTechnology.ClearSelection()
        ddlDriveThruMix.ClearSelection()

        'Productivity Enhancements
        cblProductivityEnhancements.ClearSelection()

        'I don't think we need 7 of these
        'cblProductivityEnhancements.ClearSelection()
        'cblProductivityEnhancements.ClearSelection()
        'cblProductivityEnhancements.ClearSelection()
        'cblProductivityEnhancements.ClearSelection()
        'cblProductivityEnhancements.ClearSelection()
        'cblProductivityEnhancements.ClearSelection()

        'Exchange Rate
        tbExchangeRate.Text = "1.00"

        'Crew Productivity
        'tbDP1.Text = ""
        'tbDP2_3.Text = ""
        'tbDP4_5.Text = ""
        'tbDP6.Text = ""

        ddlProductivityDP1.ClearSelection()
        ddlProductivityDP23.ClearSelection()
        ddlProductivityDP45.ClearSelection()
        ddlProductivityDP6.ClearSelection()
        ddlProductivityDP1Wknd.ClearSelection()
        ddlProductivityDP23Wknd.ClearSelection()
        ddlProductivityDP45Wknd.ClearSelection()
        ddlProductivityDP6Wknd.ClearSelection()

    End Sub

    Public Sub btnSubmit_Click()

        Submit_Form()

    End Sub

    Private Sub Submit_Form()

        Try

            ShowLastUser(Session("AccountID"), Now.ToString)

            Dim sbSQL As New StringBuilder
            Dim tabMerge As New DataTable
            Dim sboNet_StoreID As Integer = GetSBOnetStoreID()

            With sbSQL

                .AppendLine("MERGE [dbo].[tmStoreLabor] AS TARGET")
                .AppendLine("USING")
                .AppendLine("(")
                .AppendLine("	SELECT")
                .AppendLine("		 " & sboNet_StoreID & " AS [StoreID]")
                .AppendLine("		," & ddlMatrixVersion.SelectedValue & " AS [GuideID]")
                .AppendLine("		," & FormatNumber(tbExchangeRate.Text, 2) & " AS [ExchangeRate]")
                .AppendLine("		," & ddlCounterType.SelectedValue & " AS [CounterType]")
                .AppendLine("		," & ddlPUWType.SelectedValue & " AS [StoreType]")
                .AppendLine("		," & ddlKitchen.SelectedValue & " AS [KitchenType]")
                .AppendLine("		," & ddlDiningRoom.SelectedValue & " AS [DiningRoomType]")
                .AppendLine("		," & ddlFlooring.SelectedValue & " AS [DiningRoomFloorType]")
                .AppendLine("		," & ddlDrinks.SelectedValue & " AS [DiningRoomDrinksType]")
                .AppendLine("		," & ddlGrill.SelectedValue & " AS [GrillType]")
                Select Case ddlMatrixVersion.SelectedValue
                    Case 1 '4.2
                        .AppendLine("		," & ddlKiosk.SelectedValue & " AS [Kiosk]")
                    Case 6, 7, 8, 9 '6-4.4(US), 7-4.4 Canada(Future?), 8-4.5(US), 9-4.5 Canada(Future?)
                        .AppendLine("		," & ddlDigital.SelectedValue & " AS [Kiosk]") 'Kiosk was retired digital replaced it, the calculations are similar so just rebranding the Kiosk column on the backend
                    Case Else
                        .AppendLine("		,0 AS [Kiosk]") 'Kiosk/Digital not used for Versions 4.3, 4.3.1, or later than 4.5
                End Select
                .AppendLine("		," & ddlDigitalType.SelectedValue & " AS [DigitalType]")
                .AppendLine("		," & ddlOrderTechnology.SelectedValue & " AS [OrderTechnology]")
                .AppendLine("		," & ddlDriveThruMix.SelectedValue & " AS [DriveThruMix]")

                .AppendLine("		," & IIf(cblProductivityEnhancements.Items.FindByText("Powersoak").Selected, 1, 0) & " AS [Powersoak]")
                .AppendLine("		," & IIf(cblProductivityEnhancements.Items.FindByText("Cash Scales").Selected, 1, 0) & " AS [CashScales]")
                .AppendLine("		," & IIf(cblProductivityEnhancements.Items.FindByText("Grease Extraction").Selected, 1, 0) & " AS [GreaseExtraction]")
                .AppendLine("		," & IIf(cblProductivityEnhancements.Items.FindByText("Bill Validation Safe").Selected, 1, 0) & " AS [BillValidationSafe]")
                .AppendLine("		," & IIf(cblProductivityEnhancements.Items.FindByText("Back-Office Forecast/Prep").Selected, 1, 0) & " AS [BackOfficeForecastPrep]")
                .AppendLine("		," & IIf(cblProductivityEnhancements.Items.FindByText("Back-Office Inventory").Selected, 1, 0) & " AS [BackOfficeInventory]")
                .AppendLine("		," & IIf(cblProductivityEnhancements.Items.FindByText("Back-Office Labor Scheduler").Selected, 1, 0) & " AS [BackOfficeLaborScheduler]")
                .AppendLine("		," & IIf(cblProductivityEnhancements.Items.FindByText("WareWash").Selected, 1, 0) & " AS [WareWash]")
                .AppendLine("		," & IIf(cblProductivityEnhancements.Items.FindByText("Electric Salad Spinner").Selected, 1, 0) & " AS [ElectricSaladSpinner]")
                'Holding off on Pre-cut bacon for now per wendys
                '.AppendLine("		," & IIf(cblProductivityEnhancements.Items.FindByText("Pre Cut Bacon").Selected, 1, 0) & " AS [PreCutBacon]")
                .AppendLine("		,0 As [PreCutBacon]")
                .AppendLine("		," & IIf(cblProductivityEnhancements.Items.FindByText("Pre-chopped lettuce").Selected, 1, 0) & " AS [PreChopLettuce]")
                .AppendLine("		," & IIf(cblProductivityEnhancements.Items.FindByText("Prep Label Printer").Selected, 1, 0) & " AS [PrepLabelPrinter]")
                .AppendLine("		," & IIf(cblProductivityEnhancements.Items.FindByText("Operations Tablet").Selected, 1, 0) & " AS [OperationsTablet]")
                .AppendLine("		," & IIf(cblProductivityEnhancements.Items.FindByText("Lettuce Test").Selected, 1, 0) & " AS [LettuceTest]")
                .AppendLine("		," & IIf(cblProductivityEnhancements.Items.FindByText("Ambient Bacon").Selected, 1, 0) & " AS [AmbientBacon]")
                .AppendLine("		," & 0 & " AS [EmergencyDTOnly]")
                '.AppendLine("		," & tbDP1.Text & " As [CrewProductivityDP1]")
                '.AppendLine("		," & tbDP2_3.Text & " As [CrewProductivityDP2]")
                '.AppendLine("		," & tbDP4_5.Text & " As [CrewProductivityDP3]")
                '.AppendLine("		," & tbDP6.Text & " As [CrewProductivityDP4]")
                .AppendLine("		," & ddlProductivityDP1.SelectedValue & " As [CrewProductivityDP1]")
                .AppendLine("		," & ddlProductivityDP23.SelectedValue & " As [CrewProductivityDP2]")
                .AppendLine("		," & ddlProductivityDP45.SelectedValue & " As [CrewProductivityDP3]")
                .AppendLine("		," & ddlProductivityDP6.SelectedValue & " As [CrewProductivityDP4]")
                .AppendLine("		," & ddlProductivityDP1Wknd.SelectedValue & " As [CrewProductivityDP1_Wknd]")
                .AppendLine("		," & ddlProductivityDP23Wknd.SelectedValue & " As [CrewProductivityDP2_Wknd]")
                .AppendLine("		," & ddlProductivityDP45Wknd.SelectedValue & " As [CrewProductivityDP3_Wknd]")
                .AppendLine("		," & ddlProductivityDP6Wknd.SelectedValue & " As [CrewProductivityDP4_Wknd]")
                .AppendLine(") As [SOURCE]")
                .AppendLine("	On TARGET.[StoreID] = SOURCE.[StoreID]")
                .AppendLine("")
                .AppendLine("When MATCHED Then")
                .AppendLine("	UPDATE Set")
                .AppendLine("		  [CounterType] = SOURCE.[CounterType]")
                .AppendLine("		 ,[GuideID] = SOURCE.[GuideID]")
                .AppendLine("		 ,[ExchangeRate] = SOURCE.[ExchangeRate]")
                .AppendLine("		 ,[StoreType] = SOURCE.[StoreType]")
                .AppendLine("		 ,[KitchenType] = SOURCE.[KitchenType]")
                .AppendLine("		 ,[DiningRoomType] = SOURCE.[DiningRoomType]")
                .AppendLine("		 ,[DiningRoomFloorType] = SOURCE.[DiningRoomFloorType]")
                .AppendLine("		 ,[DiningRoomDrinksType] = SOURCE.[DiningRoomDrinksType]")
                .AppendLine("		 ,[GrillType] = SOURCE.[GrillType]")
                .AppendLine("		 ,[Kiosk] = SOURCE.[Kiosk]")
                .AppendLine("		 ,[DigitalType] = SOURCE.[DigitalType]")
                .AppendLine("		 ,[OrderTechnology] = SOURCE.[OrderTechnology]")
                .AppendLine("		 ,[DriveThruMix] = SOURCE.[DriveThruMix]")
                .AppendLine("		 ,[Powersoak] = SOURCE.[Powersoak]")
                .AppendLine("		 ,[CashScales] = SOURCE.[CashScales]")
                .AppendLine("		 ,[GreaseExtraction] = SOURCE.[GreaseExtraction]")
                .AppendLine("		 ,[BillValidationSafe] = SOURCE.[BillValidationSafe]")
                .AppendLine("		 ,[BackOfficeForecastPrep] = SOURCE.[BackOfficeForecastPrep]")
                .AppendLine("		 ,[BackOfficeInventory] = SOURCE.[BackOfficeInventory]")
                .AppendLine("		 ,[BackOfficeLaborScheduler] = SOURCE.[BackOfficeLaborScheduler]")
                .AppendLine("		 ,[WareWash] = SOURCE.[WareWash]")
                .AppendLine("		 ,[ElectricSaladSpinner] = SOURCE.[ElectricSaladSpinner]")
                .AppendLine("		 ,[PreCutBacon] = SOURCE.[PreCutBacon]")
                .AppendLine("		 ,[PreChopLettuce] = SOURCE.[PreChopLettuce]")
                .AppendLine("		 ,[PrepLabelPrinter] = SOURCE.[PrepLabelPrinter]")
                .AppendLine("		 ,[OperationsTablet] = SOURCE.[OperationsTablet]")
                .AppendLine("		 ,[LettuceTest] = SOURCE.[LettuceTest]")
                .AppendLine("		 ,[AmbientBacon] = SOURCE.[AmbientBacon]")
                .AppendLine("		 ,[EmergencyDTOnly] = SOURCE.[EmergencyDTOnly]")
                .AppendLine("		 ,[CrewProductivityDP1] = SOURCE.[CrewProductivityDP1]")
                .AppendLine("		 ,[CrewProductivityDP2] = SOURCE.[CrewProductivityDP2]")
                .AppendLine("		 ,[CrewProductivityDP3] = SOURCE.[CrewProductivityDP3]")
                .AppendLine("		 ,[CrewProductivityDP4] = SOURCE.[CrewProductivityDP4]")
                .AppendLine("		 ,[CrewProductivityDP1_Wknd] = SOURCE.[CrewProductivityDP1_Wknd]")
                .AppendLine("		 ,[CrewProductivityDP2_Wknd] = SOURCE.[CrewProductivityDP2_Wknd]")
                .AppendLine("		 ,[CrewProductivityDP3_Wknd] = SOURCE.[CrewProductivityDP3_Wknd]")
                .AppendLine("		 ,[CrewProductivityDP4_Wknd] = SOURCE.[CrewProductivityDP4_Wknd]")
                .AppendLine("		 	 ")
                .AppendLine("When Not MATCHED Then")
                .AppendLine("	INSERT")
                .AppendLine("	(")
                .AppendLine("		 [StoreID]")
                .AppendLine("		,[GuideID]")
                .AppendLine("		,[ExchangeRate]")
                .AppendLine("		,[CounterType]")
                .AppendLine("		,[StoreType]")
                .AppendLine("		,[KitchenType]")
                .AppendLine("		,[DiningRoomType]")
                .AppendLine("		,[DiningRoomFloorType]")
                .AppendLine("		,[DiningRoomDrinksType]")
                .AppendLine("		,[GrillType]")
                .AppendLine("		,[Kiosk]")
                .AppendLine("		,[DigitalType]")
                .AppendLine("		,[OrderTechnology]")
                .AppendLine("		,[DriveThruMix]")
                .AppendLine("		,[Powersoak]")
                .AppendLine("		,[CashScales]")
                .AppendLine("		,[GreaseExtraction]")
                .AppendLine("		,[BillValidationSafe]")
                .AppendLine("		,[BackOfficeForecastPrep]")
                .AppendLine("		,[BackOfficeInventory]")
                .AppendLine("		,[BackOfficeLaborScheduler]")
                .AppendLine("		,[WareWash]")
                .AppendLine("		,[ElectricSaladSpinner]")
                .AppendLine("		,[PreCutBacon]")
                .AppendLine("		,[PreChopLettuce]")
                .AppendLine("		,[PrepLabelPrinter]")
                .AppendLine("		,[OperationsTablet]")
                .AppendLine("		,[LettuceTest]")
                .AppendLine("		,[AmbientBacon]")
                .AppendLine("		,[EmergencyDTOnly]")
                .AppendLine("		,[CrewProductivityDP1]")
                .AppendLine("		,[CrewProductivityDP2]")
                .AppendLine("		,[CrewProductivityDP3]")
                .AppendLine("		,[CrewProductivityDP4]")
                .AppendLine("		,[CrewProductivityDP1_Wknd]")
                .AppendLine("		,[CrewProductivityDP2_Wknd]")
                .AppendLine("		,[CrewProductivityDP3_Wknd]")
                .AppendLine("		,[CrewProductivityDP4_Wknd]")
                .AppendLine("	)")
                .AppendLine("	VALUES")
                .AppendLine("	(")
                .AppendLine("		 SOURCE.[StoreID]")
                .AppendLine("		,SOURCE.[GuideID]")
                .AppendLine("		,SOURCE.[ExchangeRate]")
                .AppendLine("		,SOURCE.[CounterType]")
                .AppendLine("		,SOURCE.[StoreType]")
                .AppendLine("		,SOURCE.[KitchenType]")
                .AppendLine("		,SOURCE.[DiningRoomType]")
                .AppendLine("		,SOURCE.[DiningRoomFloorType]")
                .AppendLine("		,SOURCE.[DiningRoomDrinksType]")
                .AppendLine("		,SOURCE.[GrillType]")
                .AppendLine("		,SOURCE.[Kiosk]")
                .AppendLine("		,SOURCE.[DigitalType]")
                .AppendLine("		,SOURCE.[OrderTechnology]")
                .AppendLine("		,SOURCE.[DriveThruMix]")
                .AppendLine("		,SOURCE.[Powersoak]")
                .AppendLine("		,SOURCE.[CashScales]")
                .AppendLine("		,SOURCE.[GreaseExtraction]")
                .AppendLine("		,SOURCE.[BillValidationSafe]")
                .AppendLine("		,SOURCE.[BackOfficeForecastPrep]")
                .AppendLine("		,SOURCE.[BackOfficeInventory]")
                .AppendLine("		,SOURCE.[BackOfficeLaborScheduler]")
                .AppendLine("		,SOURCE.[WareWash]")
                .AppendLine("		,SOURCE.[ElectricSaladSpinner]")
                .AppendLine("		,SOURCE.[PreCutBacon]")
                .AppendLine("		,SOURCE.[PreChopLettuce]")
                .AppendLine("		,SOURCE.[PrepLabelPrinter]")
                .AppendLine("		,SOURCE.[OperationsTablet]")
                .AppendLine("		,SOURCE.[LettuceTest]")
                .AppendLine("		,SOURCE.[AmbientBacon]")
                .AppendLine("		,SOURCE.[EmergencyDTOnly]")
                .AppendLine("		,SOURCE.[CrewProductivityDP1]")
                .AppendLine("		,SOURCE.[CrewProductivityDP2]")
                .AppendLine("		,SOURCE.[CrewProductivityDP3]")
                .AppendLine("		,SOURCE.[CrewProductivityDP4]")
                .AppendLine("		,SOURCE.[CrewProductivityDP1_Wknd]")
                .AppendLine("		,SOURCE.[CrewProductivityDP2_Wknd]")
                .AppendLine("		,SOURCE.[CrewProductivityDP3_Wknd]")
                .AppendLine("		,SOURCE.[CrewProductivityDP4_Wknd]")
                .AppendLine("	)")
                .AppendLine(";")
                .AppendLine("")
                'Sprint 18 // D-01124 // TK-01959 2016-05-11 jjc Adjust for Change in signature of [SAS_ExportToSA_Scheduler_LaborGuides] procedure.
                .AppendLine("EXECUTE [dbo].[SAS_ExportToSA_Scheduler_LaborGuides]")
                .AppendLine("    @StoreID = " & sboNet_StoreID)
                .AppendLine("   ,@IPTSeedDate = '" & DateAdd(DateInterval.Day, -1, Date.Now()).ToShortDateString() & "'")
                .AppendLine("   ,@RequestedBy = '" & InvWSS.Platform.Application.AccountIdentifier & "'")
                .AppendLine(";")

            End With

            InvWSS.ExecuteCommand(sbSQL.ToString())

            SetOptionAvailabitlity()

        Catch ex As Exception

            Dim sErr As String
            sErr = ex.ToString

        End Try

    End Sub

    Function GetUsername() As String

        Dim username As String = ""

        Try

            Dim sbSQL As New StringBuilder
            Dim tabUsername As New DataTable

            With sbSQL

                .AppendLine("SELECT")
                .AppendLine("	[UserID] AS [Username]")
                .AppendLine("FROM")
                .AppendLine("	[dbo].[tmAccount] WITH(NOLOCK)")
                .AppendLine("WHERE")
                .AppendLine("	[UniversalAccountIdentifier] = '" & Session("AccountID") & "'")

            End With

            InvWSS.GetData(sbSQL.ToString(), tabUsername)

            If tabUsername.Rows.Count > 0 Then
                username = tabUsername.Rows(0).Item("Username")
            End If

        Catch ex As Exception

            'Eat it...

        End Try

        Return username

    End Function
    Private Sub ShowLastUser(ByVal sAccountID As String, ByVal sLastModified As String)
        Try

            Dim sbSQL As New StringBuilder
            Dim tabStoreID As New DataTable

            With sbSQL

                .AppendLine("SELECT [FirstName] + ' ' + [LastName] AS LastUser FROM tmAccount WHERE UniversalAccountIdentifier = '" & sAccountID & "'")

            End With

            InvWSS.GetData(sbSQL.ToString(), tabStoreID)

            If tabStoreID.Rows.Count > 0 Then

                lblLastUpdate.Text = "Last Updated By: " & tabStoreID.Rows(0).Item("LastUser") & " on " & sLastModified

            End If

        Catch

            'Eat it...

        End Try
    End Sub

    Private Function GetSBOnetStoreID() As Integer

        Dim storeID As Integer = -1

        Try

            Dim sbSQL As New StringBuilder
            Dim tabStoreID As New DataTable

            With sbSQL

                .AppendLine("DECLARE @dss_store_id AS VARCHAR(31) = '" & ddlStore.SelectedValue & "'")
                .AppendLine("")
                .AppendLine("SELECT")
                .AppendLine("	 [StoreID]")
                .AppendLine("FROM")
                .AppendLine("	[dbo].[tmStore] AS [tmS] WITH(NOLOCK)")
                .AppendLine("WHERE")
                .AppendLine("	LEFT(tmS.[UniversalNodeIdentifier], 31) = @dss_store_id")

            End With

            InvWSS.GetData(sbSQL.ToString(), tabStoreID)

            If tabStoreID.Rows.Count > 0 Then

                storeID = tabStoreID.Rows(0).Item("StoreID")

            End If

        Catch

            'Eat it...

        End Try

        Return storeID

    End Function

End Class