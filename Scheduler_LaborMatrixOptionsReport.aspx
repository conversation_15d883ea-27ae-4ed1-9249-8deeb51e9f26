﻿<%@ Page MasterPageFile="site.master" Language="VB" Debug="true" EnableViewState="true" AutoEventWireup="false" Inherits="Scheduler_LaborMatrixOptionsReport" CodeFile="Scheduler_LaborMatrixOptionsReport.aspx.vb" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" Runat="Server">

    <%-- Global Styles --%>
    <link rel="stylesheet" href="Styles/report-options.css" />

    <%-- Page Specific Styles --%>
    <style>

        #report-options {
            position: absolute;
            top: 150px;
            left: 50%;
            margin-left: -176px;
            width: 352px;
            z-index: 200;
        }

            #store-container {
                height: 160px;
                overflow: hidden;
                overflow-y: scroll;
            }

            .grid-container .gv td,
            .grid-container .gv th {
                padding-left: 8px !important;
                padding-right: 8px !important;
                font-size: 11px;
            }

            .grid-container .gv th {
                text-align: center !important;
            }

            .grid-container .gv th.group-header {
                font-size: 13px;
            }

                .grid-container .gv td .productivity-enhancement input {
                    width: 12px !important;
                    height: 12px !important;
                }

    </style>

    <form runat="server" id="Form1">
        
        <%-- Report Options --%>
        <div id="report-options" class="content-container hide-me">

            <div class="header">
                <h1>Report Options</h1>
            </div>

            <div class="options-container group">
                        
                <%-- Stores --%> 
                <fieldset id="fsStore" runat="server">

                    <legend>Store</legend>

                    <img class="select-none" src="images/false.jpg" height="9" width="9" title="Select None" /><img class="select-all" src="images/true.jpg" height="9" width="9" title="Select All" />

                    <div id="store-container">
                        <asp:CheckBoxList ID="cblStore" runat="server" />
                    </div>                        

                </fieldset>
                <asp:Label ID="lblStore" runat="server" CssClass="required" Visible="false" />
                
            </div>
        
            <div class="button-container">
                <input type="button" id="hide-options" class="buttons" value="Close" />
                <asp:Button ID="btnSubmit" runat="server" CssClass="buttons" Text="Submit" OnClick="btnSubmit_Click" />
            </div>

        </div>

        <%-- Report --%>
        <div id="report" class="content-container">

            <div class="header">
                <h1>Labor Matrix Options Report</h1>
            </div>

            <div class="button-container">                
                <input type="button" id="show-options" class="buttons" value="Report Options" />
                <asp:Button ID="btnExcel" CssClass="buttons" runat="server" Text="Export To Excel" OnClick="btnExcel_Click" Visible="false" />
            </div>

            <div class="grid-container">

                <asp:GridView ID="gvLaborMatrixOptions" runat="server" CssClass="gv" AutoGenerateColumns="false" AlternatingRowStyle-BackColor="#efefef"
                    OnItemCreated="MyDataGrid_ItemCreated"
                    OnRowDataBound="MyDataGrid_ItemCreated"
                    >

                    <Columns>

                        <asp:BoundField DataField="StoreNum" HeaderText="Store Num" />
                        <asp:BoundField DataField="StoreDescription" HeaderText="Store Description" />
                        <asp:BoundField DataField="GuideDescription" HeaderText="Guide" />
                        <asp:BoundField DataField="ExchangeRate" HeaderText="Exchange Rate" DataFormatString="{0:N2}" />
                        <asp:BoundField DataField="CounterType" HeaderText="Counter Type" />
                        <asp:BoundField DataField="StoreType" HeaderText="PUW Type" />
                        <asp:BoundField DataField="KitchenType" HeaderText="Kitchen" />
                        <asp:BoundField DataField="DiningRoomType" HeaderText="Dining Room" />
                        <asp:BoundField DataField="DiningRoomFloorType" HeaderText="Flooring" />
                        <asp:BoundField DataField="DiningRoomDrinksType" HeaderText="Drinks" />
                        <asp:BoundField DataField="GrillType" HeaderText="Grill" />
                        <asp:BoundField DataField="Kiosk" HeaderText="Kiosk" />
                        <asp:BoundField DataField="DigitalType" HeaderText="Digital Mix" />
                        <asp:BoundField DataField="OrderTechnology" HeaderText="Order Technology" />
                        <asp:BoundField DataField="DriveThruMix" HeaderText="Drive Thru Mix" />
                        
                        <%--<asp:CheckBoxField DataField="EmergencyDTOnly" HeaderText="DT Only" ItemStyle-HorizontalAlign="Center" />--%>

                        <asp:CheckBoxField DataField="Powersoak" HeaderText="Powersoak" ItemStyle-HorizontalAlign="Center" ControlStyle-CssClass="productivity-enhancement" />
                        <asp:CheckBoxField DataField="CashScales" HeaderText="Cash Scales" ItemStyle-HorizontalAlign="Center" ControlStyle-CssClass="productivity-enhancement" />
                        <asp:CheckBoxField DataField="GreaseExtraction" HeaderText="Grease Extraction" ItemStyle-HorizontalAlign="Center" ControlStyle-CssClass="productivity-enhancement" />
                        
                        
                        
                        
                        <asp:CheckBoxField DataField="BillValidationSafe" HeaderText="Bill Validation Safe" ItemStyle-HorizontalAlign="Center" ControlStyle-CssClass="productivity-enhancement" />
                        <asp:CheckBoxField DataField="BackOfficeForecastPrep" HeaderText="Back-Office Forecast/Prep" ItemStyle-HorizontalAlign="Center" ControlStyle-CssClass="productivity-enhancement" />
                        <asp:CheckBoxField DataField="BackOfficeInventory" HeaderText="Back-Office Inventory" ItemStyle-HorizontalAlign="Center" ControlStyle-CssClass="productivity-enhancement" />
                        <asp:CheckBoxField DataField="BackOfficeLaborScheduler" HeaderText="Back-Office Labor Scheduler" ItemStyle-HorizontalAlign="Center" ControlStyle-CssClass="productivity-enhancement" />

                        <asp:CheckBoxField DataField="WareWash" HeaderText="WareWash" ItemStyle-HorizontalAlign="Center" ControlStyle-CssClass="productivity-enhancement" />
                        <asp:CheckBoxField DataField="ElectricSaladSpinner" HeaderText="Electric Salad Spinner" ItemStyle-HorizontalAlign="Center" ControlStyle-CssClass="productivity-enhancement" />
                        <asp:CheckBoxField DataField="PreChopLettuce" HeaderText="Pre-chopped lettuce" ItemStyle-HorizontalAlign="Center" ControlStyle-CssClass="productivity-enhancement" />
                        <asp:CheckBoxField DataField="PrepLabelPrinter" HeaderText="Prep Label Printer" ItemStyle-HorizontalAlign="Center" ControlStyle-CssClass="productivity-enhancement" />
                        <asp:CheckBoxField DataField="OperationsTablet" HeaderText="Operations Tablet" ItemStyle-HorizontalAlign="Center" ControlStyle-CssClass="productivity-enhancement" />
                        <asp:CheckBoxField DataField="LettuceTest" HeaderText="Lettuce Test" ItemStyle-HorizontalAlign="Center" ControlStyle-CssClass="productivity-enhancement" />
                        <asp:CheckBoxField DataField="AmbientBacon" HeaderText="Ambient Bacon" ItemStyle-HorizontalAlign="Center" ControlStyle-CssClass="productivity-enhancement" />

                        <asp:BoundField DataField="CrewProductivityDP1" HeaderText="Day Part 1" ItemStyle-HorizontalAlign="Center" />
                        <asp:BoundField DataField="CrewProductivityDP2" HeaderText="Day Part 2 & 3" ItemStyle-HorizontalAlign="Center" />
                        <asp:BoundField DataField="CrewProductivityDP3" HeaderText="Day Part 4 & 5" ItemStyle-HorizontalAlign="Center" />
                        <asp:BoundField DataField="CrewProductivityDP4" HeaderText="Day Part 6" ItemStyle-HorizontalAlign="Center" />

                        <asp:BoundField DataField="CrewProductivityDP1_Wknd" HeaderText="Weekend Day Part 1" ItemStyle-HorizontalAlign="Center" />
                        <asp:BoundField DataField="CrewProductivityDP2_Wknd" HeaderText="Weekend Day Part 2 & 3" ItemStyle-HorizontalAlign="Center" />
                        <asp:BoundField DataField="CrewProductivityDP3_Wknd" HeaderText="Weekend Day Part 4 & 5" ItemStyle-HorizontalAlign="Center" />
                        <asp:BoundField DataField="CrewProductivityDP4_Wknd" HeaderText="Weekend Day Part 6" ItemStyle-HorizontalAlign="Center" />


                    </Columns>

                </asp:GridView>

                <asp:Label ID="lblGrid" runat="server" CssClass="required label" Text='Please select "Report Options" to continue.' />

            </div>

        </div>

        <%-- Hidden textbox to determine if the "Report Options" popup should be shown after a postback --%>
        <asp:TextBox ID="tbShowOpts" runat="server" CssClass="hide-me" />

        <%-- Site Overlay --%>
        <div id="site-overlay" class="hide-me"></div> 

    </form>

    <%-- Scripts --%>
    <script src="JS/jquery-1.9.1.js"></script>

    <script type="text/javascript">

        ;( function( $, document ) {
            "use strict";

            var $siteOverlay = $( "#site-overlay" ),
                containers = {
                    $report: $( "#report" ),
                    $reportOptions: $( "#report-options" )
                },
                buttons = {
                    $showOptions: $( "#show-options" ),
                    $hideOptions: $( "#hide-options" )
                },
                asp = {
                    $tbShowOpts: $("#<%=tbShowOpts.ClientID%>")
                },
                methods = {
                    init: function() {

                        // Bind events
                        methods.bindEvents();

                        // If the hidden <asp:TextBox> indicates that the "Report Options" popup should be displayed --> Clear the textbox and show the popup
                        if (asp.$tbShowOpts.val() === "True") {

                            asp.$tbShowOpts.val( "" );
                            methods.toggleOptions();

                        }

                    },
                    bindEvents: function() {

                        // When the "Download Blank Workbook" and/or respective "Close" button is clicked...
                        buttons.$showOptions.add( buttons.$hideOptions ).on( "click", function() {
                            methods.toggleOptions();
                        });

                        // When a "+" or "-" icon is clicked ( Select All/None )...
                        containers.$reportOptions.on( "click", ".select-all, .select-none", function() {

                            var $el = $( this ),
                                checkStatus = $el.prop( "class" ) === "select-all",
                                $parent = $el.parent();

                            $parent.find( ":checkbox" ).each( function() {
                                this.checked = checkStatus;
                            });

                        });
                        
                    },
                    toggleOptions: function() {

                        containers.$reportOptions.toggleClass( "hide-me" );
                        $siteOverlay.toggleClass( "hide-me" );

                    }

                };

            // Initialize on page load
            methods.init();

        })( jQuery, document );

    </script>

</asp:Content>