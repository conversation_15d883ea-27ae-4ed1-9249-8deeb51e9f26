Imports System
Imports WebServicesAddons.SendGridEmailService

Partial Class SendGridTest
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Page load logic if needed
    End Sub

    Protected Sub btnSendEmail_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Try
            ' Validate inputs
            If String.IsNullOrEmpty(txtToEmail.Text.Trim()) Then
                lblResult.Text = "Please enter a valid email address."
                lblResult.ForeColor = Drawing.Color.Red
                Return
            End If

            If String.IsNullOrEmpty(txtSubject.Text.Trim()) Then
                lblResult.Text = "Please enter a subject."
                lblResult.ForeColor = Drawing.Color.Red
                Return
            End If

            ' Prepare email content
            Dim toEmail As String = txtToEmail.Text.Trim()
            Dim subject As String = txtSubject.Text.Trim()
            Dim message As String = txtMessage.Text.Trim()
            
            ' Add some additional info to the message
            Dim fullMessage As String = message & vbCrLf & vbCrLf & _
                "---" & vbCrLf & _
                "Sent from SBOnet SendGrid Test Page" & vbCrLf & _
                "Date: " & DateTime.Now.ToString() & vbCrLf & _
                "Server: " & Request.ServerVariables("SERVER_NAME")

            ' Send the email
            lblResult.Text = "Sending email..."
            lblResult.ForeColor = Drawing.Color.Blue

            Dim success As Boolean = WebServicesAddons.SendGridEmailService.SendMail(toEmail, subject, fullMessage, True)

            If success Then
                lblResult.Text = "Email sent successfully to " & toEmail & " at " & DateTime.Now.ToString()
                lblResult.ForeColor = Drawing.Color.Green
                
                ' Clear the form
                txtToEmail.Text = ""
                txtSubject.Text = "SendGrid Test Email"
                txtMessage.Text = "This is a test email sent via SendGrid from SBOnet."
            Else
                lblResult.Text = "Failed to send email. Please check the SendGrid configuration and try again."
                lblResult.ForeColor = Drawing.Color.Red
            End If

        Catch ex As Exception
            lblResult.Text = "Error sending email: " & ex.Message
            lblResult.ForeColor = Drawing.Color.Red
        End Try
    End Sub

End Class
