﻿Imports AmazonUtilities.Mail.SimpleEmailService
Imports System.Net

Public Class SMT
    Inherits System.Web.UI.Page
 
   Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Dim sbBody As New StringBuilder()
        Dim sBody As String

        sbBody.AppendLine("SecurityProtocol: " & System.Net.ServicePointManager.SecurityProtocol)

        System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls Or DirectCast(768, System.Net.SecurityProtocolType) Or DirectCast(3072, System.Net.SecurityProtocolType)

        sbBody.AppendLine("SecurityProtocol: " & System.Net.ServicePointManager.SecurityProtocol)

        sbBody.AppendLine()
        sbBody.AppendLine("Line 1")
        sbBody.AppendLine("Line 2")
        sbBody.AppendLine("Line 3")

        sbBody.appendline(ConfigurationManager.AppSettings("AmazonSES-Host"))
        sbBody.appendline(ConfigurationManager.AppSettings("AmazonSES-Port"))
        
        sBody = sbBody.ToString '.Replace(vbCrLf, "<br />")

        tbInfo.text= sBody.Replace(vbCrLf, "<br />")

        AmazonUtilities.Mail.SimpleEmailService.SendMail("<EMAIL>", "Test", sBody & Now.ToString(), True)

    End Sub

End Class