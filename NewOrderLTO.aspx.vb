﻿Option Explicit On

Imports System.Data.SqlClient
Imports AmazonUtilities.Mail.SimpleEmailService
Imports System.Configuration
Imports SBOTypeLibrary
Imports WebServicesAddons


'Changelog:
'2016-12-19 jjc Sprint 28 // B-02683 // TK-02759 Add Column for purchase unit information
'2016-12-21 jjc Sprint 28 // B-02684 // TK-02763 // TK-02764 Color Description for Catch Weight Items & Legend
'2016-12-21 jjc Sprint 28 // B-02684 // TK-02765 Make Suggested quantity look clickable.
'2017-01-17 jjc Sprint NA // -- NA - // Removed "<EMAIL>" from eMail list under btnRequest_click (Big Ant request from DF)
'2017-07-08 jjc Sprint 37 // B-03021 // TK-03748 Allow for ordering by Count sheet walking order as defined in new [tmCountSheetOrderingSequence] table.
'2017-08-11 jjc Sprint 38 // B-03061 // Minor tweak to where clause for [tmCountSheetOrderingSequence] (added [Used])
'2017-09-07 DAF Sprint 39 // B-03103 // Allow SOGWiz create an order based on data from an alternate location
'2019-04-17 jjc Sprint 64 SBOD-341/538  Added tdCS.[IsActive] = 1 to the [CSWalking] section of GetSuggestedOrder(), so that only items that are active on a count sheet are used within that count sheets section of the ordering
'   Threw in a couple of no-locks in the GetSuggestedOrder() query while I was there.  It seems to be a slow running query sometimes, maybe it'll help.
'2019-05-29 jrp Sprint 65? - I ripped out the old GetSuggestedOrder() query and shoved in a new one. Major speed improvements, was tested by Jocelyn's team and QA.
'   Also, I added a bunch of debug timings for future reference.
'2019-05-29 jjc Sprint 66 SBOD-848/849 - Added zero protection for "orderdays" being zero when used to determine ThawTimeInfluence
'2019-06-12 jjc Sprint 66 SBOD-871/872 Added an IsNull proctection for [BasisGrossSales]

'2019-07-18 jjc Sprint 68 SBOD-986/987 Regional recipes are no longer "a thing" and have been removed from the rollup some time ago,
'   and not present in the GUI for a long time before that.  And they seem to be causing a strange issue with slowing this query way
'   down in JirehMgt, so out with the old.
'2019-07-25 jjc Sprint 69 SBOD-1015/1016 so the [GeneralRecipes] section is still being a pain, taking up to a minute to run in WendGord under optimal conditions.
'   to add to the strangeness, the procedure ran nice and quick when ran for Maines, but bogged down when using SYGMA.
'   Changed the join between @IngredientStats and the [GeneralRecipes] CTE from a LEFT JOIN to an INNER JOIN as the column being updated had a default of 0 so
'   it wasn't necessary to LEFT JOIN to hit all rows in @IngredientStats as the ones that would be picked up in a LEFT JOIN as opposed to an INNER JOIN are 
'   already 0.
'2019-09-05 jjc Sprint 71 SBOD 1140/1141 Added an Index hint in [GeneralRecipes] CTE for join to [BI_trStoreSalesDetail]
'2021-04-12 jjs Sprint 79w65 SBOD-2204 Fixing join to [CountSheetSequence], Was previously joined to [I-Stats] for StoreID, which doesn't always have a value for StoreID
'2021-06-07 jjc Sprint79w67 SBOD-2254 Updating <NAME_EMAIL> to <EMAIL>
'   HelpDesk@dumac.<NAME_EMAIL>
'   Alerts-SBOnet@dumac.<NAME_EMAIL>
'2021-09-11 jjc Sprint 79w68 No story just efficency problems on Saturday
'2025-04-17 mari Updated email addresses to use configuration values with @ncrvoyix.com domain
'2025-04-16 jjc Sprint 27 SBOO-810 Adding ProcessApprovedOrder() Sub as workaround for Vendor order seneing not being available in 6.x yet

Partial Class NewOrderLTO
    Inherits System.Web.UI.Page

    Friend SelVendorName As String
    Friend ProcessMessage As String
    Friend sFormat_Decimal As String = "0.#0"
    Friend SOGQtyVarPct As Double
    'Mark Williams Bug 348
    Friend MaxVendorOrderQty As Integer
    Friend nAutoSaveInterval As Integer


    Dim InvWSS As InventoryWebSiteServices.InventoryServices
    Dim sPackageCode As String = "NewOrder"
    Dim busdate As Date
    Dim dBasisdate As Date
    Dim orderdays As Integer
    Dim SelVendorID As Integer
    Dim SelStoreID As Integer
    Dim SelStoreName As String
    Dim ProSales As Double
    Dim DSSForecast As String
    Dim qsLTOS As String = "0"
    Dim bSupplies As Boolean
    Dim VendorOrderNum As Integer
    Dim PageTitle As String
    Dim PO As String
    Dim gTotal As Double
    Dim ShowPrevOrd As Boolean = False
    Dim ShowPrevOrdNoInfluence As Boolean = False
    Dim CaseCount As Double
    Dim iSort As Integer = 0
    Dim iIncludeAllItems As Integer = 0
    Dim bStoreDoesBreakfast As Boolean
    Dim nRowNumber As Integer = 0
    Dim TotalItems As Integer
    Dim sSQL As String
    Dim AutoGenInvoiceOp As Integer = 0
    Dim SOGRoundUp As Double
    Dim bStartAutoSave As Boolean = True
    Private iDecimalFormatting As Integer
    Dim iWksTrend As Integer = 0

    Sub Page_Load(ByVal sender As Object, ByVal e As EventArgs) Handles Me.Load
        If Len(Request.QueryString("function")) AndAlso Request.QueryString("function") = "VendorOrder" Then
            ProcessApprovedOrder()
        Else




            If Len(Session("UserID")) = 0 Then
                Response.Redirect("login.aspx?timeout=1")
            End If

            InvWSS = Session("IWSS")

            SBOMaster.CalcTimings("Pre-PVs")

            Try
                iDecimalFormatting = InvWSS.Platform.PersistentValue("InventoryConfig", "DecimalFormatting")
                sFormat_Decimal = "0.#"
                For I As Integer = 0 To iDecimalFormatting - 2
                    sFormat_Decimal = sFormat_Decimal & "0"
                Next
            Catch eFormatting As Exception
                sFormat_Decimal = "0.#0"
            End Try

            Try
                SOGQtyVarPct = InvWSS.Platform.PersistentValue("InventoryConfig", "SOGQtyVarPct")
            Catch eSOGQtyVarPct As Exception
                SOGQtyVarPct = 0.25
            End Try

            Try
                AutoGenInvoiceOp = InvWSS.Platform.PersistentValue("InventoryConfig", "InvoiceAutoGenOption")
            Catch eSOGQtyVarPct As Exception
                AutoGenInvoiceOp = 0
            End Try

            If AutoGenInvoiceOp <> 0 Then
                cbAutoInvoice.Visible = True
            End If

            vendorlogo.ImageUrl = ""
            vendorlogo.Visible = False

            Try
                nAutoSaveInterval = InvWSS.Platform.PersistentValue("InventoryConfig", "AutoSaveInterval_Order")
            Catch eSOGQtyVarPct As Exception
                nAutoSaveInterval = 600000
            End Try

            SBOMaster.CalcTimings("PVs Loaded")

            If InvWSS.GetUserViewPermission(sPackageCode, Session("AccountID")) = False Then
                Response.Redirect("permissionmsgs.aspx?msg=noviewperm")
            End If

            If InvWSS.GetUserApprovePermission(sPackageCode, Session("AccountID")) = False Then
                btnSubmit.Visible = False
                btnApprove.Visible = False
            End If

            If InvWSS.GetUserExecutePermission(sPackageCode, Session("AccountID")) = False Then
                btnSave.Visible = False
                btnSaveAndExit.Visible = False
                btnSaveOrder.Visible = False
            End If

            btnConfirmResend.Visible = False
            btnRequestResend.Visible = False

            SBOMaster.CalcTimings("Perms Checked")

            If Not IsPostBack Then
                Session("VOrder") = Nothing

                If Len(Request.QueryString("StoreID")) AndAlso IsNumeric(Request.QueryString("StoreID")) Then
                    SelStoreID = Request.QueryString("StoreID")
                End If

                If Len(Request.QueryString("VendorID")) AndAlso IsNumeric(Request.QueryString("VendorID")) Then
                    SelVendorID = Request.QueryString("VendorID")
                End If

                If Len(Request.QueryString("busdate")) AndAlso IsDate(Request.QueryString("busdate")) Then
                    busdate = Request.QueryString("busdate")
                End If

                If Len(Request.QueryString("UseRefStore")) AndAlso IsNumeric(Request.QueryString("UseRefStore")) AndAlso Request.QueryString("UseRefStore") > 0 Then
                    lblRefStore.Visible = True
                    lblRefStoreName.Visible = True
                    lblRefStoreName.Text = InvWSS.Platform.SBOCoreScalarGet("tmStore", "StoreDescription", "StoreID = " & Request.QueryString("UseRefStore"))
                End If

                If Len(Request.QueryString("basisdate")) AndAlso IsDate(Request.QueryString("basisdate")) Then
                    dBasisdate = CDate(Request.QueryString("basisdate"))
                End If

                If Len(Request.QueryString("ordays")) AndAlso IsNumeric(Request.QueryString("ordays")) Then
                    orderdays = Request.QueryString("ordays")
                End If

                If Len(Request.QueryString("vonum")) AndAlso IsNumeric(Request.QueryString("vonum")) Then
                    VendorOrderNum = Val(Request.QueryString("vonum"))
                Else
                    PageTitle = "New " & SelVendorName & " Order"
                    VendorOrderNum = -1
                End If

                If Len(Request.QueryString("ProSales")) AndAlso IsNumeric(Request.QueryString("ProSales")) Then
                    ProSales = Request.QueryString("ProSales")
                End If

                If Len(Request.QueryString("DSSForecast")) Then
                    DSSForecast = Request.QueryString("DSSForecast")
                    lblDSSForecast.Text = DSSForecast
                End If

                If Len(Request.QueryString("spo")) AndAlso IsNumeric(Request.QueryString("spo")) AndAlso Request.QueryString("spo") = 1 Then
                    ShowPrevOrd = True
                End If

                If Len(Request.QueryString("spo2")) AndAlso IsNumeric(Request.QueryString("spo2")) AndAlso Request.QueryString("spo2") = 1 Then
                    ShowPrevOrdNoInfluence = True
                End If

                If Len(Request.QueryString("supplies")) AndAlso IsNumeric(Request.QueryString("supplies")) Then
                    bSupplies = Val(Request.QueryString("supplies"))
                End If

                If Len(Request.QueryString("LTOS")) Then
                    Dim splitLTOS() As String
                    Dim bOK As Boolean = True
                    splitLTOS = Request.QueryString("LTOS").Split(",")
                    For Each LTO As String In splitLTOS
                        If Not Int32.TryParse(LTO, Nothing) Then
                            bOK = False
                        End If
                    Next

                    If bOK Then
                        qsLTOS = Request.QueryString("LTOS")
                    End If
                End If

                If Len(Request.QueryString("sort")) AndAlso IsNumeric(Request.QueryString("sort")) Then
                    iSort = Val(Request.QueryString("sort"))
                End If

                If Len(Request.QueryString("deliverydate")) AndAlso IsDate(Request.QueryString("deliverydate")) Then
                    txtDeliveryDate.Text = Request.QueryString("deliverydate")
                End If

                'If Len(Request.QueryString("UseRefStore")) AndAlso IsNumeric(Request.QueryString("UseRefStore")) AndAlso Request.QueryString("UseRefStore") = 1 Then
                '    bUseRefStore = True
                '    ddlRefStore.Visible = True
                '    txtBasisDate.Visible = True
                '    ImageBasisDate.Visible = True
                '    If txtBasisDate.Text = "" AndAlso IsDate(dBasisdate) Then
                '        txtBasisDate.Text = dBasisdate
                '    End If
                '    lblBasisDt.Visible = False
                'End If

                If Len(Request.QueryString("wkstrend")) AndAlso IsNumeric(Request.QueryString("wkstrend")) Then
                    iWksTrend = Val(Request.QueryString("wkstrend"))
                End If

                If iWksTrend = 0 Then
                    iWksTrend = 4
                End If

                SBOMaster.CalcTimings("Query String Params")
                txtstoreid.Text = SelStoreID
                SelStoreName = GetStoreName(SelStoreID)
                SBOMaster.CalcTimings("Get Store Name")
                StoreName.Text = SelStoreName
                txtvendorid.Text = SelVendorID
                SelVendorName = GetVendorName(SelVendorID)
                SBOMaster.CalcTimings("Get Vendor Name")
                txtbusdate.Text = busdate.ToString
                vonum.Text = VendorOrderNum
                PageTitle = SelVendorName & " Order " & Format(VendorOrderNum, "0000#")
                SetSOGRoundUp()
                SBOMaster.CalcTimings("SetSOGRoundUp")

                txtCheckMax.Value = "False"

                If SelVendorID > 0 Then
                    'Mark Williams Bug 348
                    CheckMaxOrderQty(SelVendorID)
                    SBOMaster.CalcTimings("CheckMaxOrderQty")
                End If

                Dim tInv As New DataTable

                If VendorOrderNum <> -1 Then
                    SBOMaster.DeclareTimingsRegion("Existing Order")
                    'Get VendorOrder Info LY 07JAN2015 BZ 2563 - Get SortOrder saved with original order
                    Dim sVOInfo As String = "Select ShowSupplies, IncludeItems, SortOrder FROM tmVendorOrder WITH(NOLOCK) WHERE VendorOrderID = " & VendorOrderNum.ToString
                    Dim tVOInfo As New DataTable
                    If InvWSS.GetData(sVOInfo, tVOInfo) Then
                        For Each row As DataRow In tVOInfo.Rows
                            If Not IsDBNull(row("SortOrder")) Then
                                iSort = row("SortOrder")
                            End If
                        Next
                    End If
                    SBOMaster.CalcTimings("Load Sort Order")

                    'InvWSS.GetVendorOrderDetail(VendorOrderNum, tInv)
                    Dim sSQL1 As New StringBuilder

                    sSQL1.AppendLine("Select *")
                    sSQL1.AppendLine("FROM [dbo].[vjVendorOrder_Detail] As [vjVOD] With(NOLOCK)")
                    sSQL1.AppendLine("	INNER JOIN [dbo].[tmIngredient] As [ing] With (NOLOCK)")
                    sSQL1.AppendLine("		On vjVOD.[IngredientID] = ing.[IngredientID]")
                    sSQL1.AppendLine("	LEFT OUTER JOIN")
                    sSQL1.AppendLine("		(")
                    sSQL1.AppendLine("		Select")
                    sSQL1.AppendLine("			 tmCS.[CountSheetID]")
                    sSQL1.AppendLine("			,tmCS.[StoreID]")
                    sSQL1.AppendLine("			,tdCS.[IngredientID]")
                    sSQL1.AppendLine("			,tdCS.[WalkingOrder]")
                    sSQL1.AppendLine("			,Priority.[Priority]")
                    sSQL1.AppendLine("			,ROW_NUMBER() OVER (PARTITION BY tmCS.[StoreID], tdCS.[IngredientID] ORDER BY Priority.[Priority]) As [RNK]")
                    sSQL1.AppendLine("		FROM [dbo].[tmCountSheetOrderingSequence] As [Priority] With (NOLOCK)")
                    sSQL1.AppendLine("			INNER JOIN [dbo].[tmCountSheet] As [tmCS] With (NOLOCK)")
                    sSQL1.AppendLine("				On Priority.[CountSheetID] = tmCS.[CountSheetID]")
                    sSQL1.AppendLine("					And Priority.[Used] = 1")
                    sSQL1.AppendLine("			INNER JOIN [dbo].[tdCountSheet] As [tdCS] With (NOLOCK)")
                    sSQL1.AppendLine("				On tmCS.[CountSheetID] = tdCS.[CountSheetID]")
                    sSQL1.AppendLine("					And tdCS.[IsActive] = 1")
                    sSQL1.AppendLine("		) As [CSWalking]")
                    sSQL1.AppendLine("		On vjVOD.[StoreID] = CSWalking.[StoreID]")
                    sSQL1.AppendLine("			And vjVOD.[IngredientID] = CSWalking.[IngredientID]")
                    sSQL1.AppendLine("			And CSWalking.[RNK] = 1")
                    sSQL1.AppendLine("WHERE [VendorOrderID] = " & VendorOrderNum.ToString)
                    sSQL1.AppendLine("")

                    Select Case iSort
                        Case 1
                            sSQL1.AppendLine("ORDER BY vjVOD.[VendorItemNumber], vjVOD.[WalkingOrder], vjVOD.[IngredientName], vjVOD.[PresentationOrder], vjVOD.[VendorItemID]")
                        Case 2
                            sSQL1.AppendLine("ORDER BY vjVOD.[VendorItemDesc], vjVOD.[WalkingOrder], vjVOD.[IngredientName], vjVOD.[PresentationOrder], vjVOD.[VendorItemNumber], vjVOD.[VendorItemID]")
                        Case 3 'jjc ToDo
                            sSQL1.AppendLine("ORDER BY ISNULL(CSWalking.[RNK], 999), CSWalking.[Priority], CSWalking.[WalkingOrder], vjVOD.[VendorItemID]")
                        Case Else
                            sSQL1.AppendLine("ORDER BY vjVOD.[WalkingOrder], vjVOD.[IngredientName], vjVOD.[PresentationOrder], vjVOD.[VendorItemNumber], vjVOD.[VendorItemID]")
                    End Select

                    InvWSS.GetData(sSQL1.ToString, tInv)
                    SBOMaster.CalcTimings("Load tInv")
                    'DebugTable(tInv)
                    If tInv.Rows.Count Then
                        Dim row As DataRow
                        row = tInv.Rows(0)
                        If Not IsDBNull(row("BusinessDate")) Then
                            BusinessDate.Text = IIf(CType(row("BusinessDate"), Date).Year <> 1, CType(row("BusinessDate"), Date).ToShortDateString, "")
                            txtbusdate.Text = IIf(CType(row("BusinessDate"), Date).Year <> 1, CType(row("BusinessDate"), Date).ToShortDateString, "")
                        End If
                        If Not IsDBNull(row("DatePosted")) Then
                            DatePosted.Text = IIf(CType(row("DatePosted"), Date).Year <> 1, row("DatePosted"), "")
                        End If
                        'If Not IsDBNull(row("DateReceived")) Then
                        'DateReceived.Text = IIf(CType(row("DateReceived"), Date).Year <> 1, row("DateReceived"), "")
                        'End If

                        If Not IsDBNull(row("StoreID")) Then
                            txtstoreid.Text = row("StoreID")
                            StoreName.Text = GetStoreName(row("StoreID"))
                        End If
                        Try
                            If Not IsDBNull(row("DeliveryDate")) Then
                                lblDeliveryDate.Text = CDate(row("DeliveryDate")).ToShortDateString
                                txtDeliveryDate.Text = CDate(row("DeliveryDate")).ToShortDateString
                            End If
                        Catch ex As Exception

                        End Try

                        If Not IsDBNull(row("PurchaseOrderNumber")) Then
                            PurchaseOrderNumber.Text = IIf(row("PurchaseOrderNumber") <> 0, row("PurchaseOrderNumber"), "")
                            txtpo.Text = row("PurchaseOrderNumber")
                        End If

                        If Not IsDBNull(row("VendorID")) Then
                            SelVendorName = GetVendorName(row("VendorID"))
                            txtvendorid.Text = row("VendorID")
                            SelVendorID = row("VendorID")
                            CheckMaxOrderQty(SelVendorID)
                        End If
                        VONumber.Text = Format(VendorOrderNum, "0000#")

                        Dim sDistCtrID As String = ""
                        DistCtr.Text = GetOrderDistCtrName(VendorOrderNum, sDistCtrID)
                        txtDistCtrID.Text = sDistCtrID

                        'Get ApprovedBy and OrderedBy User Account Info
                        Dim dtLastModified As Date
                        Dim tTable As New DataTable
                        Dim sSQL As String = ""
                        sSQL = "Select LastModified, "
                        sSQL &= "(Select LastName FROM tmAccount With(NOLOCK) WHERE UniversalAccountIdentifier = vo.ApprovedBY) As ALastName, "
                        sSQL &= "(Select FirstName FROM tmAccount With(NOLOCK) WHERE UniversalAccountIdentifier = vo.ApprovedBY) As AFirstName, "
                        sSQL &= "(Select LastName FROM tmAccount With(NOLOCK) WHERE UniversalAccountIdentifier = vo.ThumbPrint) As CLastName, "
                        sSQL &= "(Select FirstName FROM tmAccount With(NOLOCK) WHERE UniversalAccountIdentifier = vo.ThumbPrint) As CFirstName "
                        sSQL &= "FROM tmVendorOrder vo With(NOLOCK) WHERE VendorOrderID = " & VendorOrderNum
                        InvWSS.GetData(sSQL, tTable)
                        For Each row In tTable.Rows
                            If Not IsDBNull(row("CFirstName")) Then
                                OrderedBy.Text = row("CFirstName") & " "
                            End If
                            If Not IsDBNull(row("CLastName")) Then
                                OrderedBy.Text &= row("CLastName")
                            End If
                            If Not IsDBNull(row("AFirstName")) Then
                                ApprovedBy.Text = row("AFirstName") & " "
                            End If
                            If Not IsDBNull(row("ALastName")) Then
                                ApprovedBy.Text &= row("ALastName")
                            End If
                            If Not IsDBNull(row("LastModified")) Then
                                dtLastModified = row("LastModified")
                            End If

                        Next

                        'Add Dataless Columns that normally appear in newly created order
                        Dim colLastCountDate As New DataColumn("LastCountDate", System.Type.GetType("System.DateTime"))
                        tInv.Columns.Add(colLastCountDate)

                        btnConfirmResend.Visible = False
                        btnRequestResend.Visible = False

                        'Determine which buttons should be disabled
                        If Len(ApprovedBy.Text) Then      'Order has been approved
                            btnSubmit.Enabled = False
                            'Mark Williams Bug 841 disables the save button
                            btnSaveAndExit.Disabled = True
                            btnSaveAndExit.Visible = False
                            btnSaveOrder.Disabled = True
                            btnSaveOrder.Visible = False
                            btnApprove.Visible = False
                            btnSave.Enabled = False
                            bStartAutoSave = False

                            If dtLastModified.ToShortDateString = Now.ToShortDateString Then
                                Dim URank As Integer
                                Dim RoleID As String = ""
                                'Per BZ 335, hide the resend button for all except admin
                                UserRank(Session("AccountID"), URank, RoleID)
                                If URank = 900 Then
                                    btnConfirmResend.Visible = True
                                Else
                                    btnRequestResend.Visible = True
                                End If
                            End If
                        End If
                    End If
                Else
                    SBOMaster.DeclareTimingsRegion("New Order")
                    If ShowPrevOrd = True Then
                        lblPOInfluence.Text = "Previous order influences order quantities"
                    Else
                        lblPOInfluence.Text = "Previous order does Not influence order quantities"
                    End If

                    If IsNumeric(Request.QueryString("IncludeAllItems")) Then
                        iIncludeAllItems = Val(Request.QueryString("IncludeAllItems"))

                        SBOMaster.CalcTimings("Include All Items")
                    End If

                    If Len(Request.QueryString("DistCtr")) Then
                        txtDistCtrID.Text = Request.QueryString("DistCtr")
                        DistCtr.Text = GetDistCtrName(Request.QueryString("DistCtr"))
                        SBOMaster.CalcTimings("GetDistCtrName")
                    End If

                    tInv = GetSuggestedOrder(SelStoreID, SelVendorID, ProSales, qsLTOS, bSupplies, iSort, Request.QueryString("DistCtr"), ShowPrevOrd, iIncludeAllItems)
                    SBOMaster.CalcTimings("GetSuggestedOrder")

                    PurchaseOrderNumber.Text = Request.QueryString("PO")
                    txtpo.Text = Request.QueryString("PO")
                    BusinessDate.Text = CType(Request.QueryString("busdate"), Date).ToShortDateString
                    lblDeliveryDate.Text = txtDeliveryDate.Text
                    ProjectedSales.Text = FormatCurrency(ProSales, 0).ToString

                    If DSSForecast <> String.Empty AndAlso IsNumeric(DSSForecast) Then
                        lblDSSForecast.Text = FormatCurrency(DSSForecast, 0).ToString
                    ElseIf DSSForecast <> String.Empty Then
                        lblDSSForecast.Text = DSSForecast.ToString
                    End If

                    InvtOrderDays.Text = orderdays.ToString
                    lblBasisDt.Text = dBasisdate

                    If bStoreDoesBreakfast = False Then
                        ProcessMessage &= "This order does Not show Breakfast items because the selected store Is defined As a non-breakfast store.  "
                    End If

                    'BZ 348 Comment #7 Item #2
                    'DAF 01/31/2012
                    'disable the button after clicked so the user doesnt get click happy
                    btnSave.Attributes.Add("onclick", "javascript:" + btnSave.ClientID + ".disabled=true;" + ClientScript.GetPostBackEventReference(btnSave, ""))
                End If

                SBOMaster.DeclareTimingsRegion("PageLoad (After Order Load)")

                If VendorDoesEOrders(SelVendorID) = False Then
                    btnApprove.Visible = False
                    btnSubmit.Visible = False
                    ProcessMessage &= SelVendorName & " does not accept an electronic order.  "
                End If

                SBOMaster.CalcTimings("E-Order check")

                DateReceived.Text = SOGRoundUp
                Session("VOrder") = tInv
                'LY 07JAN2015 BZ2563 - Added the following session variables to preserve these options for use when the order is saved
                Session("iSort") = iSort
                Session("iIncludeAllItems") = iIncludeAllItems
                Session("bSupplies") = bSupplies
                BindGrid(tInv)
            End If

            If InStr(lblDSSForecast.Text, "*") > 0 Then
                lblDSSForecastPartial.Visible = True
            Else
                lblDSSForecastPartial.Visible = False
            End If

            If bStartAutoSave Then
                Dim bdyMaster As HtmlGenericControl = Master.FindControl("bdyMaster")
                bdyMaster.Attributes.Add("onload", "SetLastSaveTime();")
            End If
        End If
    End Sub

    Private Enum eGridColumns
        RowNumber = 0
        VendorItemNumber = 1
        VendorItemDesc = 2
        ProjectedUsage = 3
        OnHand = 4
        PreviousOrders = 5
        OrderQty = 6
        UnitMeasureDescription = 7
        UnitPrice = 8
        TotalPrice = 9
        DollarYield = 10
        SOGQty = 11
        Calculation = 12
        IsPriority = 13
    End Enum

    Sub SetSOGRoundUp()
        Dim tTable As New DataTable
        Dim row As DataRow
        SOGRoundUp = 0.1
        InvWSS.GetData("SELECT SOGRoundUp FROM tmStoreInfo WITH(NOLOCK) WHERE StoreID = " & txtstoreid.Text, tTable)
        For Each row In tTable.Rows
            If Not IsDBNull(row("SOGRoundUp")) Then
                SOGRoundUp = row("SOGRoundUp")
            End If
        Next
    End Sub

    Function GetSuggestedOrder(ByVal iStoreID As Integer, ByVal iVendorID As Integer, ByVal dProjectedSales As Double, ByVal sSPOS As String, ByVal bShowSupplies As Boolean, ByVal iSortOrder As Integer, Optional ByVal sDistributionCenterID As String = "-1", Optional ByVal bIncludePreviousOrder As Boolean = False, Optional ByVal iShowItems As Integer = 0) As DataTable
        Dim ttable As New DataTable
        Dim sbSQL As New StringBuilder
        Dim nRefStoreID As Integer

        Dim sbTestVariables As New StringBuilder

        If Len(Request.QueryString("UseRefStore")) AndAlso IsNumeric(Request.QueryString("UseRefStore")) AndAlso Request.QueryString("UseRefStore") > 0 Then
            nRefStoreID = CInt(Request.QueryString("UseRefStore"))
        Else
            nRefStoreID = iStoreID
        End If

        ' I wish I was surprised that this came from the query string and wasn't sanitized...
        Dim arrSPOS = sSPOS.Split(",")
        Dim listSPOS as new List(Of Integer)
        For Each s As String In arrSPOS
            Dim SPO as Integer = 0
            If Not IsNumeric(s) Or Not Integer.TryParse(s, SPO) Then Continue For
            
            listSPOS.Add(SPO)
        Next

        sSPOS = String.Join(",", listSPOS)
        'It's now sanitized.
        'If this vendor is setup to use [tmVendorAllowedItems], then verify they can only order items in the table
        'Note: In case you're wondering, [tmVendorAllowedItems] is populated with a service that runs on APP4 called VendorFileDistribution. It is used for orgs who do not allow all the same stores to order the same items.
        Dim useAllowedItems as Boolean = InvWSS.Platform.SBODBScalarGet("SBOCore", "tmVendor", "UseAllowedItems", "VendorID = " & iVendorID)

        sbSQL.AppendLine("--For testing SQL")
        sbSQL.AppendLine("/*")
        sbSQL.AppendLine("DECLARE @StoreID INT = ")
        sbSQL.AppendLine("DECLARE @RefStoreID INT = ")
        sbSQL.AppendLine("DECLARE @RefWeekCount INT = 4")
        sbSQL.AppendLine("DECLARE @RefDate DATETIME = CAST(GETDATE() AS DATE)")
        sbSQL.AppendLine("DECLARE @ProjectedSales MONEY = ")
        sbSQL.AppendLine("DECLARE @VendorID INT = ")
        sbSQL.AppendLine("DECLARE @DistributionCenter VARCHAR(10) = NULL")
        sbSQL.AppendLine("DECLARE @ExcludeBreakfast BIT = 0")
        sbSQL.AppendLine("DECLARE @ExcludeSupplies BIT = 1")
        sbSQL.AppendLine("--*/")
        sbSQL.AppendLine("")
        sbSQL.AppendLine("DECLARE @LastOrderDate DATE =")
        sbSQL.AppendLine("        (")
        sbSQL.AppendLine("            SELECT")
        sbSQL.AppendLine("                MAX( [BusinessDate] )")
        sbSQL.AppendLine("            FROM [dbo].[tmVendorOrder] AS [TVO]")
        sbSQL.AppendLine("            WHERE")
        sbSQL.AppendLine("                [TVO].[StoreID] = @RefStoreID")
        sbSQL.AppendLine("                AND [TVO].[VendorID] = @VendorID")
        sbSQL.AppendLine("                AND [TVO].[DatePosted] IS NOT NULL")
        sbSQL.AppendLine("        );")

        sbSQL.AppendLine("DECLARE @GrossSales MONEY = $0.00;")
        sbSQL.AppendLine("DECLARE @ReportingPeriodIds TABLE( RPID INT );")

        sbSQL.AppendLine("DECLARE @Ingredients TABLE( IngredientID INT );")

        sbSQL.AppendLine("DECLARE @ExclusionIngredients TABLE( IngredientID INT );")

        sbSQL.AppendLine("DECLARE @ExclusionGroups TABLE( IngredientGroupID INT );")

        sbSQL.AppendLine("DECLARE @LtoOrderQty TABLE( IngredientId INT, SuggestedQty FLOAT );")

        
        If iShowItems = 2 Or iShowItems = 3 Then
            sbSQL.AppendLine("DECLARE @FilteredItems  TABLE (VendorItemID INT );")
            sbSQL.AppendLine("INSERT INTO @FilteredItems( [VendorItemID] )")
            sbSQL.AppendLine("SELECT DISTINCT")
            sbSQL.AppendLine("        [TVI].[VendorItemID]")
            sbSQL.AppendLine("FROM [dbo].[tmVendorItems] AS [TVI] WITH( NOLOCK )")
            sbSQL.AppendLine("        LEFT JOIN [dbo].[tdVendorOrder] [ORDER-DETAIL] WITH( NOLOCK )")
            sbSQL.AppendLine("            ON [ORDER-DETAIL].[VendorItemID] = [TVI].[VendorItemID]")
            sbSQL.AppendLine("        LEFT JOIN [dbo].[tmVendorOrder] [ORDER] WITH( NOLOCK )")
            sbSQL.AppendLine("            ON [ORDER].[VendorOrderID] = [ORDER-DETAIL].[VendorOrderID]")
            sbSQL.AppendLine("WHERE")
            sbSQL.AppendLine("    [TVI].[IsActive] = 1")
            sbSQL.AppendLine("    AND")
            sbSQL.AppendLine("    (")
            sbSQL.AppendLine("        [TVI].[DistributionCenterID] = @DistributionCenter")
            sbSQL.AppendLine("        OR @DistributionCenter IS NULL")
            sbSQL.AppendLine("    )")
            sbSQL.AppendLine("    AND [TVI].[VendorID] = @VendorID")

            sbSQL.AppendLine("    AND")
            sbSQL.AppendLine("    (")
            sbSQL.AppendLine("		( [ORDER].StoreID = @StoreID AND [ORDER-DETAIL].[OrderQty] > 0 )")
            If iShowItems = 3 Then
                sbSQL.AppendLine("		OR ( [TVI].[Birthday] > DATEADD( MONTH, -4, @RefDate ))")
            End If
            sbSQL.AppendLine("    )")

        End If

        sbSQL.AppendLine("/*")
        sbSQL.AppendLine("Exclude Disengaged Ingredients")
        sbSQL.AppendLine("*/")
        sbSQL.AppendLine("INSERT INTO	@ExclusionIngredients( [IngredientID] )")
        sbSQL.AppendLine("SELECT DISTINCT")
        sbSQL.AppendLine("    [PromoIng].[IngredientID]")
        sbSQL.AppendLine("FROM [dbo].[tdProdPromoIngredients] AS [PromoIng] WITH( NOLOCK )")
        sbSQL.AppendLine("     INNER JOIN [dbo].[tmProdPromo] AS [Promos] WITH( NOLOCK )")
        sbSQL.AppendLine("         ON [Promos].[PromoID] = [PromoIng].[PromoID]")
        sbSQL.AppendLine("     INNER JOIN [dbo].[tdProdPromoStores] AS [PromoStores] WITH( NOLOCK )")
        sbSQL.AppendLine("         ON [PromoStores].[PromoID] = [Promos].[PromoID]")
        sbSQL.AppendLine("         AND [PromoStores].[StoreID] = @StoreID")
        sbSQL.AppendLine("WHERE")
        sbSQL.AppendLine("    [PromoIng].[Disengage] = 1")
        sbSQL.AppendLine("    AND [Promos].[EndDate] <= @RefDate;")

        sbSQL.AppendLine("/*")
        sbSQL.AppendLine("Exclude Breakfast Groups")
        sbSQL.AppendLine("*/")
        sbSQL.AppendLine("IF @ExcludeBreakfast = 1")
        sbSQL.AppendLine("BEGIN")
        sbSQL.AppendLine("    INSERT INTO @ExclusionGroups( [IngredientGroupID] )")
        sbSQL.AppendLine("    SELECT DISTINCT")
        sbSQL.AppendLine("        [TIG].[IngredientGroupID]")
        sbSQL.AppendLine("    FROM [dbo].[tmIngredientGroup] AS [TIG]")
        sbSQL.AppendLine("    WHERE")
        sbSQL.AppendLine("        [TIG].[IngredientGroupName] LIKE '%breakfast%'")
        sbSQL.AppendLine("        AND [TIG].[IngredientGroupName] NOT LIKE '%other%';")
        sbSQL.AppendLine("END;")

        sbSQL.AppendLine("/*")
        sbSQL.AppendLine("Exclude Supplies")
        sbSQL.AppendLine("*/")
        sbSQL.AppendLine("IF @ExcludeSupplies = 1")
        sbSQL.AppendLine("BEGIN")
        sbSQL.AppendLine("    INSERT INTO @ExclusionGroups( [IngredientGroupID] )")
        sbSQL.AppendLine("    SELECT DISTINCT")
        sbSQL.AppendLine("        [TIG].[IngredientGroupID]")
        sbSQL.AppendLine("    FROM [dbo].[tmIngredientGroup] AS [TIG]")
        sbSQL.AppendLine("    WHERE")
        sbSQL.AppendLine("        [TIG].[IsFood] = 0")
        sbSQL.AppendLine("        AND [TIG].[IngredientGroupName] NOT LIKE '%paper%'")
        sbSQL.AppendLine("        AND [TIG].[IngredientGroupName] NOT LIKE '%supply%';")
        sbSQL.AppendLine("END;")

        sbSQL.AppendLine("DECLARE @IngredientStats TABLE")
        sbSQL.AppendLine("    (")
        sbSQL.AppendLine("        IngredientID INT")
        sbSQL.AppendLine("       ,StoreID INT")
        sbSQL.AppendLine("       ,LastCount DATE")
        sbSQL.AppendLine("       ,LastCountTxId INT")
        sbSQL.AppendLine("       ,CountQty FLOAT")
        sbSQL.AppendLine("            DEFAULT( 0 )")
        sbSQL.AppendLine("       ,Received FLOAT")
        sbSQL.AppendLine("            DEFAULT( 0 )")
        sbSQL.AppendLine("       ,Credit FLOAT")
        sbSQL.AppendLine("            DEFAULT( 0 )")
        sbSQL.AppendLine("       ,TransIn FLOAT")
        sbSQL.AppendLine("            DEFAULT( 0 )")
        sbSQL.AppendLine("       ,TransOut FLOAT")
        sbSQL.AppendLine("            DEFAULT( 0 )")
        sbSQL.AppendLine("       ,Theo FLOAT")
        sbSQL.AppendLine("            DEFAULT( 0 )")
        sbSQL.AppendLine("       ,GrossSales MONEY")
        sbSQL.AppendLine("            DEFAULT( 0 )")
        sbSQL.AppendLine("    );")

        sbSQL.AppendLine("/*")
        sbSQL.AppendLine("Get the list of applicable ingredients, used to filter other queries.")
        sbSQL.AppendLine("*/")
        sbSQL.AppendLine("INSERT INTO @Ingredients( [IngredientID] )")
        sbSQL.AppendLine("SELECT DISTINCT")
        sbSQL.AppendLine("    [ITM].[IngredientID]")
        sbSQL.AppendLine("FROM [dbo].[tmVendorItems] AS [ITM] WITH( NOLOCK )")
        If useAllowedItems Then
            sbSQL.AppendLine("     INNER JOIN [dbo].[tmVendorAllowedItems] AS [TVAI] WITH( NOLOCK )")
            sbSQL.AppendLine("         ON [TVAI].[DistributionCenterID] = [ITM].[DistributionCenterID]")
            sbSQL.AppendLine("             AND [TVAI].[VendorRawItemId] = [ITM].[VendorRawItemID]")
            sbSQL.AppendLine("             AND [TVAI].[VendorItemNumber] = [ITM].[VendorItemNumber]")
            sbSQL.AppendLine("             AND [TVAI].[StoreID] = @StoreID;")
        End If

        sbSQL.AppendLine("/*")
        sbSQL.AppendLine("Loading Reporting Period IDs")
        sbSQL.AppendLine("*/")
        sbSQL.AppendLine(";WITH [Dates] AS")
        sbSQL.AppendLine("    (")
        sbSQL.AppendLine("        SELECT DATEADD( DAY, -7, @RefDate ) AS [D], 1 AS [N]")
        sbSQL.AppendLine("        UNION ALL")
        sbSQL.AppendLine("        SELECT")
        sbSQL.AppendLine("            DATEADD( DAY, -7, [Dates].[D] )")
        sbSQL.AppendLine("           ,[Dates].[N] + 1 [N]")
        sbSQL.AppendLine("        FROM [Dates]")
        sbSQL.AppendLine("        WHERE")
        sbSQL.AppendLine("            [N] < @RefWeekCount")
        sbSQL.AppendLine("    )")
        sbSQL.AppendLine("INSERT INTO @ReportingPeriodIds( [RPID] )")
        sbSQL.AppendLine("SELECT DISTINCT")
        sbSQL.AppendLine("       [TRP].[ReportingPeriodID]")
        sbSQL.AppendLine("FROM [dbo].[tmReportingPeriod] AS [TRP] WITH( NOLOCK )")
        sbSQL.AppendLine("     INNER JOIN [Dates]")
        sbSQL.AppendLine("         ON [Dates].[D] BETWEEN [TRP].[ReportingStartDate] AND [TRP].[ReportingEndDate];")

        sbSQL.AppendLine("/*")
        sbSQL.AppendLine("Loading gross sales")
        sbSQL.AppendLine("*/")
        sbSQL.AppendLine("SET @GrossSales =")
        sbSQL.AppendLine("(")
        sbSQL.AppendLine("    SELECT")
        sbSQL.AppendLine("        SUM( ISNULL( [BTFS].[GrossSales], $0 ))")
        sbSQL.AppendLine("    FROM [dbo].[BI_trFinancialSummary] AS [BTFS] WITH( NOLOCK )")
        sbSQL.AppendLine("    WHERE")
        sbSQL.AppendLine("        [BTFS].[StoreID] = @StoreID")
        sbSQL.AppendLine("        AND [BTFS].[ReportingPeriodID] IN (SELECT [RPI].[RPID] FROM @ReportingPeriodIds AS [RPI])")
        sbSQL.AppendLine(");")

        sbSQL.AppendLine("/*")
        sbSQL.AppendLine("Loading LTO Qtys")
        sbSQL.AppendLine("*/")
        sbSQL.AppendLine(";WITH [ExtendedRecipes] AS")
        sbSQL.AppendLine("    (")
        sbSQL.AppendLine("        SELECT")
        sbSQL.AppendLine("            tmRecipe.[RecipeID]")
        sbSQL.AppendLine("           ,tdRecipeMat.[IngredientID]")
        sbSQL.AppendLine("           ,tdRecipeMat.[VariableUsage]")
        sbSQL.AppendLine("           ,tdRecipeMat.[UsagePercent]")
        sbSQL.AppendLine("           ,0 AS [RType]")
        sbSQL.AppendLine("        FROM [tdRecipeMat] tdRecipeMat WITH( NOLOCK )")
        sbSQL.AppendLine("             INNER JOIN [tdProdPromoIngredients] [PromoIng] WITH( NOLOCK )")
        sbSQL.AppendLine("                 ON [PromoIng].[IngredientID] = [tdRecipeMat].[IngredientID]")
        sbSQL.AppendLine("                 AND [PromoIng].[PromoID] IN ( " & sSPOS & " )") ' This is sanitized - Comma separated numbers only
        sbSQL.AppendLine("             INNER JOIN [tmRecipe] tmRecipe WITH( NOLOCK )")
        sbSQL.AppendLine("                 ON tdRecipeMat.[RecipeID] = tmRecipe.[RecipeID]")
        sbSQL.AppendLine("        WHERE")
        sbSQL.AppendLine("            [PromoIng].[Engage] = 1")
        sbSQL.AppendLine("        UNION")
        sbSQL.AppendLine("        SELECT")
        sbSQL.AppendLine("            tdRecipeSub.[RecipeID]")
        sbSQL.AppendLine("           ,tdRecipeMat.[IngredientID]")
        sbSQL.AppendLine("           ,tdRecipeMat.[VariableUsage] * tdRecipeSub.[VariableUsage] AS [VariableUsage]")
        sbSQL.AppendLine("           ,tdRecipeMat.[UsagePercent]")
        sbSQL.AppendLine("           ,tdRecipeSub.[SubRecipeID] AS [RType]")
        sbSQL.AppendLine("        FROM [tdRecipeMat] tdRecipeMat WITH( NOLOCK )")
        sbSQL.AppendLine("             INNER JOIN [tdProdPromoIngredients] [PromoIng] WITH( NOLOCK )")
        sbSQL.AppendLine("                 ON [PromoIng].[IngredientID] = [tdRecipeMat].[IngredientID]")
        sbSQL.AppendLine("                 AND [PromoIng].[PromoID] IN ( " & sSPOS & " )") ' This is sanitized - Comma separated numbers only
        sbSQL.AppendLine("             INNER JOIN [tdRecipeSub] tdRecipeSub WITH( NOLOCK )")
        sbSQL.AppendLine("                 ON tdRecipeMat.[RecipeID] = tdRecipeSub.[SubRecipeID]")
        sbSQL.AppendLine("        WHERE")
        sbSQL.AppendLine("            [PromoIng].[Engage] = 1")
        sbSQL.AppendLine("    )")
        sbSQL.AppendLine("INSERT INTO @LtoOrderQty( [IngredientId], [SuggestedQty] )")
        sbSQL.AppendLine("SELECT")
        sbSQL.AppendLine("    [ExtendedRecipes].[IngredientID]")
        sbSQL.AppendLine("   ,SUM(((( [PromoMenuItems].[APMix] / 100 ) * @ProjectedSales ) / [PromoMenuItems].[APrice] ) * ( [ExtendedRecipes].[VariableUsage] * ( [ExtendedRecipes].[UsagePercent] / 100 ))) AS [SuggestedLTO_OrderQty]")
        sbSQL.AppendLine("FROM [tmRecipe] [Recipe] WITH( NOLOCK )")
        sbSQL.AppendLine("     INNER JOIN [tmMenuItem] [MenuItems] WITH( NOLOCK )")
        sbSQL.AppendLine("         ON [Recipe].[RecipeID] = [MenuItems].[RecipeID]")
        sbSQL.AppendLine("     INNER JOIN [tdProdPromoMenuItems] [PromoMenuItems] WITH( NOLOCK )")
        sbSQL.AppendLine("         ON [PromoMenuItems].[MenuItemID] = [MenuItems].[MenuItemID]")
        sbSQL.AppendLine("     INNER JOIN [ExtendedRecipes]")
        sbSQL.AppendLine("         ON [Recipe].[RecipeID] = [ExtendedRecipes].[RecipeID]")
        sbSQL.AppendLine("     INNER JOIN [tmProdPromo] [Promos] WITH( NOLOCK )")
        sbSQL.AppendLine("         ON [PromoMenuItems].[PromoID] = [Promos].[PromoID]")
        sbSQL.AppendLine("WHERE")
        sbSQL.AppendLine("    [PromoMenuItems].[MenuItemID] IS NOT NULL")
        sbSQL.AppendLine("    AND [Recipe].[IsActive] = 1")
        sbSQL.AppendLine("    AND [Promos].[Title] IS NOT NULL")
        sbSQL.AppendLine("    AND [PromoMenuItems].[APMix] > 0")
        sbSQL.AppendLine("    AND [PromoMenuItems].[APrice] > 0")
        sbSQL.AppendLine("    AND [Promos].[PromoID] IN ( " & sSPOS & " )") 'This is sanitized above. It's only comma seperated numbers.
        sbSQL.AppendLine("GROUP BY")
        sbSQL.AppendLine("    [ExtendedRecipes].[IngredientID];")


                
        sbSQL.AppendLine("/*")
        sbSQL.AppendLine("Loading stats since last count")
        sbSQL.AppendLine("*/")
        sbSQL.AppendLine(";WITH [LatestTransactionByIng] AS")
        sbSQL.AppendLine("    (")
        sbSQL.AppendLine("        SELECT")
        sbSQL.AppendLine("            [InvTx].[IngredientID]")
        sbSQL.AppendLine("           ,[InvTx].[StoreID]")
        sbSQL.AppendLine("           ,MAX( [InvTx].[TransactionID] ) [TransactionId]")
        sbSQL.AppendLine("        FROM [dbo].[tmInventoryTransaction] AS [InvTx] WITH( NOLOCK )")
        sbSQL.AppendLine("        WHERE")
        sbSQL.AppendLine("            [InvTx].[StoreID] = @StoreID")
        sbSQL.AppendLine("            AND [InvTx].[TransactionTypeID] = 5")
        sbSQL.AppendLine("        GROUP BY")
        sbSQL.AppendLine("            [InvTx].[IngredientID]")
        sbSQL.AppendLine("           ,[InvTx].[StoreID]")
        sbSQL.AppendLine("    )")
        sbSQL.AppendLine("INSERT INTO @IngredientStats( [IngredientID], [StoreID], [LastCount], [LastCountTxId] )")
        sbSQL.AppendLine("SELECT")
        sbSQL.AppendLine("    [InvTx].[IngredientID]")
        sbSQL.AppendLine("   ,[InvTx].[StoreID]")
        sbSQL.AppendLine("   ,[InvTx].[TransactionTime]")
        sbSQL.AppendLine("   ,[LatestTransactionByIng].[TransactionId]")
        sbSQL.AppendLine("FROM @Ingredients AS [ITM]")
        sbSQL.AppendLine("     INNER JOIN [dbo].[tmInventoryTransaction] AS [InvTx] WITH( NOLOCK )")
        sbSQL.AppendLine("         ON [InvTx].[IngredientID] = [ITM].[IngredientID]")
        sbSQL.AppendLine("     INNER JOIN [LatestTransactionByIng]")
        sbSQL.AppendLine("         ON [LatestTransactionByIng].[TransactionId] = [InvTx].[TransactionID]")
        sbSQL.AppendLine("             AND [LatestTransactionByIng].[IngredientID] = [InvTx].[IngredientID];")

        sbSQL.AppendLine(";WITH [PivotedSums] AS")
        sbSQL.AppendLine("    (")

        sbSQL.AppendLine("        SELECT")
        sbSQL.AppendLine("            [PVT].[IngredientID]")
        sbSQL.AppendLine("           ,[PVT].[7] [Credit]")
        sbSQL.AppendLine("           ,[PVT].[3] [TransOut]")
        sbSQL.AppendLine("           ,[PVT].[2] [TransIn]")
        sbSQL.AppendLine("           ,[PVT].[1] [Received]")
        sbSQL.AppendLine("           ,[PVT].[5] [Count]")
        sbSQL.AppendLine("        FROM")
        sbSQL.AppendLine("        (")
        sbSQL.AppendLine("            SELECT")
        sbSQL.AppendLine("                [TxSums].[IngredientID]")
        sbSQL.AppendLine("               ,[TxSums].[TransactionTypeID]")
        sbSQL.AppendLine("               ,SUM( ISNULL( [TxSums].[Count], 0 )) [Total]")
        sbSQL.AppendLine("            FROM [dbo].[tmInventoryTransaction] AS [TxSums] WITH( NOLOCK )")
        sbSQL.AppendLine("                 INNER JOIN @IngredientStats AS [IS2]")
        sbSQL.AppendLine("                     ON [IS2].[IngredientID] = [TxSums].[IngredientID]")
        sbSQL.AppendLine("            WHERE")
        sbSQL.AppendLine("                [TxSums].[TransactionTypeID] IN ( 1, 2, 3, 7, 5 )")
        sbSQL.AppendLine("                AND [TxSums].[StoreID] = @StoreID")
        sbSQL.AppendLine("                AND [TxSums].[TransactionTime] <= @RefDate")
        sbSQL.AppendLine("                AND")
        sbSQL.AppendLine("                (")
        sbSQL.AppendLine("                    (")
        sbSQL.AppendLine("                        [TxSums].[TransactionTime] > [IS2].[LastCount]")
        sbSQL.AppendLine("                        AND [TxSums].[TransactionTypeID] <> 5")
        sbSQL.AppendLine("                    )")
        sbSQL.AppendLine("                    OR ( [TxSums].[TransactionID] = [IS2].[LastCountTxId] )")
        sbSQL.AppendLine("                )")
        sbSQL.AppendLine("            GROUP BY")
        sbSQL.AppendLine("                [TxSums].[IngredientID]")
        sbSQL.AppendLine("               ,[TxSums].[TransactionTypeID]")
        sbSQL.AppendLine("        ) [Dat]")
        sbSQL.AppendLine("        PIVOT( SUM([Dat].[Total]) FOR [TransactionTypeID] IN( [1], [2], [3], [7], [5] ) ) [PVT]")
        sbSQL.AppendLine("    )")

        sbSQL.AppendLine("UPDATE")
        sbSQL.AppendLine("    [IS]")
        sbSQL.AppendLine("SET")
        sbSQL.AppendLine("    [IS].[Credit] = ISNULL( [PS].[Credit], 0 )")
        sbSQL.AppendLine("   ,[IS].[TransOut] = ISNULL( [PS].[TransOut], 0 )")
        sbSQL.AppendLine("   ,[IS].[TransIn] = ISNULL( [PS].[TransIn], 0 )")
        sbSQL.AppendLine("   ,[IS].[Received] = ISNULL( [PS].[Received], 0 )")
        sbSQL.AppendLine("   ,[IS].[CountQty] = ISNULL( [PS].[Count], 0 )")
        sbSQL.AppendLine("FROM @IngredientStats [IS]")
        sbSQL.AppendLine("     LEFT JOIN [PivotedSums] [PS]")
        sbSQL.AppendLine("         ON [PS].[IngredientID] = [IS].[IngredientID];")

        '2019-07-18 jjc Regional recipes are no longer "a thing" and have been removed from the rollup some time ago, and not present in the GUI for a long time before that.
        'And they seem to be causing a strange issue with slowing this query way down in JirehMgy, so out with the old

        '2019-08-08 jjc So I'm ready to start fipping tables.  I ran the query created by this page in SQL against WendGord Carlsbad and it ran in around 16 seconds, running the
        'page locally took longer than I was willing to wait.   This particular section was showing up in recent expensive queries.  being that it wasn't running slow in SQL
        'I had very little to go on, So for the hell of it I took the [GeneralRecipes] CTE and turned it into a [GeneralRecipes] Subquery.  Really shouldn't have made a difference,
        'but it did.  My confidence that I won't be back here within a few weeks or less is somewhere around 5% (and is probably way too generous.

        '2019-08-09 jjc so the commented out block is running fine this morning (it got put back in the Dev folder for the sake of curiosity.)  So this change is likely
        'inconsequential beyond being different enough to use a different execution plan.  It's likely that our wendgord problems are/were indexing fragemntation related
        'for table [dbo].[BI_trStoreSalesDetail], Wendgord was reindexed this mornging, so who knows.  If you're reading this and can't think of a good reason not to,
        'I'd suggest getting rid of this code and restoring the commented block below it.  But your call, I have no power, I'm just an old comment sitting here in this file.

        'sbSQL.AppendLine("UPDATE")
        'sbSQL.AppendLine("    [IS]")
        'sbSQL.AppendLine("/*")
        'sbSQL.AppendLine("SELECT  ")
        'sbSQL.AppendLine("    [IS].[StoreID],")
        'sbSQL.AppendLine("    [IS].[IngredientID],--*/")
        'sbSQL.AppendLine("SET")
        'sbSQL.AppendLine("    [IS].[Theo] = ISNULL( [GeneralRecipes].[Qty], 0 )")
        'sbSQL.AppendLine("FROM @IngredientStats AS [IS]")
        'sbSQL.AppendLine("     INNER JOIN ")
        'sbSQL.AppendLine("    (")
        'sbSQL.AppendLine("        SELECT")
        'sbSQL.AppendLine("            [IS].[IngredientID]")
        'sbSQL.AppendLine("           ,[IS].[StoreID]")
        'sbSQL.AppendLine("           ,ISNULL( SUM( [BTSSD].[QtySold] * [TMIRE].[RecipeUsageQty] ), 0 ) [Qty]")
        'sbSQL.AppendLine("           ,COUNT( 1 ) [C]")
        'sbSQL.AppendLine("        FROM @IngredientStats AS [IS]")
        'sbSQL.AppendLine("             INNER JOIN [dbo].[tmStore] AS [TS] WITH( NOLOCK )")
        'sbSQL.AppendLine("                 ON [TS].[StoreID] = [IS].[StoreID]")
        'sbSQL.AppendLine("             INNER JOIN [dbo].[tdMenuItemRecipeExtension] AS [TMIRE] WITH( NOLOCK )")
        'sbSQL.AppendLine("                 ON [TMIRE].[IngredientID] = [IS].[IngredientID]")
        'sbSQL.AppendLine("                     AND [TMIRE].[RegionNum] IS NULL")
        'sbSQL.AppendLine("             INNER JOIN [dbo].[tmPLU] AS [TP] WITH( NOLOCK )")
        'sbSQL.AppendLine("                 ON [TP].[MenuItemID] = [TMIRE].[MenuItemID]")
        'sbSQL.AppendLine("             INNER JOIN [dbo].[BI_trStoreSalesDetail] AS [BTSSD] WITH( NOLOCK )")
        'sbSQL.AppendLine("                 ON [BTSSD].[PLUMasterID] = [TP].[PLUMasterID]")
        'sbSQL.AppendLine("                     AND [BTSSD].[BusinessDate] <= @RefDate")
        'sbSQL.AppendLine("                     AND [BTSSD].[BusinessDate] > [IS].[LastCount]")
        'sbSQL.AppendLine("                     AND [BTSSD].[StoreID] = [IS].[StoreID]")
        'sbSQL.AppendLine("        GROUP BY")
        'sbSQL.AppendLine("            [IS].[IngredientID]")
        'sbSQL.AppendLine("           ,[IS].[StoreID]")
        'sbSQL.AppendLine("    ) AS [GeneralRecipes]")
        'sbSQL.AppendLine("         ON [GeneralRecipes].[StoreID] = [IS].[StoreID]")
        'sbSQL.AppendLine("             AND [GeneralRecipes].[IngredientID] = [IS].[IngredientID];")


        'Put this back
        sbSQL.AppendLine(";WITH [GeneralRecipes] AS")
        sbSQL.AppendLine("    (")
        sbSQL.AppendLine("        SELECT")
        sbSQL.AppendLine("            [IS].[IngredientID]")
        sbSQL.AppendLine("           ,[IS].[StoreID]")
        sbSQL.AppendLine("           ,ISNULL( SUM( [BTSSD].[QtySold] * [TMIRE].[RecipeUsageQty] ), 0 ) [Qty]")
        sbSQL.AppendLine("           ,COUNT( 1 ) [C]")
        sbSQL.AppendLine("        FROM @IngredientStats AS [IS]")
        sbSQL.AppendLine("             INNER JOIN [dbo].[tmStore] AS [TS] WITH( NOLOCK )")
        sbSQL.AppendLine("                 ON [TS].[StoreID] = [IS].[StoreID]")
        sbSQL.AppendLine("             INNER JOIN [dbo].[tdMenuItemRecipeExtension] AS [TMIRE] WITH( NOLOCK )")
        sbSQL.AppendLine("                 ON [TMIRE].[IngredientID] = [IS].[IngredientID]")
        sbSQL.AppendLine("                     AND [TMIRE].[RegionNum] IS NULL")
        sbSQL.AppendLine("             INNER JOIN [dbo].[tmPLU] AS [TP] WITH( NOLOCK )")
        sbSQL.AppendLine("                 ON [TP].[MenuItemID] = [TMIRE].[MenuItemID]")
        sbSQL.AppendLine("             INNER JOIN [dbo].[BI_trStoreSalesDetail] AS [BTSSD] WITH (NOLOCK, INDEX([IX_BI_trStoreSalesDetail_StoreID_PLUID_ReportingPeriodID]))")
        sbSQL.AppendLine("                 ON [BTSSD].[PLUMasterID] = [TP].[PLUMasterID]")
        sbSQL.AppendLine("                     AND [BTSSD].[BusinessDate] <= @RefDate")
        sbSQL.AppendLine("                     AND [BTSSD].[BusinessDate] > [IS].[LastCount]")
        sbSQL.AppendLine("                     AND [BTSSD].[StoreID] = [IS].[StoreID]")
        sbSQL.AppendLine("        GROUP BY")
        sbSQL.AppendLine("            [IS].[IngredientID]")
        sbSQL.AppendLine("           ,[IS].[StoreID]")
        sbSQL.AppendLine("    )")
        sbSQL.AppendLine("UPDATE")
        sbSQL.AppendLine("    [IS]")
        sbSQL.AppendLine("/*")
        sbSQL.AppendLine("SELECT  ")
        sbSQL.AppendLine("    [IS].[StoreID],")
        sbSQL.AppendLine("    [IS].[IngredientID],--*/")
        sbSQL.AppendLine("SET")
        sbSQL.AppendLine("    [IS].[Theo] = ISNULL( [GeneralRecipes].[Qty], 0 )")
        sbSQL.AppendLine("FROM @IngredientStats AS [IS]")
        sbSQL.AppendLine("     INNER JOIN [GeneralRecipes]")
        sbSQL.AppendLine("         ON [GeneralRecipes].[StoreID] = [IS].[StoreID]")
        sbSQL.AppendLine("             AND [GeneralRecipes].[IngredientID] = [IS].[IngredientID];")

        sbSQL.AppendLine(";WITH [GrossSalesByIngredient] AS")
        sbSQL.AppendLine("    (")
        sbSQL.AppendLine("        SELECT")
        sbSQL.AppendLine("            [BTFS].[StoreID]")
        sbSQL.AppendLine("           ,[IS].[IngredientID]")
        sbSQL.AppendLine("           ,SUM( [BTFS].[GrossSales] ) [GrossSales]")
        sbSQL.AppendLine("        FROM @IngredientStats AS [IS]")
        sbSQL.AppendLine("             INNER JOIN [dbo].[BI_trFinancialSummary] AS [BTFS] WITH( NOLOCK )")
        sbSQL.AppendLine("                 ON [BTFS].[StoreID] = [IS].[StoreID]")
        sbSQL.AppendLine("                     AND [BTFS].[BusinessDate] > [IS].[LastCount]")
        sbSQL.AppendLine("        GROUP BY")
        sbSQL.AppendLine("            [BTFS].[StoreID]")
        sbSQL.AppendLine("           ,[IS].[IngredientID]")
        sbSQL.AppendLine("    )")
        sbSQL.AppendLine("UPDATE")
        sbSQL.AppendLine("    [IS]")
        sbSQL.AppendLine("/*")
        sbSQL.AppendLine("SELECT")
        sbSQL.AppendLine("    [BTFS].[StoreID],")
        sbSQL.AppendLine("    [IS].[IngredientID],--*/")
        sbSQL.AppendLine("SET")
        sbSQL.AppendLine("    [IS].[GrossSales] = [GSBI].[GrossSales]")
        sbSQL.AppendLine("FROM @IngredientStats AS [IS]")
        sbSQL.AppendLine("     INNER JOIN [GrossSalesByIngredient] [GSBI]")
        sbSQL.AppendLine("         ON [GSBI].[IngredientID] = [IS].[IngredientID]")
        sbSQL.AppendLine("             AND [GSBI].[StoreID] = [IS].[StoreID];")

        sbSQL.AppendLine("DECLARE @LatestInvoice TABLE")
        sbSQL.AppendLine("	([VendorItemID] INT")
        sbSQL.AppendLine("	,[InventoryUnitPrice] FLOAT")
        sbSQL.AppendLine("	,[UnitPrice] FLOAT")
        sbSQL.AppendLine("	)")
        sbSQL.AppendLine("")
        sbSQL.AppendLine("INSERT @LatestInvoice")
        sbSQL.AppendLine("	([VendorItemID]")
        sbSQL.AppendLine("	,[InventoryUnitPrice]")
        sbSQL.AppendLine("	,[UnitPrice]")
        sbSQL.AppendLine("	)")
        sbSQL.AppendLine("")
        sbSQL.AppendLine("SELECT")
        sbSQL.AppendLine("    [Dat].[VendorItemID]")
        sbSQL.AppendLine("    ,[Dat].[InventoryUnitPrice]")
        sbSQL.AppendLine("    ,[Dat].[UnitPrice]")
        sbSQL.AppendLine("FROM")
        sbSQL.AppendLine("(")
        sbSQL.AppendLine("    SELECT")
        sbSQL.AppendLine("        [InvoiceData].[VendorItemID]")
        sbSQL.AppendLine("        ,[InvoiceData].[InventoryUnitPrice]")
        sbSQL.AppendLine("        ,CASE ISNULL( [ReceivedUnitPrice], 0 )")
        sbSQL.AppendLine("            WHEN 0 THEN ISNULL( [InvoiceUnitPrice], 0 )")
        sbSQL.AppendLine("            ELSE ISNULL( [ReceivedUnitPrice], 0 )")
        sbSQL.AppendLine("        END [UnitPrice]")
        sbSQL.AppendLine("        ,RANK() OVER ( PARTITION BY")
        sbSQL.AppendLine("                            [InvoiceData].[VendorItemID]")
        sbSQL.AppendLine("                        ORDER BY")
        sbSQL.AppendLine("                            [InvoiceData].[LineItemID] DESC")
        sbSQL.AppendLine("                    ) [Rnk]")
        sbSQL.AppendLine("    FROM [dbo].[tmVendorInvoice] AS [Invoice] WITH( NOLOCK )")
        sbSQL.AppendLine("            INNER JOIN [dbo].[tdVendorInvoice] AS [InvoiceData] WITH( NOLOCK )")
        sbSQL.AppendLine("                ON [Invoice].[VendorID] = @VendorID")
        sbSQL.AppendLine("				AND [InvoiceData].[VendorInvoiceID] = [Invoice].[VendorInvoiceID]")
        sbSQL.AppendLine("				AND [Invoice].InvoiceDate > DATEADD(dd, -60, GETDATE())")
        sbSQL.AppendLine("    WHERE [InvoiceData].[InvoiceUnitPrice] <> 0")
        sbSQL.AppendLine(") [Dat]")
        sbSQL.AppendLine("WHERE")
        sbSQL.AppendLine("    [Dat].[Rnk] = 1")
        sbSQL.AppendLine("")

        sbSQL.AppendLine(";WITH [CTE_trInventory] AS")
        sbSQL.AppendLine("    (")
        sbSQL.AppendLine("        SELECT")
        sbSQL.AppendLine("            [INV].[IngredientID]")
        sbSQL.AppendLine("           ,[INV].[StoreID]")
        sbSQL.AppendLine("           ,SUM( ISNULL( [INV].[P2_ActualUsage], 0 )) [ActualUsage]")
        sbSQL.AppendLine("           ,SUM( ISNULL( [INV].[P2_TheoreticalUsage], 0 )) [TheoreticalUsage]")
        sbSQL.AppendLine("        FROM @Ingredients [ITM]")
        sbSQL.AppendLine("             INNER JOIN [dbo].[INV_trInventory] AS [INV] WITH( NOLOCK )")
        sbSQL.AppendLine("                 ON [INV].[IngredientID] = [ITM].[IngredientID]")
        sbSQL.AppendLine("             INNER JOIN [dbo].[tmReportingPeriod] AS [TRP] WITH( NOLOCK )")
        sbSQL.AppendLine("                 ON [INV].[BusinessDate] = [TRP].[ReportingEndDate]")
        sbSQL.AppendLine("        WHERE")
        sbSQL.AppendLine("            [INV].[ReportingPeriodID] IN (SELECT [RPI].[RPID] FROM @ReportingPeriodIds AS [RPI])")
        sbSQL.AppendLine("        GROUP BY")
        sbSQL.AppendLine("            [INV].[IngredientID]")
        sbSQL.AppendLine("           ,[INV].[StoreID]")
        sbSQL.AppendLine("    )")
        'sbSQL.AppendLine("    ,[LatestInvoice] AS")
        'sbSQL.AppendLine("    (")
        'sbSQL.AppendLine("        SELECT")
        'sbSQL.AppendLine("            [Dat].[VendorItemID]")
        'sbSQL.AppendLine("           ,[Dat].[InventoryUnitPrice]")
        'sbSQL.AppendLine("           ,[Dat].[UnitPrice]")
        'sbSQL.AppendLine("        FROM")
        'sbSQL.AppendLine("        (")
        'sbSQL.AppendLine("            SELECT")
        'sbSQL.AppendLine("                [InvoiceData].[VendorItemID]")
        'sbSQL.AppendLine("               ,[InvoiceData].[InventoryUnitPrice]")
        'sbSQL.AppendLine("               ,CASE ISNULL( [ReceivedUnitPrice], 0 )")
        'sbSQL.AppendLine("                    WHEN 0 THEN ISNULL( [InvoiceUnitPrice], 0 )")
        'sbSQL.AppendLine("                    ELSE ISNULL( [ReceivedUnitPrice], 0 )")
        'sbSQL.AppendLine("                END [UnitPrice]")
        'sbSQL.AppendLine("               ,RANK() OVER ( PARTITION BY")
        'sbSQL.AppendLine("                                  [InvoiceData].[VendorItemID]")
        'sbSQL.AppendLine("                              ORDER BY")
        'sbSQL.AppendLine("                                  [InvoiceData].[LineItemID] DESC")
        'sbSQL.AppendLine("                            ) [Rnk]")
        'sbSQL.AppendLine("            FROM [dbo].[tmVendor] AS [VNDR] WITH( NOLOCK )")
        'sbSQL.AppendLine("                 LEFT JOIN [dbo].[tmVendorInvoice] AS [Invoice] WITH( NOLOCK )")
        'sbSQL.AppendLine("                     ON [Invoice].[VendorID] = [VNDR].[VendorID]")
        'sbSQL.AppendLine("                 LEFT JOIN [dbo].[tdVendorInvoice] AS [InvoiceData] WITH( NOLOCK )")
        'sbSQL.AppendLine("                     ON [InvoiceData].[VendorInvoiceID] = [Invoice].[VendorInvoiceID]")
        'sbSQL.AppendLine("            WHERE")
        'sbSQL.AppendLine("                [VNDR].[VendorID] = @VendorID")
        'sbSQL.AppendLine("				--2021-09-11 jjc")
        'sbSQL.AppendLine("				--This query is still a pain when the database can't be properly indexed.  But we certainly don't need invoices from all time")
        'sbSQL.AppendLine("				AND [Invoice].InvoiceDate > DATEADD(DD, -60, GETDATE())")
        '                AND [InvoiceData].[InvoiceUnitPrice] <> 0")
        'sbSQL.AppendLine("        ) [Dat]")
        'sbSQL.AppendLine("        WHERE")
        'sbSQL.AppendLine("            [Dat].[Rnk] = 1")
        'sbSQL.AppendLine("    )")
        sbSQL.AppendLine("    ,[LatestOrder] AS")
        sbSQL.AppendLine("    (")
        sbSQL.AppendLine("        SELECT")
        sbSQL.AppendLine("            [VOD].[VendorItemID]")
        sbSQL.AppendLine("           ,ISNULL( SUM( [OrderQty] ), 0 ) [Qty]")
        sbSQL.AppendLine("        FROM dbo.[tdVendorOrder] VOD WITH( NOLOCK )")
        sbSQL.AppendLine("        WHERE")
        sbSQL.AppendLine("            VOD.[VendorOrderID] IN")
        sbSQL.AppendLine("            (")
        sbSQL.AppendLine("                SELECT")
        sbSQL.AppendLine("                    [VendorOrderID]")
        sbSQL.AppendLine("                FROM dbo.[tmVendorOrder] WITH( NOLOCK )")
        sbSQL.AppendLine("                WHERE")
        sbSQL.AppendLine("                    [StoreID] = @StoreID")
        sbSQL.AppendLine("                    AND [BusinessDate] = @LastOrderDate")
        sbSQL.AppendLine("            )")
        sbSQL.AppendLine("        GROUP BY")
        sbSQL.AppendLine("            [VOD].[VendorItemID]")
        sbSQL.AppendLine("    )")
        sbSQL.AppendLine("    ,[CountSheetSequence] AS")
        sbSQL.AppendLine("    (")
        sbSQL.AppendLine("        SELECT")
        sbSQL.AppendLine("            [Sheet].[CountSheetID]")
        sbSQL.AppendLine("           ,[Sheet].[StoreID]")
        sbSQL.AppendLine("           ,[Sheet-Data].[IngredientID]")
        sbSQL.AppendLine("           ,[Sheet-Data].[WalkingOrder]")
        sbSQL.AppendLine("           ,[Priority].[Priority]")
        sbSQL.AppendLine("           ,ROW_NUMBER() OVER ( PARTITION BY [Sheet].[StoreID], [Sheet-Data].[IngredientID] ORDER BY [Priority].[Priority] ) AS [RNK]")
        sbSQL.AppendLine("        FROM [dbo].[tmCountSheetOrderingSequence] AS [Priority] WITH( NOLOCK )")
        sbSQL.AppendLine("             INNER JOIN [dbo].[tmCountSheet] AS [Sheet] WITH( NOLOCK )")
        sbSQL.AppendLine("                 ON [Priority].[CountSheetID] = [Sheet].[CountSheetID]")
        sbSQL.AppendLine("                     AND [Priority].[Used] = 1")
        sbSQL.AppendLine("                     AND [Sheet].[StoreID] = [Priority].[StoreID]")
        sbSQL.AppendLine("             INNER JOIN [dbo].[tdCountSheet] AS [Sheet-Data] WITH( NOLOCK )")
        sbSQL.AppendLine("                 ON [Sheet].[CountSheetID] = [Sheet-Data].[CountSheetID]")
        sbSQL.AppendLine("                     AND [Sheet-Data].[IsActive] = 1")
        sbSQL.AppendLine("        WHERE")
        sbSQL.AppendLine("            [Sheet].[StoreID] = @StoreID")
        sbSQL.AppendLine("    )")
        sbSQL.AppendLine("SELECT")
        sbSQL.AppendLine("    CASE [ITM].[SplitCase]")
        sbSQL.AppendLine("        WHEN 0 THEN [ITM].[VendorItemNumber]")
        sbSQL.AppendLine("        ELSE [ITM].[VendorItemNumber] + 'S'")
        sbSQL.AppendLine("    END [VendorItemNumber]")
        sbSQL.AppendLine("   ,CASE [ITM].[SplitCase]")
        sbSQL.AppendLine("        WHEN 0 THEN [ITM].[VendorItemDesc]")
        sbSQL.AppendLine("        ELSE [ITM].[VendorItemDesc] + ' (split)'")
        sbSQL.AppendLine("    END [VendorItemDesc]")
        sbSQL.AppendLine("   ,0.0000 AS [ProjectedUsage]")
        sbSQL.AppendLine("   ,0.0000 AS [OProjectedUsage]")
        sbSQL.AppendLine("   ,0.0000 AS [OnHand]")
        sbSQL.AppendLine("   ,[ING].[IsPriority]")
        sbSQL.AppendLine("   ,ISNULL( [LatestOrder].[Qty], 0 ) AS [PreviousOrders]")
        sbSQL.AppendLine("   ,0.0000 AS [OrderQty]")
        sbSQL.AppendLine("   ,ISNULL( [LatestInvoice].[UnitPrice], 0 ) [UnitPrice]")
        sbSQL.AppendLine("   ,0.0000 AS [Extension]")
        sbSQL.AppendLine("   ,0.0000 AS [SOGQty]")
        sbSQL.AppendLine("   ,0 AS [LineItemID]")
        sbSQL.AppendLine("   ,@StoreID AS [StoreID]")
        sbSQL.AppendLine("   ,[ITM].[VendorItemID]")
        sbSQL.AppendLine("   ,[ITM].[StandardCaseWeight]")
        sbSQL.AppendLine("   ,[ITM].[IsCatchWeight]")
        sbSQL.AppendLine("   ,CASE [ITM].[SplitCase]")
        sbSQL.AppendLine("        WHEN 0 THEN [ITM-M].[UnitMeasureDescription]")
        sbSQL.AppendLine("        ELSE [SPLIT-M].[UnitMeasureDescription]")
        sbSQL.AppendLine("    END [UnitMeasureDescription]")
        sbSQL.AppendLine("   ,[LatestInvoice].[InventoryUnitPrice]")
        sbSQL.AppendLine("   ,[ITM].[OrderUnitMultiplier]")
        sbSQL.AppendLine("   ,[ING].[IngredientID]")
        sbSQL.AppendLine("   ,[ING].[IngredientNumber]")
        sbSQL.AppendLine("   ,[ING].[IngredientName]")
        sbSQL.AppendLine("   ,[ING].[IngredientName] [IngredientDescription]")
        sbSQL.AppendLine("   ,[ING-M].[UnitMeasureDescription] [IngredientUnitMeasure]")
        sbSQL.AppendLine("   ,[ING].[IsIncidental]")
        sbSQL.AppendLine("   ,[ING].[AlwaysSuggestZero]")
        sbSQL.AppendLine("   ,ISNULL( [INV].[ActualUsage], 0 ) AS [ActualUsage]")
        sbSQL.AppendLine("   ,ISNULL( [INV].[TheoreticalUsage], 0 ) AS [TheoreticalUsage]")
        sbSQL.AppendLine("   ,ISNULL(@GrossSales, 0) AS [BasisGrossSales]")
        sbSQL.AppendLine("   ,ISNULL( [ITM].[PresentationOrder], 0 ) [PresentationOrder]")
        sbSQL.AppendLine("   ,[ING].[WalkingOrder]")
        sbSQL.AppendLine("   ,[I-Stats].[LastCount] [LastCountDate]")
        sbSQL.AppendLine("   ,CASE [ITM].[OrderUnitMultiplier]")
        sbSQL.AppendLine("        WHEN 0 THEN 0")
        sbSQL.AppendLine("        ELSE ISNULL( [I-Stats].[CountQty], 0 ) / ISNULL( [ITM].[OrderUnitMultiplier], 1 )")
        sbSQL.AppendLine("    END [LastActualCount]")
        sbSQL.AppendLine("   ,CASE [ITM].[OrderUnitMultiplier]")
        sbSQL.AppendLine("        WHEN 0 THEN 0")
        sbSQL.AppendLine("        ELSE ISNULL( [I-Stats].[Theo], 0 ) / ISNULL( [ITM].[OrderUnitMultiplier], 1 )")
        sbSQL.AppendLine("    END [TheoreticalSinceCount]")
        sbSQL.AppendLine("   ,CASE [ITM].[OrderUnitMultiplier]")
        sbSQL.AppendLine("        WHEN 0 THEN 0")
        sbSQL.AppendLine("        ELSE ISNULL( [I-Stats].[Received], 0 ) / ISNULL( [ITM].[OrderUnitMultiplier], 1 )")
        sbSQL.AppendLine("    END [ReceivedSinceCount]")
        sbSQL.AppendLine("   ,CASE [ITM].[OrderUnitMultiplier]")
        sbSQL.AppendLine("        WHEN 0 THEN 0")
        sbSQL.AppendLine("        ELSE ISNULL( [I-Stats].[Credit], 0 ) / ISNULL( [ITM].[OrderUnitMultiplier], 1 )")
        sbSQL.AppendLine("    END [CreditSinceCount]")
        sbSQL.AppendLine("   ,CASE [ITM].[OrderUnitMultiplier]")
        sbSQL.AppendLine("        WHEN 0 THEN 0")
        sbSQL.AppendLine("        ELSE ISNULL( [I-Stats].[TransIn], 0 ) / ISNULL( [ITM].[OrderUnitMultiplier], 1 )")
        sbSQL.AppendLine("    END [TranInSinceCount]")
        sbSQL.AppendLine("   ,CASE [ITM].[OrderUnitMultiplier]")
        sbSQL.AppendLine("        WHEN 0 THEN 0")
        sbSQL.AppendLine("        ELSE ISNULL( [I-Stats].[TransOut], 0 ) / ISNULL( [ITM].[OrderUnitMultiplier], 1 )")
        sbSQL.AppendLine("    END [TranOutSinceCount]")
        sbSQL.AppendLine("   ,ISNULL( [I-Stats].[GrossSales], $0 ) AS GrossSalesSinceLastCount")
        sbSQL.AppendLine("   ,0.0000 AS [BasisUsage]")
        sbSQL.AppendLine("   ,0 AS [TheoBasis]")
        sbSQL.AppendLine("   ,0.0000 AS [DollarYield]")
        sbSQL.AppendLine("   ,0 AS [IncidentalUsageSinceCount]")
        sbSQL.AppendLine("   ,@ProjectedSales AS [ProjectedGrossSales]")
        sbSQL.AppendLine("   ,'Some Calculation' AS [Calc]")
        sbSQL.AppendLine("   ,0.0000 AS [InventoryOrderQty]")
        sbSQL.AppendLine("   ,[LTO].[SuggestedQty] [SuggestedLTO_OrderQty]")
        sbSQL.AppendLine("   ,0.0000 AS [LTOInfluence]")
        sbSQL.AppendLine("   ,[ING].[ThawTime]")
        sbSQL.AppendLine("   ,0.0000 AS [ThawTimeInfluence]")
        sbSQL.AppendLine("   ,[ITM].[Birthday]")
        sbSQL.AppendLine("   ,[ITM].[SplitCase]")
        sbSQL.AppendLine("FROM [dbo].[tmVendorItems] AS [ITM] WITH( NOLOCK )")
        If useAllowedItems Then
            sbSQL.AppendLine("     INNER JOIN [dbo].[tmVendorAllowedItems] AS [TVAI] WITH( NOLOCK )")
            sbSQL.AppendLine("         ON [TVAI].[DistributionCenterID] = [ITM].[DistributionCenterID]")
            sbSQL.AppendLine("             AND [TVAI].[VendorRawItemId] = [ITM].[VendorRawItemID]")
            sbSQL.AppendLine("             AND [TVAI].[VendorItemNumber] = [ITM].[VendorItemNumber]")
            sbSQL.AppendLine("             AND [TVAI].[StoreID] = @StoreID")
        End If

        sbSQL.AppendLine("     INNER JOIN [dbo].[tmIngredient] AS [ING] WITH( NOLOCK )")
        sbSQL.AppendLine("         ON [ING].[IngredientID] = [ITM].[IngredientID]")
        sbSQL.AppendLine("     LEFT JOIN [CTE_trInventory] [INV] WITH( NOLOCK )")
        sbSQL.AppendLine("         ON [INV].[IngredientID] = [ITM].[IngredientID]")
        sbSQL.AppendLine("             AND [INV].[StoreID] = @RefStoreID")
        'sbSQL.AppendLine("     LEFT JOIN [LatestInvoice] WITH( NOLOCK )")
        sbSQL.AppendLine("     LEFT JOIN @LatestInvoice AS [LatestInvoice]")
        sbSQL.AppendLine("         ON [LatestInvoice].[VendorItemID] = [ITM].[VendorItemID]")
        sbSQL.AppendLine("     LEFT JOIN [LatestOrder]")
        sbSQL.AppendLine("         ON [LatestOrder].[VendorItemID] = [ITM].[VendorItemID]")
        sbSQL.AppendLine("     LEFT JOIN [dbo].[tmUnitMeasure] AS [ING-M] WITH( NOLOCK )")
        sbSQL.AppendLine("         ON [ING-M].[UnitMeasureID] = [ING].[UnitMeasureID]")
        sbSQL.AppendLine("     LEFT JOIN [dbo].[tmUnitMeasure] AS [ITM-M] WITH( NOLOCK )")
        sbSQL.AppendLine("         ON [ITM-M].[UnitMeasureID] = [ITM].[UnitMeasureID]")
        sbSQL.AppendLine("     LEFT JOIN [dbo].[tmIngredientCountMethod] AS [SPLIT-ICM] WITH( NOLOCK )")
        sbSQL.AppendLine("         ON [SPLIT-ICM].[IngredientID] = [ITM].[IngredientID]")
        sbSQL.AppendLine("             AND [SPLIT-ICM].[CountMethodNum] = 2")
        sbSQL.AppendLine("     LEFT JOIN [dbo].[tmUnitMeasure] AS [SPLIT-M] WITH( NOLOCK )")
        sbSQL.AppendLine("         ON [SPLIT-M].[UnitMeasureID] = [SPLIT-ICM].[UnitMeasureID]")
        sbSQL.AppendLine("     LEFT JOIN @LtoOrderQty AS [LTO]")
        sbSQL.AppendLine("         ON [LTO].[IngredientId] = [ITM].[IngredientID]")
        sbSQL.AppendLine("     LEFT JOIN @IngredientStats AS [I-Stats]")
        sbSQL.AppendLine("         ON [I-Stats].[IngredientID] = [ITM].[IngredientID]")
        sbSQL.AppendLine("             AND [I-Stats].[StoreID] = @StoreID")
        sbSQL.AppendLine("     LEFT JOIN [CountSheetSequence] [Sequence]")
        sbSQL.AppendLine("         ON [Sequence].[IngredientID] = [ING].[IngredientID]")
        sbSQL.AppendLine("             AND [Sequence].[RNK] = 1")
        sbSQL.AppendLine("             AND [Sequence].[StoreID] = @StoreID")
        sbSQL.AppendLine("WHERE")
        sbSQL.AppendLine("    [ITM].[VendorID] = @VendorID -- Vendor ID")
        sbSQL.AppendLine("    AND [ITM].[IsActive] = 1 --Item is active")
        sbSQL.AppendLine("    AND [ITM].[ShowOnSOG] = 1 -- Item is on sog")

        sbSQL.AppendLine("    AND")
        sbSQL.AppendLine("    (")
        sbSQL.AppendLine("        [ITM].[DistributionCenterID] = @DistributionCenter")
        sbSQL.AppendLine("        OR @DistributionCenter IS NULL")
        sbSQL.AppendLine("    )")

        sbSQL.AppendLine("    AND [ING].[IsActive] = 1 --Ingredient is active")
        sbSQL.AppendLine("    AND [ING].[IngredientGroupID] NOT IN( SELECT [EG].[IngredientGroupID] FROM @ExclusionGroups AS [EG] )")
        sbSQL.AppendLine("    AND [ING].[IngredientID] NOT IN ( SELECT [EI].[IngredientID] FROM @ExclusionIngredients AS [EI])")


        
        If iShowItems = 2 Or iShowItems = 3 Then
            sbSQL.AppendLine("    AND [ITM].[VendorItemID] IN ( SELECT [FI].[VendorItemID] FROM @FilteredItems AS [FI] )")
        End If

        sbSQL.AppendLine("ORDER BY")
        Select Case iSortOrder
            Case 1
                sbSQL.AppendLine("    [ITM].[VendorItemNumber],")
            Case 2
                sbSQL.AppendLine("    [ITM].[VendorItemDesc],")
            Case 3
                sbSQL.AppendLine("    ISNULL( [Sequence].[RNK], 999),")
                sbSQL.AppendLine("    [Sequence].[Priority],")
                sbSQL.AppendLine("    [Sequence].[WalkingOrder],")
                sbSQL.AppendLine("    [ITM].[VendorItemID],")
        End Select
        sbSQL.AppendLine("    [ING].[WalkingOrder]")
        sbSQL.AppendLine("   ,[ING].[IngredientName]")
        sbSQL.AppendLine("   ,[ITM].[PresentationOrder] ")
        sbSQL.AppendLine("   ,[ITM].[SplitCase] ")

        If iSortOrder <> 1 Then
            sbSQL.AppendLine("   ,[ITM].[VendorItemNumber] ")
        End If 

        If iSortOrder <> 3 Then
            sbSQL.AppendLine("   ,[ITM].[VendorItemID] ")
        End If

        If False 'Testing Stuff, drag in here as needed
            sbTestVariables.AppendLine(String.Format(""))
            sbTestVariables.AppendLine(String.Format("SET @StoreID = {0}", iStoreID))
            sbTestVariables.AppendLine(String.Format("SET @RefStoreID = {0}", nRefStoreID))
            sbTestVariables.AppendLine(String.Format("SET @RefWeekCount = {0}", iWksTrend))
            sbTestVariables.AppendLine(String.Format("SET @RefDate = '{0}'",  Date.Today.ToString("yyyy-MM-dd")))
            sbTestVariables.AppendLine(String.Format("SET @ProjectedSales = {0}", dProjectedSales))
            sbTestVariables.AppendLine(String.Format("SET @VendorID = {0}", iVendorID))
            sbTestVariables.AppendLine(String.Format("SET @DistributionCenter = '{0}'", sDistributionCenterID))
            sbTestVariables.AppendLine(String.Format("SET @ExcludeBreakfast = {0}", iif(Not IsStoreBreakfast(iStoreID),"1","0")))
            sbTestVariables.AppendLine(String.Format("SET @ExcludeSupplies = {0}", iif(Not bShowSupplies,"1","0")))
            sbTestVariables.AppendLine(String.Format(""))
            Dim sTestVariables = sbTestVariables.ToString
        End If

        SBOMaster.CalcTimings("Get SOG: Prepare Query")

        Using command As New SqlCommand(sbSQL.ToString)
            command.Parameters.Add("@StoreID", SqlDbType.Int).Value = iStoreID
            command.Parameters.Add("@RefStoreID", SqlDbType.Int).Value = nRefStoreID
            command.Parameters.Add("@RefWeekCount", SqlDbType.Int).Value = iWksTrend
            command.Parameters.Add("@RefDate", SqlDbType.Date).Value = Date.Today
            command.Parameters.Add("@ProjectedSales", SqlDbType.Money).Value = dProjectedSales
            command.Parameters.Add("@VendorID", SqlDbType.Int).Value = iVendorID
            With command.Parameters.Add("@DistributionCenter", SqlDbType.VarChar, 10)
                If Not IsNothing(sDistributionCenterID) AndAlso sDistributionCenterID <> "" AndAlso sDistributionCenterID <> "-1" Then
                    .Value = sDistributionCenterID
                Else 
                    .Value = DBNull.Value
                End If
            End With

            command.Parameters.Add("@ExcludeBreakfast", SqlDbType.Bit).Value = Not IsStoreBreakfast(iStoreID)
            command.Parameters.Add("@ExcludeSupplies", SqlDbType.Bit).Value = Not bShowSupplies

            ttable = InvWSS.Platform.SBODBExecQuerySQLCommand("SBOCore", command)
        End Using


        For Each row As DataRow In ttable.Rows
            'iRowCount += 1
            'Determine BasisUsage
            If row("ActualUsage") = 0 Then
                row("BasisUsage") = row("TheoreticalUsage")
            Else
                row("BasisUsage") = row("ActualUsage")
            End If

            'convert InventoryUnits to purchase units
            If IsDBNull(row("OrderUnitMultiplier")) Then
                row("OrderUnitMultiplier") = 1
            End If
            If row("OrderUnitMultiplier") > 0 Then
                row("BasisUsage") = row("BasisUsage") / row("OrderUnitMultiplier")
            End If

            'Calculate dollar yield in purchase units usage per dollar gross sales in basis period
            If row("BasisGrossSales") <> 0 AndAlso (row("BasisUsage") / row("BasisGrossSales")) <> 0 Then
                'DAF 3/23/2011
                'PER SK/SW change(DY = 1 / (BU / BGS))
                'row("DollarYield") = row("BasisUsage") / row("BasisGrossSales")
                row("DollarYield") = 1 / (row("BasisUsage") / row("BasisGrossSales"))
            Else
                row("DollarYield") = 0
            End If

            'Multiply projected gross sales by the dollar to yield projected usage in purchase units
            'DAF 3/23/2011 - see line 508
            'row("ProjectedUsage") = row("DollarYield") * row("ProjectedGrossSales")
            If row("BasisGrossSales") <> 0 Then
                row("ProjectedUsage") = (row("BasisUsage") / row("BasisGrossSales")) * row("ProjectedGrossSales")
            Else
                row("ProjectedUsage") = 0
            End If

            'DAF 7/22/2011
            'IncidentalUsageSinceCount was not being set
            If row("DollarYield") <> 0 Then
                row("IncidentalUsageSinceCount") = row("GrossSalesSinceLastCount") / row("DollarYield")
            Else
                row("IncidentalUsageSinceCount") = 0
            End If

            row("OProjectedUsage") = row("ProjectedUsage")
            row("OrderQty") = row("ProjectedUsage")
            row("SOGQty") = row("OrderQty")

            'Determine LTO Influence
            Dim LTOProUsage As Double = 0
            Dim bUseLTO As Boolean = False
            If Not IsDBNull(row("SuggestedLTO_OrderQty")) Then
                If row("OrderUnitMultiplier") <> 0.0 Then
                    LTOProUsage = row("SuggestedLTO_OrderQty") / row("OrderUnitMultiplier")
                Else
                    trProcessMessage.Visible = False
                    LTOProUsage = row("SuggestedLTO_OrderQty") / 1
                    lblPOInfluence.Text = "<div style=""color: #000000;font-size: 9pt; border: solid 2px #000000; background-color:#7693D9;padding: 7pt;width: 500px;"">"
                    lblPOInfluence.Text &= "An error has been detected which will require attention by the WebServices "
                    lblPOInfluence.Text &= "team at DUMAC.  Please use the [Support] button in the upper right corner of "
                    lblPOInfluence.Text &= "your screen to open a help request.  Please indicate that you were preparing a "
                    lblPOInfluence.Text &= "Suggested Order when this message appeared.  Thank you."
                    lblPOInfluence.Text &= "<br /><br />**Order Unit Multiplier not set: " & row("VendorItemNumber") & " - " & row("VendorItemDesc") & "**</div>"
                    btnSave.Enabled = False
                    btnSubmit.Enabled = False
                    btnResub.Enabled = False
                    btnApprove.Visible = False
                    Return Nothing
                End If
                'If LTOSOGQty is more than SOGQty	                
                If LTOProUsage > row("SOGQty") Then
                    bUseLTO = True
                End If
            End If

            If bUseLTO Then
                row("LTOInfluence") = LTOProUsage - row("ProjectedUsage")
                row("ProjectedUsage") += row("LTOInfluence")
            End If

            'Determine Thaw Time Influence
            Dim nBaseThaw As Double = 0
            If Not IsDBNull(row("ThawTime")) AndAlso row("ThawTime") <> 0 AndAlso orderdays <> 0 Then
                row("ThawTimeInfluence") = (row("ProjectedUsage") / orderdays) * row("ThawTime")
                row("ProjectedUsage") += row("ThawTimeInfluence")
                'row("OrderQty") = row("ProjectedUsage")
            End If

            'Calculate OnHand
            row("OnHand") = row("LastActualCount") - IIf(row("IsIncidental") = 0, row("TheoreticalSinceCount"), row("IncidentalUsageSinceCount"))
            row("OnHand") += row("ReceivedSinceCount")
            row("OnHand") += row("TranInSinceCount")
            row("OnHand") -= row("TranOutSinceCount")
            row("OnHand") -= row("CreditSinceCount")

            'Back items LastCount or already ordered out of the projected usage to get the suggested order
            row("OrderQty") = row("ProjectedUsage") - row("OnHand")
            row("SOGQty") = row("OrderQty")

            If bIncludePreviousOrder Then
                If Not IsDBNull(row("PreviousOrders")) Then
                    row("OrderQty") -= row("PreviousOrders")
                    row("SOGQty") = row("OrderQty")
                End If
            End If

            '03/11/2011 DAF
            'If an item is a catchweight and has a StandardCaseWeight assigned, the item is order by the case, not by the pound.
            'i.e. 1 case = 20 lbs.  SOG will currently suggest 45lbs.  This will convert to 2.25 cases.  Round up to 3 cases and put on new order as 3.
            If Not IsDBNull(row("IsCatchWeight")) AndAlso row("IsCatchWeight") AndAlso row("StandardCaseWeight") > 0 Then
                row("OrderQty") = row("OrderQty") / row("StandardCaseWeight")
                '13JUL2017 LAY S37/B-03013 Adjusted unit price for CatchWeightItems
                'In these instances the initial UnitPrice is the price per pound but since we're
                'ordering by the Case the UnitPrice should be mulitplied by the StandardCaseWeight (lbs per Case)
                'so that the Total Price for the item is correct.
                row("UnitPrice") = row("UnitPrice") * row("StandardCaseWeight")
            End If

            Dim dFraction As Double = 0
            dFraction = row("OrderQty") - Math.Floor(row("OrderQty"))
            If SOGRoundUp = 0 Then SOGRoundUp = 0.1
            If dFraction > SOGRoundUp Then
                row("OrderQty") = Math.Ceiling(row("OrderQty"))
            Else
                row("OrderQty") = Math.Floor(row("OrderQty"))
            End If

            row("SOGQty") = row("OrderQty")

            'calculate extended costs
            If Not IsDBNull(row("UnitPrice")) Then
                row("Extension") = row("OrderQty") * row("UnitPrice")
            End If

            'Fill in the inventory units suggested
            row("InventoryOrderQty") = row("OrderQty") * row("OrderUnitMultiplier")

            'zero any items we have in abundance
            If row("OrderQty") < 0 Or row("SplitCase") Then
                row("OrderQty") = 0
                row("SOGQty") = 0
            End If

            If row("AlwaysSuggestZero") Or row("SplitCase") Then
                row("OrderQty") = 0
                'Mark Williams Bug 1072
                row("SOGQty") = 0
            End If
        Next

        SBOMaster.CalcTimings("Get SOG: Process ttable")
        Return ttable
    End Function

    Function IsStoreBreakfast(ByVal storeId As Integer) As Boolean

        Dim tTable As New DataTable
        Dim sbSubSQL As New StringBuilder

        sbSubSQL.AppendLine("      SELECT  ")
        sbSubSQL.AppendLine("          Breakfast  ")
        sbSubSQL.AppendLine("      FROM  ")
        sbSubSQL.AppendLine("          [tmStoreInfo] WITH(NOLOCK)")
        sbSubSQL.AppendLine("      WHERE  ")
        sbSubSQL.AppendLine("          [StoreID] = " & storeId.ToString & " ")

        If Not InvWSS.GetData(sbSubSQL.ToString, tTable) Then
            bStoreDoesBreakfast = False
            Return False
        End If

        For Each Row As DataRow In tTable.Rows
            If(Not IsDBNull(Row("Breakfast")))
                bStoreDoesBreakfast = Row("Breakfast")
            Else
                bStoreDoesBreakfast = False
            End If
            Return bStoreDoesBreakfast
        Next

    End Function

    Function GetOrderDistCtrName(ByVal OrderID As Integer, ByRef DistCtrID As String) As String
        Dim sSQL As String
        Dim tTable As New DataTable
        Dim row As DataRow
        Dim sDistCtrName As String = ""
        sSQL = "select ISNULL(distributioncentername,'') AS DistCtr, vo.DistributionCenterID AS DistCtrID from tmvendororder vo WITH(NOLOCK) "
        sSQL &= "left outer join tmVendorDistributionCenter dc WITH(NOLOCK) on vo.DistributionCenterID = dc.DistributionCenterID "
        sSQL &= "WHERE vo.VendorOrderID = " & OrderID.ToString
        InvWSS.GetData(sSQL, tTable)
        For Each row In tTable.Rows
            If Not IsDBNull(row("DistCtrID")) Then
                sDistCtrName = row("DistCtr")
                DistCtrID = row("DistCtrID")
            End If
        Next
        tTable.Dispose()
        Return sDistCtrName
    End Function

    Function GetDistCtrName(ByVal DistCtrID As String) As String
        Dim sSQL As String
        Dim tTable As New DataTable
        Dim row As DataRow
        Dim sDistCtrName As String = ""
        sSQL = "select ISNULL(distributioncentername,'') AS DistCtr from tmVendorDistributionCenter dc WITH(NOLOCK) "
        sSQL &= "WHERE dc.DistributionCenterID = '" & DistCtrID & "'"
        InvWSS.GetData(sSQL, tTable)
        For Each row In tTable.Rows
            sDistCtrName = row("DistCtr")
        Next
        tTable.Dispose()
        Return sDistCtrName
    End Function

    Sub SetDistCtr(ByVal OrderID As Integer, ByVal DistCtrID As String)
        Dim sSQL As String
        Dim SQLCmd As SqlCommand
        Dim SQLCon As New SqlConnection(Session("ConnectString"))
        sSQL = "UPDATE tmVendorOrder SET DistributionCenterID = '" & DistCtrID & "' WHERE VendorOrderID = " & OrderID
        Try
            SQLCon.Open()
            SQLCmd = New SqlCommand(sSQL, SQLCon)
            SQLCmd.ExecuteNonQuery()
            SQLCmd.Dispose()
            SQLCon.Close()
            SQLCon.Dispose()
        Catch eSetDistCtr As Exception
        End Try
    End Sub

    Function VendorDoesEOrders(ByVal iVendID As Integer) As Boolean
        Dim sSQL As String
        Dim tTable As New DataTable
        Dim row As DataRow
        Dim bResult As Boolean = False
        sSQL = "SELECT HasElectronicOrders FROM tmVendor WITH(NOLOCK) WHERE VendorID = " & iVendID
        InvWSS.GetData(sSQL, tTable)
        For Each row In tTable.Rows
            bResult = row("HasElectronicOrders")
        Next
        'Response.Write(bResult)
        Return bResult
    End Function

    Sub BindGrid(ByVal Orders As DataTable)
        If Not IsNothing(Orders) AndAlso Orders.Rows.Count > 0 Then
            MyDataGrid.DataSource = Orders.DefaultView
            MyDataGrid.DataBind()
        End If
    End Sub

    Sub btnUpdate_click(ByVal sender As Object, ByVal e As EventArgs)
        Dim sError As String = ""
        Dim tOrder As New DataTable
        tOrder = Session("VOrder")
        sError = GetUserValues(tOrder)
        If Len(sError) Then
            Response.Redirect("VendorOrders30.aspx")
        Else
            BindGrid(tOrder)
        End If
    End Sub

    Sub MyDataGrid_ItemDataBound(ByVal source As Object, ByVal e As DataGridItemEventArgs)
        Dim dItemTotal As Double
        Dim sOrderUnitDesc As String = ""

        Select Case e.Item.ItemType
            Case ListItemType.Header
            Case ListItemType.Footer
                e.Item.Cells(eGridColumns.RowNumber).ColumnSpan = 2
                e.Item.Cells(eGridColumns.RowNumber).Text = TotalItems & " total items"
            Case ListItemType.Item, ListItemType.AlternatingItem
                'relalc SOGQty

                TotalItems += 1
                Dim tbQty As TextBox = CType(e.Item.FindControl("txtPurchUnits"), TextBox)
                Dim etxt As String = "validateQty(" & e.Item.ItemIndex & "," & "this," & e.Item.DataItem("SOGQty") & "," & e.Item.DataItem("UnitPrice") & ")"
                tbQty.Attributes.Add("onblur", etxt)
                'e.Item.Cells( eGridColumns.VendorItemNumber).Text = e.Item.ItemIndex
                e.Item.Cells(eGridColumns.VendorItemNumber).ToolTip = e.Item.DataItem("Birthday")
                e.Item.Cells(eGridColumns.RowNumber).Text = TotalItems
                If Not IsDBNull(e.Item.DataItem("OrderQty")) Then
                    CaseCount += e.Item.DataItem("OrderQty")
                    If Not IsDBNull(e.Item.DataItem("UnitPrice")) Then
                        dItemTotal = e.Item.DataItem("OrderQty") * e.Item.DataItem("UnitPrice")
                        If dItemTotal > 0 Then
                            e.Item.Cells(eGridColumns.TotalPrice).Text = Format(dItemTotal, "$" & sFormat_Decimal)
                            gTotal += Math.Round(dItemTotal, 2)
                        Else
                            e.Item.Cells(eGridColumns.TotalPrice).Text = "$0.00"
                        End If
                        If e.Item.DataItem("UnitPrice") = 0 Then
                            e.Item.Cells(eGridColumns.UnitPrice).Text = ""
                        End If
                    End If


                    'Mark Williams Bug 348
                    'If e.Item.DataItem("OrderQty") > MaxVendorOrderQty Then
                    '    tbQty.BackColor = System.Drawing.ColorTranslator.FromHtml("#FF0000")
                    '    tbQty.ToolTip = "CAUTION: The vendor allows for a max order quantity of " & MaxVendorOrderQty

                    'End If

                    If Not IsDBNull(e.Item.DataItem("SOGQty")) Then
                        If e.Item.DataItem("OrderQty") > MaxVendorOrderQty Then
                            tbQty.BackColor = System.Drawing.ColorTranslator.FromHtml("#FF0000")
                            tbQty.ToolTip = "CAUTION: The vendor allows for a max order quantity of " & MaxVendorOrderQty
                            txtCheckMax.Value = ""
                        ElseIf e.Item.DataItem("OrderQty") > (e.Item.DataItem("SOGQty") * (1 + SOGQtyVarPct)) Then
                            tbQty.BackColor = System.Drawing.ColorTranslator.FromHtml("#ffdd00")
                            tbQty.ToolTip = "CAUTION: The amount entered is not within +/- 25% of the suggested quantity (" & e.Item.DataItem("SOGQty") & ")"
                        Else
                            If e.Item.DataItem("OrderQty") < (e.Item.DataItem("SOGQty") * (1 - SOGQtyVarPct)) Then
                                tbQty.BackColor = System.Drawing.ColorTranslator.FromHtml("#ffdd00")
                                tbQty.ToolTip = "CAUTION: The amount entered is not within +/- 25% of the suggested quantity (" & e.Item.DataItem("SOGQty") & ")"
                            Else
                                tbQty.BackColor = System.Drawing.ColorTranslator.FromHtml("#ffffff")
                                tbQty.ToolTip = ""
                            End If
                        End If
                    End If
                End If

                If VendorOrderNum <> -1 Then
                    Try
                        If Not IsDBNull(e.Item.DataItem("Calc")) Then
                            e.Item.Cells(eGridColumns.Calculation).Text = e.Item.DataItem("Calc")
                            Dim calcvals() As String = (e.Item.DataItem("calc") & ",0,0,0,0,0,0,0").ToString.Split(",")
                            If Val(calcvals(16)) = 1 Then
                                e.Item.Cells(eGridColumns.VendorItemDesc).Attributes.Add("bgcolor", "#9ED8A7")
                                e.Item.Cells(eGridColumns.VendorItemDesc).Text = "<span style=""color:#00000;font-weight:bold;"">" & e.Item.DataItem("VendorItemDesc") & "</span>"
                            Else
                                e.Item.Cells(eGridColumns.VendorItemDesc).Text = "<span>" & e.Item.DataItem("VendorItemDesc") & "</span>"
                            End If

                            sOrderUnitDesc = calcvals(18)

                            If Val(calcvals(15)) = 0 And Val(calcvals(24)) = 0 Then
                                e.Item.Cells(eGridColumns.PreviousOrders).Text = ""
                            End If
                            If Len(ProjectedSales.Text) = 0 Then ProjectedSales.Text = Format(Val(calcvals(1)), "#")
                            If calcvals.GetUpperBound(0) >= 27 Then
                                If Len(lblDSSForecast.Text) = 0 Then lblDSSForecast.Text = calcvals(27)
                            End If

                            If Len(lblBasisDt.Text) = 0 Then lblBasisDt.Text = calcvals(19)

                            If Len(InvtOrderDays.Text) = 0 Then InvtOrderDays.Text = calcvals(20)

                            If calcvals(0) > 0 Then
                                e.Item.Cells(eGridColumns.DollarYield).Text = "$" & CInt(calcvals(0))
                            Else
                                e.Item.Cells(eGridColumns.DollarYield).Text = "$0.00"
                            End If
                        End If
                    Catch eIsIncidental As Exception
                        lblErrorMessage.Text &= eIsIncidental.Message
                        lblErrorMessage.Visible = True
                    End Try

                Else
                    Dim sCalc As String
                    Dim SOGCalc1 As Double

                    If IsDBNull(e.Item.DataItem("Onhand")) Then
                        e.Item.DataItem("OnHand") = 0
                    End If
                    If IsDBNull(e.Item.DataItem("PreviousOrders")) Then
                        e.Item.DataItem("PreviousOrders") = 0
                    End If
                    If IsDBNull(e.Item.DataItem("ProjectedUsage")) Then
                        e.Item.DataItem("ProjectedUsage") = 0
                    End If
                    If IsDBNull(e.Item.DataItem("IsIncidental")) Then
                        e.Item.DataItem("IsIncidental") = 0
                    End If
                    If IsDBNull(e.Item.DataItem("IncidentalUsageSinceCount")) Then
                        e.Item.DataItem("IncidentalUsageSinceCount") = 0
                    End If

                    Dim nDollarYield As Integer

                    Try
                        nDollarYield = CInt(e.Item.DataItem("DollarYield"))
                    Catch ex As Exception
                        lblErrorMessage.Text &= "VendorItemNumber: " & e.Item.DataItem("VendorItemNumber") & "<br />"
                        lblErrorMessage.Text &= "VendorItemDesc: " & e.Item.DataItem("VendorItemDesc") & "<br />"
                        lblErrorMessage.Text &= "DollarYield: " & e.Item.DataItem("DollarYield") & "<br />" & ex.Message & "<br /><br />"
                        lblErrorMessage.Visible = True

                        nDollarYield = 0
                    End Try

                    sCalc = nDollarYield                           '0
                    sCalc &= "," & Format(e.Item.DataItem("ProjectedGrossSales"), "0.#000")         '1
                    sCalc &= "," & Format(e.Item.DataItem("OProjectedUsage"), "0.#000")             '2
                    sCalc &= "," & e.Item.DataItem("BasisUsage")                                    '3
                    sCalc &= "," & e.Item.DataItem("BasisGrossSales")                               '4
                    sCalc &= "," & Format(e.Item.DataItem("Onhand"), "0.#000")                      '5
                    sCalc &= "," & Format(e.Item.DataItem("PreviousOrders"), "0.#000")              '6

                    If ShowPrevOrd = True Then
                        SOGCalc1 = Format(e.Item.DataItem("OProjectedUsage") - e.Item.DataItem("Onhand") - e.Item.DataItem("PreviousOrders"), "0.#000")
                    Else
                        SOGCalc1 = Format(e.Item.DataItem("OProjectedUsage") - e.Item.DataItem("Onhand"), "0.#000")
                    End If

                    sCalc &= "," & Format(e.Item.DataItem("OProjectedUsage") + e.Item.DataItem("LTOInfluence") + e.Item.DataItem("ThawTimeInfluence"), "0.#000")
                    sCalc &= "," & e.Item.DataItem("SOGQty")                                    '8
                    sCalc &= "," & Format(e.Item.DataItem("TranInSinceCount"), "0.#000")                              '9
                    sCalc &= "," & Format(e.Item.DataItem("TranOutSinceCount"), "0.#000")                             '10
                    sCalc &= "," & Format(e.Item.DataItem("ReceivedSinceCount"), "0.#000")                        '11
                    sCalc &= "," & Format(e.Item.DataItem("TheoreticalSinceCount"), "0.#000")                         '12
                    sCalc &= "," & e.Item.DataItem("LastCountDate")                             '13
                    sCalc &= "," & Format(e.Item.DataItem("LastActualCount"), "0.#000")                           '14

                    If ShowPrevOrd Then                                            '15
                        sCalc &= ",1"
                    Else
                        sCalc &= ",0"
                    End If

                    If ShowPrevOrd = False And ShowPrevOrdNoInfluence = False Then
                        e.Item.Cells(eGridColumns.PreviousOrders).Text = ""
                    End If

                    sCalc &= "," & IIf(e.Item.DataItem("IsIncidental") <> 0, "1", "0")
                    sCalc &= "," & Format(e.Item.DataItem("IncidentalUsageSinceCount"), "0.#000")                     '17

                    If Not IsDBNull(e.Item.DataItem("OrderUnitMultiplier")) Then
                        sOrderUnitDesc &= e.Item.DataItem("OrderUnitMultiplier")
                    Else
                        sOrderUnitDesc &= "_"
                    End If
                    If Not IsDBNull(e.Item.DataItem("IngredientUnitMeasure")) Then
                        sOrderUnitDesc &= " " & e.Item.DataItem("IngredientUnitMeasure")
                    Else
                        sOrderUnitDesc &= " _"
                    End If
                    If Not IsDBNull(e.Item.DataItem("UnitMeasureDescription")) Then
                        sOrderUnitDesc &= " per " & e.Item.DataItem("UnitMeasureDescription")
                    Else
                        sOrderUnitDesc &= " per _"
                    End If

                    sCalc &= "," & sOrderUnitDesc                                                           '18  
                    sCalc &= "," & dBasisdate                                                                '19
                    sCalc &= "," & orderdays

                    If Not IsDBNull(e.Item.DataItem("ThawTime")) Then
                        sCalc &= "," & Format(e.Item.DataItem("ThawTimeInfluence"), "0.#000")     '21
                    Else
                        sCalc &= ",0"
                    End If

                    If ShowPrevOrd = True Then
                        sCalc &= "," & Format(e.Item.DataItem("ProjectedUsage") - e.Item.DataItem("Onhand") - e.Item.DataItem("PreviousOrders"), "0.#000")
                    Else
                        sCalc &= "," & Format(e.Item.DataItem("ProjectedUsage") - e.Item.DataItem("Onhand"), "0.#000")
                    End If
                    If Not IsDBNull(e.Item.DataItem("SuggestedLTO_OrderQty")) AndAlso e.Item.DataItem("SuggestedLTO_OrderQty") > 0 Then
                        sCalc &= "," & Format(e.Item.DataItem("LTOInfluence"), "0.#000")                                 '23
                    Else
                        sCalc &= ",0"
                    End If
                    If ShowPrevOrdNoInfluence = True Then   '24
                        sCalc &= ",1"
                    Else
                        sCalc &= ",0"
                    End If

                    '25
                    If Not IsDBNull(e.Item.DataItem("IsCatchWeight")) AndAlso e.Item.DataItem("IsCatchWeight") AndAlso e.Item.DataItem("StandardCaseWeight") > 0 Then
                        sCalc &= "," & e.Item.DataItem("StandardCaseWeight")
                    Else
                        sCalc &= ",0"
                    End If

                    '26 Credits
                    If Not IsDBNull(e.Item.DataItem("CreditSinceCount")) Then
                        sCalc &= "," & Format(e.Item.DataItem("CreditSinceCount"), "0.#000")
                    Else
                        sCalc &= ",0"
                    End If

                    '27 DSSProjectedSales
                    If Val(lblDSSForecast.Text) > 0 Then
                        sCalc &= "," & lblDSSForecast.Text
                    Else
                        sCalc &= ",0"
                    End If

                    e.Item.Cells(eGridColumns.Calculation).Text = sCalc

                    If e.Item.DataItem("IsIncidental") <> 0 Then
                        e.Item.Cells(eGridColumns.VendorItemDesc).Attributes.Add("bgcolor", "#9ED8A7")
                        e.Item.Cells(eGridColumns.VendorItemDesc).Text = "<span title=""" & sOrderUnitDesc & """ style=""color:#00000;"">" & e.Item.DataItem("VendorItemDesc") & "</span>"
                    Else
                        e.Item.Cells(eGridColumns.VendorItemDesc).Text = "<span title=""" & sOrderUnitDesc & """>" & e.Item.DataItem("VendorItemDesc") & "</span>"
                    End If

                    e.Item.Cells(eGridColumns.DollarYield).Text = "$" & nDollarYield
                End If

                If Not IsDBNull(e.Item.DataItem("calc")) Then
                    e.Item.Cells(eGridColumns.SOGQty).Attributes.Add("onclick", "javascript:displaycalc(" & e.Item.ItemIndex & ",event);return false;")
                End If

                If Not IsDBNull(e.Item.DataItem("OnHand")) Then
                    If e.Item.DataItem("OnHand") < 0 Then
                        e.Item.Cells(eGridColumns.OnHand).BackColor = System.Drawing.ColorTranslator.FromHtml("#ff4444")
                    End If
                    e.Item.Cells(eGridColumns.OnHand).Text = Format(e.Item.DataItem("OnHand"), "0.#00")
                End If

                If Not IsDBNull(e.Item.DataItem("IsPriority")) Then
                    If e.Item.DataItem("IsPriority") Then
                        e.Item.Cells(eGridColumns.VendorItemNumber).BackColor = System.Drawing.ColorTranslator.FromHtml("#00ffff")
                    End If
                End If

                Dim qtybox As TextBox
                If Len(ApprovedBy.Text) Then
                    qtybox = e.Item.FindControl("txtPurchUnits")
                    qtybox.ReadOnly = True

                Else
                    If e.Item.ItemIndex = 0 Then
                        qtybox = e.Item.FindControl("txtPurchUnits")
                        e.Item.Focus()
                    End If
                End If

                If Not IsDBNull(e.Item.DataItem("SuggestedLTO_OrderQty")) Then
                    e.Item.Cells(eGridColumns.VendorItemDesc).Attributes.Add("bgcolor", "#E9967A")
                End If

                If Not IsDBNull(e.Item.DataItem("ThawTime")) Then
                    If e.Item.DataItem("ThawTime") > 0 Then
                        e.Item.Cells(eGridColumns.ProjectedUsage).Attributes.Add("bgcolor", "#00BFFF")
                    End If
                End If
                If Not IsDBNull(e.Item.DataItem("AlwaysSuggestZero")) Then
                    If e.Item.DataItem("AlwaysSuggestZero") <> 0 Then
                        e.Item.Cells(eGridColumns.VendorItemDesc).Attributes.Add("bgcolor", "#B896D0")
                        e.Item.Cells(eGridColumns.VendorItemDesc).ToolTip = ""
                    End If
                End If
                'B-02683 - Core Life: SOG - Add OUM Column: "Note: if standard case size is >0, description should be "case""
                If Not IsDBNull(e.Item.DataItem("StandardCaseWeight")) AndAlso e.Item.DataItem("StandardCaseWeight") > 0 Then
                    'e.Item.Cells(eGridColumns.UnitMeasureDescription).Text = "Case"
                    e.Item.Cells(eGridColumns.UnitMeasureDescription).Text = "<span title=""" & sOrderUnitDesc & """>Case</span>"
                Else
                    e.Item.Cells(eGridColumns.UnitMeasureDescription).Text = "<span title=""" & sOrderUnitDesc & """>" & e.Item.DataItem("UnitMeasureDescription") & "</span>"
                End If


                If Not IsDBNull(e.Item.DataItem("IsCatchWeight")) AndAlso e.Item.DataItem("IsCatchWeight") Then
                    e.Item.Cells(eGridColumns.VendorItemDesc).Attributes.Add("bgcolor", "#E558EF")
                End If

                e.Item.Cells(eGridColumns.SOGQty).Attributes.Add("style", "text-decoration: underline; cursor: pointer; color:blue;")
        End Select
    End Sub

    Friend Sub MyDataGrid_ItemCreated(ByVal sender As Object, ByVal e As DataGridItemEventArgs)
        If (e.Item.ItemType = ListItemType.Footer) Then
            e.Item.Cells(eGridColumns.UnitPrice).Text = Format(gTotal, "$" & sFormat_Decimal)
            e.Item.Cells(eGridColumns.PreviousOrders).Text = CaseCount
        End If
    End Sub

    Function GetUserValues(ByRef Order As DataTable) As String
        Dim i As Integer
        Dim _item As DataGridItem
        Dim dr As DataRow
        Dim sResult As String = ""

        For i = 0 To MyDataGrid.Items.Count - 1
            _item = MyDataGrid.Items(i)
            Try
                dr = Order.Rows(i)
                Dim PurchUnits As TextBox = _item.FindControl("txtPurchUnits")
                If PurchUnits.Text = "" Then
                    dr("OrderQty") = 0
                Else
                    dr("OrderQty") = Val(PurchUnits.Text)
                End If
                If IsDBNull(dr("OrderQty")) Then
                    dr("Quantity") = 0
                End If
                If IsDBNull(dr("UnitPrice")) Then
                    dr("UnitPrice") = 0
                End If
                dr("Calc") = MyDataGrid.Items(i).Cells.Item(eGridColumns.Calculation).Text
            Catch eVal As Exception
                sResult = eVal.Message
                Exit For
            End Try
        Next
        Return sResult
    End Function

    Function GetPriorityLessThanSuggested(ByRef Order As DataTable) As List(Of String)

        Dim _item As DataGridItem
        Dim dr As DataRow
        Dim priorityLessThanSuggested As List(Of String) = New List(Of String)

        For i = 0 To MyDataGrid.Items.Count - 1
            _item = MyDataGrid.Items(i)
            dr = Order.Rows(i)
            If dr("IsPriority") And ((dr("OrderQty") = 0 Or dr.IsNull("OrderQty")) Or dr("OrderQty") < dr("SOGQty")) Then
                Order.Rows(i)("PriorityLessThanSuggested") = 1
            Else
                Order.Rows(i)("PriorityLessThanSuggested") = 0
            End If
        Next

        Return priorityLessThanSuggested
    End Function

    Function GetManuallyChangedZero(ByRef Order As DataTable) As List(Of String)
        Dim _item As DataGridItem
        Dim dr As DataRow
        Dim manuallyChangedZero As List(Of String) = New List(Of String)

        For i = 0 To MyDataGrid.Items.Count - 1
            _item = MyDataGrid.Items(i)
            dr = Order.Rows(i)
            If (dr("OrderQty") = 0 Or dr.IsNull("OrderQty")) And Not dr("SOGQty") = 0 Then
                Order.Rows(i)("ManuallyChangedZero") = 1
            Else
                Order.Rows(i)("ManuallyChangedZero") = 0
            End If
        Next

        Return manuallyChangedZero
    End Function

    Function GetGreaterThanSuggested(ByRef Order As DataTable) As List(Of String)
        Dim _item As DataGridItem
        Dim dr As DataRow
        Dim greaterThanSuggested As List(Of String) = New List(Of String)

        For i = 0 To MyDataGrid.Items.Count - 1
            _item = MyDataGrid.Items(i)
            dr = Order.Rows(i)
            If dr("OrderQty") > dr("SOGQty") Then
                Order.Rows(i)("GreaterThanSuggested") = 1
            Else
                Order.Rows(i)("GreaterThanSuggested") = 0
            End If
        Next
        Return greaterThanSuggested
    End Function

    Sub btnSubmit_click(ByVal sender As Object, ByVal e As EventArgs)

        Dim OrderToSubmit As New DataTable
        Dim sError As String = ""

        OrderToSubmit = Session("VOrder")
        sError = GetUserValues(OrderToSubmit)
        If (Not OrderToSubmit.Columns.Contains("PriorityLessThanSuggested")) Then
            OrderToSubmit.Columns.Add("PriorityLessThanSuggested")
        End If
        If (Not OrderToSubmit.Columns.Contains("ManuallyChangedZero")) Then
            OrderToSubmit.Columns.Add("ManuallyChangedZero")
        End If
        If (Not OrderToSubmit.Columns.Contains("GreaterThanSuggested")) Then
            OrderToSubmit.Columns.Add("GreaterThanSuggested")
        End If
        GetPriorityLessThanSuggested(OrderToSubmit)
        GetManuallyChangedZero(OrderToSubmit)
        GetGreaterThanSuggested(OrderToSubmit)
        If Len(sError) = 0 Then
            Dim OrderNum As Integer
            If Len(vonum.Text) > 0 Then
                OrderNum = Val(vonum.Text)
            End If

            If PostOrder(OrderNum, Val(txtvendorid.Text), Val(txtstoreid.Text), Val(txtpo.Text), CType(txtbusdate.Text, Date), OrderToSubmit) Then
                VendorOrderNum = OrderNum
                SetDistCtr(OrderNum, txtDistCtrID.Text)
            End If

            PostOrderApproved(OrderNum)

            Dim iInvoiceID As Integer
            If AutoGenInvoiceOp <> 0 Then
                InvWSS.VendorOrderToInvoice(VendorOrderNum, iInvoiceID)
            End If
        End If
        Response.Redirect("VendorOrders.aspx?storeid=" & txtstoreid.Text)

    End Sub

    Sub btnSave_click(ByVal sender As Object, ByVal e As EventArgs)
        Dim OrderToSave As New DataTable
        Dim sError As String = ""
        OrderToSave = Session("VOrder")
        sError = GetUserValues(OrderToSave)
        If (Not OrderToSave.Columns.Contains("PriorityLessThanSuggested")) Then
            OrderToSave.Columns.Add("PriorityLessThanSuggested")
        End If
        If (Not OrderToSave.Columns.Contains("ManuallyChangedZero")) Then
            OrderToSave.Columns.Add("ManuallyChangedZero")
        End If
        If (Not OrderToSave.Columns.Contains("GreaterThanSuggested")) Then
            OrderToSave.Columns.Add("GreaterThanSuggested")
        End If
        GetPriorityLessThanSuggested(OrderToSave)
        GetManuallyChangedZero(OrderToSave)
        GetGreaterThanSuggested(OrderToSave)
        If Len(sError) = 0 Then
            Dim OrderNum As Integer

            If Len(vonum.Text) > 0 Then
                OrderNum = Val(vonum.Text)
            End If

            If PostOrder(OrderNum, Val(txtvendorid.Text), Val(txtstoreid.Text), Val(txtpo.Text), CType(txtbusdate.Text, Date), OrderToSave) Then

                VendorOrderNum = OrderNum
                SetDistCtr(OrderNum, txtDistCtrID.Text)
            End If
        End If

        Response.Redirect("VendorOrders.aspx?storeid=" & txtstoreid.Text)
    End Sub

    Sub btnSave_AutoSave_click(ByVal sender As Object, ByVal e As EventArgs)
        Dim OrderToSave As New DataTable
        Dim sError As String = ""
        Dim OrderNum As Integer

        OrderToSave = Session("VOrder")


        sError = GetUserValues(OrderToSave)
        If (Not OrderToSave.Columns.Contains("PriorityLessThanSuggested")) Then
            OrderToSave.Columns.Add("PriorityLessThanSuggested")
        End If
        If (Not OrderToSave.Columns.Contains("ManuallyChangedZero")) Then
            OrderToSave.Columns.Add("ManuallyChangedZero")
        End If
        If (Not OrderToSave.Columns.Contains("GreaterThanSuggested")) Then
            OrderToSave.Columns.Add("GreaterThanSuggested")
        End If
        GetPriorityLessThanSuggested(OrderToSave)
        GetManuallyChangedZero(OrderToSave)
        GetGreaterThanSuggested(OrderToSave)
        If Len(sError) = 0 Then

            If Len(vonum.Text) > 0 Then
                OrderNum = Val(vonum.Text)
            End If

            If PostOrder(OrderNum, Val(txtvendorid.Text), Val(txtstoreid.Text), Val(txtpo.Text), CType(txtbusdate.Text, Date), OrderToSave) Then

                VendorOrderNum = OrderNum
                SetDistCtr(OrderNum, txtDistCtrID.Text)
            End If

        End If

        If bStartAutoSave And OrderNum > 0 Then
            Response.Redirect("NewOrderLTO.aspx?vonum=" & OrderNum)
        Else
            Response.Redirect("VendorOrders.aspx?storeid=" & txtstoreid.Text)
        End If
    End Sub

    Sub btnResend_click(ByVal sender As Object, ByVal e As EventArgs)

        InvWSS.Platform.SBODBExecNonQuerySQL("SBOCore", "UPDATE tmVendorOrder SET ExportFileName = NULL, DatePosted = NULL WHERE VendorOrderID = " & vonum.Text)

        '2017-04-20 DAF - Jocely, Now this will work. 
        InvWSS.ProcessOrdersAllVendors()

        Response.Redirect("VendorOrders.aspx?storeid=" & txtstoreid.Text)

    End Sub

    Function PostOrder(ByRef rnVendorOrderID As Integer, ByVal vnVendorID As Integer, ByVal vnStoreID As Integer, ByVal vnPurchaseOrderNumber As Integer, ByVal vtBusinessDate As DateTime, ByRef robjEntries As DataTable) As Boolean
        Dim flag2 As Boolean
        Try
            Dim flag As Boolean = PostOrderHeader(rnVendorOrderID, vnVendorID, vnStoreID, vnPurchaseOrderNumber, vtBusinessDate)
            If (IIf(((flag AndAlso (Not robjEntries Is Nothing)) AndAlso (robjEntries.Rows.Count > 0)), 1, 0) <> 0) Then
                flag = PostOrderDetail(rnVendorOrderID, robjEntries)
            End If
            flag2 = flag
        Catch exception1 As Exception
            flag2 = False
        End Try
        Return flag2
    End Function

    Function PostOrderHeader(ByRef rnVendorOrderID As Integer, ByVal vnVendorID As Integer, ByVal vnStoreID As Integer, ByVal vnPurchaseOrderNumber As Integer, ByVal vtBusinessDate As DateTime) As Boolean
        Dim flag As Boolean
        Dim dbCon As New SqlConnection(Session("ConnectString"))
        dbCon.Open()

        Try
            Dim robjSQLCommand As New SqlCommand

            With robjSQLCommand
                .Connection = dbCon
                .CommandText = "ps_sm_EnterOrder"
                .CommandType = CommandType.StoredProcedure

                .Parameters.Add("@VendorOrderID", SqlDbType.Int)
                .Parameters.Item("@VendorOrderID").Direction = ParameterDirection.InputOutput
                .Parameters.Add("@ReportingPeriodID", SqlDbType.Int)
                .Parameters.Add("@VendorID", SqlDbType.Int)
                .Parameters.Add("@StoreID", SqlDbType.Int)
                .Parameters.Add("@PurchaseOrderNumber", SqlDbType.Int)
                .Parameters.Add("@BusinessDate", SqlDbType.DateTime)
                .Parameters.Add("@AccountID", SqlDbType.VarChar, 50)
                .Parameters.Add("@ShowSupplies", SqlDbType.Bit)
                .Parameters.Add("@IncludeItems", SqlDbType.Int)
                .Parameters.Add("@SortOrder", SqlDbType.Int)

                .Parameters.Item("@VendorOrderID").Value = CInt(rnVendorOrderID)
                .Parameters.Item("@ReportingPeriodID").Value = -1
                .Parameters.Item("@VendorID").Value = vnVendorID
                .Parameters.Item("@StoreID").Value = vnStoreID
                .Parameters.Item("@PurchaseOrderNumber").Value = vnPurchaseOrderNumber
                .Parameters.Item("@BusinessDate").Value = vtBusinessDate
                .Parameters.Item("@AccountID").Value = Session("AccountID")
                'LY 07JAN2015 BZ2563 - Added the following session variables to preserve these options for use when the order is saved
                bSupplies = Session("bSupplies")
                iIncludeAllItems = Session("iIncludeAllItems")
                iSort = Session("iSort")
                .Parameters.Item("@ShowSupplies").Value = IIf(bSupplies = True, 1, 0)
                .Parameters.Item("@IncludeItems").Value = iIncludeAllItems
                .Parameters.Item("@SortOrder").Value = iSort

                .ExecuteNonQuery()
            End With
            rnVendorOrderID = robjSQLCommand.Parameters.Item("@VendorOrderID").Value
            'The SetDeliveryDateOverride sub is temporary - the above sp will be modified to include DeliveryDateOverride
            SetDeliveryDateOverride(rnVendorOrderID)
            robjSQLCommand.Dispose()
            flag = True
        Catch exception1 As Exception
            InvWSS.Platform.LogError(exception1, "SBOnet - NewOrderLTO.aspx", "PostOrderHeader")
            'Response.Write(exception1.ToString)
            flag = False
        End Try
        dbCon.Close()
        Return flag
    End Function
    Sub SetDeliveryDateOverride(ByVal iVendorOrderID As Integer)
        Dim sSQL As String = "UPDATE tmVendorOrder SET [DeliveryDate] = '" & txtDeliveryDate.Text & "' WHERE VendorOrderID = " & iVendorOrderID

        InvWSS.ExecuteCommand(sSQL)

    End Sub
    Function PostOrderDetail(ByVal vnVendorOrderID As Integer, ByRef robjEntries As DataTable) As Boolean
        Dim flag As Boolean
        Dim dbCon As New SqlConnection(Session("ConnectString"))
        dbCon.Open()

        Try
            Dim robjSQLCommand As New SqlCommand
            With robjSQLCommand
                .Connection = dbCon
                .CommandText = "ps_sm_EnterOrderDetail"
                .CommandType = CommandType.StoredProcedure

                .Parameters.Add("@VendorOrderID", SqlDbType.Int)
                .Parameters.Add("@LineItemID", SqlDbType.Int)
                .Parameters.Item("@LineItemID").Direction = ParameterDirection.InputOutput
                .Parameters.Add("@VendorItemID", SqlDbType.Int)
                .Parameters.Add("@IngredientID", SqlDbType.Int)
                .Parameters.Add("@SOGQty", SqlDbType.Float)
                .Parameters.Add("@OrderQty", SqlDbType.Float)
                .Parameters.Add("@UnitPrice", SqlDbType.Decimal)
                .Parameters.Add("@InventoryUnitPrice", SqlDbType.Decimal)
                .Parameters.Add("@InventoryOrderQty", SqlDbType.Float)
                .Parameters.Add("@OnHand", SqlDbType.Float)
                .Parameters.Add("@PreviousOrders", SqlDbType.Float)
                .Parameters.Add("@ProjectedUsage", SqlDbType.Float)
                .Parameters.Add("@Calc", SqlDbType.NVarChar, 200)
                .Parameters.Add("@LTOQty", SqlDbType.Float)
                .Parameters.Add("@PriorityLessThanSuggested", SqlDbType.Bit)
                .Parameters.Add("@ManuallyChangedZero", SqlDbType.Bit)
                .Parameters.Add("@GreaterThanSuggested", SqlDbType.Bit)
            End With

            Dim num2 As Integer = robjEntries.Rows.Count - 1
            Dim i As Integer = 0
            Do While (i <= num2)
                Dim row As DataRow = robjEntries.Rows.Item(i)
                If (row.RowState <> DataRowState.Deleted) Then
                    With robjSQLCommand
                        .Parameters.Item("@VendorOrderID").Value = vnVendorOrderID
                        .Parameters.Item("@LineItemID").Value = row("LineItemID")
                        .Parameters.Item("@VendorItemID").Value = row("VendorItemID")
                        .Parameters.Item("@IngredientID").Value = row("IngredientID")
                        .Parameters.Item("@SOGQty").Value = row("SOGQty")
                        .Parameters.Item("@OrderQty").Value = row("OrderQty")
                        .Parameters.Item("@UnitPrice").Value = row("UnitPrice")
                        .Parameters.Item("@InventoryUnitPrice").Value = row("InventoryUnitPrice")
                        .Parameters.Item("@InventoryOrderQty").Value = row("InventoryOrderQty")
                        .Parameters.Item("@OnHand").Value = row("OnHand")
                        .Parameters.Item("@PreviousOrders").Value = row("PreviousOrders")
                        .Parameters.Item("@ProjectedUsage").Value = row("ProjectedUsage")
                        .Parameters.Item("@Calc").Value = row("Calc")
                        .Parameters.Item("@LTOQty").Value = row("SuggestedLTO_OrderQty")
                        .Parameters.Item("@PriorityLessThanSuggested").Value = If(row("PriorityLessThanSuggested") = 1, True, False)
                        .Parameters.Item("@ManuallyChangedZero").Value = If(row("ManuallyChangedZero") = 1, True, False)
                        .Parameters.Item("@GreaterThanSuggested").Value = If(row("GreaterThanSuggested") = 1, True, False)
                        .ExecuteNonQuery()
                    End With
                End If
                i += 1
            Loop
            robjSQLCommand.Dispose()
            flag = True
        Catch exception1 As Exception
            InvWSS.Platform.LogError(exception1, "SBOnet - NewOrderLTO.aspx", "PostOrderDetail")
            'Response.Write(exception1.ToString)
            flag = False
        End Try
        dbCon.Close()
        Return flag
    End Function

    Function PostOrderApproved(ByVal vnVendorOrderID As Integer) As Boolean

        Dim dDateNow As Date = Now
        Dim flag As Boolean

        Try
            'Mark Williams Bug 1340 Modified the following SQL to updated the new DateApproved Column


            Dim vsSQL As String = "UPDATE dbo.tmVendorOrder SET ApprovedBy = '" & Session("AccountID") & "', DateApproved = '" & dDateNow & "' WHERE VendorOrderID = " & vnVendorOrderID.ToString
            InvWSS.Platform.SBODBExecNonQuerySQL("SBOCore", vsSQL)

            vsSQL = "DELETE FROM dbo.tdVendorOrder WHERE VendorOrderID = " & vnVendorOrderID.ToString & " AND (OrderQty IS NULL OR OrderQty = 0)"
            InvWSS.Platform.SBODBExecNonQuerySQL("SBOCore", vsSQL)

            flag = True

            InvWSS.ProcessOrdersAllVendors
        Catch exception1 As Exception
            InvWSS.Platform.LogError(exception1, "SBOnet - NewOrderLTO.aspx", "PostOrderApproved")
            flag = False
        End Try

        Return flag
    End Function

    Function ProcessApprovedOrder() As Boolean
        Dim sOrgname As String
        Dim nVendorOrderID As Integer
        Dim sbSQL As New StringBuilder
        Dim tTable As New DataTable
        Dim Platform As ISBOPlatformV100 = Nothing
        Try
            sOrgname = Request.QueryString("Org")
            nVendorOrderID = CInt(Request.QueryString("vonum"))

            Dim matchpattern As String = "<(?:[^>=]|='[^']*'|=""[^""]*""|=[^'""][^\s>]*)*>"

            sOrgname = System.Text.RegularExpressions.Regex.Replace(sOrgname, matchpattern, String.Empty, RegexOptions.IgnoreCase Or RegexOptions.IgnorePatternWhitespace Or RegexOptions.Multiline Or RegexOptions.Singleline)


            InvWSS = New InventoryWebSiteServices.InventoryServices

            Platform = InvWSS.Login(sOrgname)

            Platform.ApplicationLogger.LogLocation = LogLocationEnum.LogDB
            Platform.ApplicationLogger.EnableLogger = True
            Platform.ApplicationLogger.Verbosity = VerbosityLevelEnum.VerbosityLogBlabby

            sbSQL.AppendLine("SELECT [VendorOrderID], [ApprovedBy], [DateApproved]")
            sbSQL.AppendLine("FROM [dbo].[tmVendorOrder]")
            sbSQL.AppendLine("WHERE [VendorOrderID] = @VendorOrderID")
            sbSQL.AppendLine("	AND [ApprovedBy] IS NOT NULL")
            sbSQL.AppendLine("	AND [DateApproved] IS NOT NULL")
            'sbSQL.AppendLine("	AND ISNULL([ExportFileName], '') = ''") 'jjc There's already protections for resends in ProcessOrdersAllVendors.  This might be overkill.
            sbSQL.AppendLine("")


            Dim dbCon As SqlConnection
            dbCon = Platform.SBODBConnection("sbocore")
            Dim robjSQLCommand As New SqlCommand

            With robjSQLCommand

                .Connection = dbCon
                .CommandText = sbSQL.ToString
                .CommandType = CommandType.Text

                .Parameters.Add("@VendorOrderID", SqlDbType.Int)
                .Parameters.Item("@VendorOrderID").Value = nVendorOrderID
            End With

            tTable = InvWSS.Platform.SBODBExecQuerySQLCommand("SBOCore", robjSQLCommand)

            If tTable.Rows.Count > 0 Then
                InvWSS.ProcessOrdersAllVendors()
            End If
        Catch ex As Exception
            If Platform IsNot Nothing Then
                Platform.LogError(ex, False)
            End If
        End Try

        Response.Redirect("login.aspx?timeout=1")

    End Function

    Function GetVendorName(ByVal VendorID As Integer) As String
        Dim tbVendors As New DataTable
        Dim sResponse As String = ""
        InvWSS.GetVendorEntries(True, tbVendors)
        Dim row As DataRow
        For Each row In tbVendors.Rows
            If row("VendorID") = VendorID Then
                sResponse = row("VendorName")
                Exit For
            End If
        Next row
        row = Nothing
        tbVendors.Dispose()
        Return sResponse
    End Function

    Function GetStoreName(ByVal StoreID As Integer) As String
        Dim tbStores As New DataTable
        Dim sResponse As String = ""
        tbStores = InvWSS.GetPermittedStores()
        Dim row As DataRow
        For Each row In tbStores.Rows
            If row("StoreID") = StoreID Then
                sResponse = row("StoreDescription")
                Exit For
            End If
        Next row
        row = Nothing
        tbStores.Dispose()
        Return sResponse
    End Function

    Sub DebugTable(ByVal OrderDetail As DataTable)
        Dim col As DataColumn
        Dim row As DataRow
        Dim sOut As String = "<table>"
        row = OrderDetail.Rows(0)

        For Each col In OrderDetail.Columns
            sOut &= "<tr><td>" & col.ColumnName & "</td><td>" & row(col.Ordinal) & "</td></tr>"
        Next
        sOut &= "</table>"
        Response.Write(sOut)
    End Sub

    Sub UserRank(ByVal UAI As String, ByRef iUserRank As Integer, ByRef sRoleID As String)
        Dim tbRoles As New DataTable
        Dim row As DataRow
        Dim iRoleRank As Integer
        InvWSS.GetListUserRoles(UAI, tbRoles)
        For Each row In tbRoles.Rows
            iRoleRank = GetRoleRank(row("ItemID"))
            If row("IsMember") And iRoleRank > iUserRank Then
                iUserRank = iRoleRank
                sRoleID = row("itemid")
            End If
        Next
        tbRoles.Dispose()
    End Sub

    Function GetRoleRank(ByVal URI As String) As Integer
        Dim sSQL As String = "SELECT Rank FROM tmRole WITH(NOLOCK) WHERE UniversalRoleIdentifier = '" & URI & "'"
        Dim tTable As New DataTable
        Dim row As DataRow
        Dim iRank As Integer
        InvWSS.GetData(sSQL, tTable)
        For Each row In tTable.Rows
            iRank = row("Rank")
        Next
        tTable.Dispose()
        Return iRank
    End Function

    Sub btnRequest_click(ByVal sender As Object, ByVal e As System.EventArgs)

        Dim sEmailBody As String = ""
        Dim sSubject As String = "Order Resend Request"
        Dim sEmailAddress As String = ConfigurationManager.AppSettings("EmailAddress.HelpDesk") & ";" & ConfigurationManager.AppSettings("EmailAddress.SBOWebWork") & ";"

        Dim sOnCallNumber As String = InvWSS.Platform.SBODBScalarGet("SBORoot", "vjCSROnCallSchedule", "CellPhoneEmail", "GetDate() BETWEEN StartDate AND EndDate")

        If Len(sOnCallNumber) Then
            sEmailAddress &= sOnCallNumber & ";"
        End If

        sEmailBody &= sSubject & vbCrLf & vbCrLf
        sEmailBody &= "Owner: " & InvWSS.Platform.Application.Organization & vbCrLf
        sEmailBody &= "StoreID: " & txtstoreid.Text & vbCrLf
        sEmailBody &= "Store Name: " & GetStoreName(Val(txtstoreid.Text)) & vbCrLf
        sEmailBody &= "Requestor: " & GetUser() & vbCrLf
        sEmailBody &= "Vendor Name: " & GetVendorName(Val(txtvendorid.Text)) & vbCrLf
        sEmailBody &= "Business Date: " & txtbusdate.Text & vbCrLf
        sEmailBody &= "Vendor Order Num: " & vonum.Text & vbCrLf

        SendEmailAndText(sEmailBody, sSubject, sEmailAddress)
        Response.Redirect("VendorOrders.aspx")

    End Sub

    Sub SendEmailAndText(ByVal sEmailBody As String, ByVal sSubject As String, ByVal sEmailAddress As String)

        'sSQL = "INSERT INTO Message "
        'sSQL += "("
        'sSQL += "[AllRecipients], "
        'sSQL += "[Author], "
        'sSQL += "[DTRcvd], "
        'sSQL += "[DTSent], "
        'sSQL += "[RecordDate], "
        'sSQL += "[HasAttachments], "
        'sSQL += "[MsgHeader], "
        'sSQL += "[Note], "
        'sSQL += "[ParentFolder], "
        'sSQL += "[Subject], "
        'sSQL += "[Viewed], "
        'sSQL += "[ReplyTo], "
        'sSQL += "[IsPackage], "
        'sSQL += "[PackageStatus], "
        'sSQL += "[POP3Account], "
        'sSQL += "[POPMsgID], "
        'sSQL += "[dekaolc], "
        'sSQL += "[GUID], "
        'sSQL += "[FromAlias], "
        'sSQL += "[HTML] "
        'sSQL += ")"
        'sSQL += "VALUES "
        'sSQL += "("
        'sSQL += "'" & sEmailAddress & "', "
        'sSQL += "'" & ConfigurationManager.AppSettings("EmailAddress.Webservices") & "', "
        'sSQL += "'" & Now() & "', "
        'sSQL += "'" & Now() & "', "
        'sSQL += "'" & Now() & "', "
        'sSQL += "0, "
        'sSQL += "'', "
        'sSQL += "'" & sEmailBody & "', "
        'sSQL += "1, "
        'sSQL += "'" & sSubject & "', "
        'sSQL += "0, "
        'sSQL += "'" & ConfigurationManager.AppSettings("EmailAddress.Webservices") & "', "
        'sSQL += "0, "
        'sSQL += "0, "
        'sSQL += "'<EMAIL>', "
        'sSQL += "'" & InvWSS.Platform.GetGUID() & "<EMAIL>', "
        'sSQL += "0, "
        'sSQL += "NewID(), "
        'sSQL += "'', "
        'sSQL += "''"
        'sSQL += ")"

        ''RunCommand(sSQL)
        'RunCommand(sSQL, "data source=SQL-CLU01;initial catalog=eManager_App4;Password=Reporter001;persist security info=False;user id=sa;workstation id=ASP-WSS-1;packet size=4096")

        SendMail(sEmailAddress, sSubject, sEmailBody, False)
    End Sub

    Function GetUser() As String
        Dim tUserTable As New DataTable
        Dim sUserName As String = ""
        sSQL = "SELECT ACCT.LastName + ', ' + ACCT.FirstName + ' - ' + ROL.Description AS Requestor FROM tmAccount ACCT WITH(NOLOCK) "
        sSQL &= "LEFT OUTER JOIN tsUserRole UROL WITH(NOLOCK) ON  ACCT.UniversalAccountIdentifier = UROL.UniversalAccountIdentifier "
        sSQL &= "LEFT OUTER JOIN tmRole ROL WITH(NOLOCK) ON  ROL.UniversalRoleIdentifier = UROL.UniversalRoleIdentifier "
        sSQL &= "WHERE ActiveAccount = 1 "
        sSQL &= "AND ACCT.UniversalAccountIdentifier = '" & InvWSS.Platform.Application.AccountIdentifier & "' "

        sSQL &= "ORDER BY UserID"
        If InvWSS.GetData(sSQL, tUserTable) Then
            sUserName = tUserTable.Rows(0).Item("Requestor")
        End If
        Return sUserName
    End Function

    Sub RunCommand(ByVal SQL As String, ByVal sConnection As String)
        Dim dbConnect As New SqlConnection(sConnection)
        Dim cmdSQL As SqlCommand
        dbConnect.Open()
        cmdSQL = New SqlCommand(SQL, dbConnect)
        With cmdSQL
            .CommandTimeout = 0
            .ExecuteNonQuery()
            .Dispose()
        End With
        dbConnect.Close()
        dbConnect.Dispose()

        'InvWSS.Platform.SBODBExecNonQuerySQL("SBOCore", vsSQL)
    End Sub

    Sub CheckMaxOrderQty(ByVal VendorID As Integer)
        'Mark Williams Bug 348
        Dim tabMax As New DataTable
        Dim sMaxSql As String
        Dim row As DataRow
        'Dim drMax As New DataRow

        sMaxSql = "SELECT vendorID, VendorName, MaxOrderQty FROM tmvendor WITH(NOLOCK) WHERE VendorID in (" & VendorID & ")"
        'DAF - 20150603 - This line serves no purpose, nor does it work properly. Line of Nothingness, I damn thee to death.
        'tabMax = InvWSS.GetInformation("tmVendor", "MaxOrderQty")
        InvWSS.GetData(sMaxSql, tabMax)
        For Each row In tabMax.Rows
            MaxVendorOrderQty = row("MaxOrderQty")
        Next
    End Sub
End Class
