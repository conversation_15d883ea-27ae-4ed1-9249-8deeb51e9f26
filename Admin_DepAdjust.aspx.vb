Option Explicit On

Imports System.Data
Imports System.Data.SqlClient
Imports SBOTypeLibrary
Imports SBOWebSiteServices
Imports InventoryWebSiteServices
Imports AmazonUtilities.Mail.SimpleEmailService
Imports System.Text.RegularExpressions

Partial Class Admin_DepAdjust
    Inherits System.Web.UI.Page

    Dim InvWSS As SBONetTypeLibrary.ISBONetInventoryServices204
    Dim sPackageCode As String = "AdminDepositAdjustment"
    Dim TransID As Integer

    Sub Page_Load(ByVal sender As Object, ByVal e As EventArgs) Handles Me.Load
        If Len(Session("UserID")) = 0 Then
            Response.Redirect("login.aspx?timeout=1")
        End If

        InvWSS = New InventoryServices204(CType(Session("IWSS"), SBONetTypeLibrary.ISBONetInventoryServices))

        If InvWSS.GetUserViewPermission(sPackageCode, Session("AccountID")) = False Then
            Response.Redirect("permissionmsgs.aspx?msg=noviewperm")
        End If

        If InvWSS.GetUserExecutePermission(sPackageCode, Session("AccountID")) = False Then
            btnSave.Visible = False
        End If

        If Not IsPostBack Then
            Try
                With Store
                    .DataSource = InvWSS.GetPermittedStores()
                    .DataTextField = "StoreDescription"
                    .DataValueField = "StoreID"
                    .DataBind()
                End With
            Catch ex As Exception
                trMessage.Visible = True
                lblMessageLabel.Text &= "<br />" & ex.Message & "<br />"
            End Try

            Try
                If CDate(Request.QueryString("BDate")) > #1/1/1972# Then
                    BusinessDate.Text = CDate(Request.QueryString("BDate"))
                Else
                    BusinessDate.Text = Now.ToShortDateString
                End If
            Catch ex As Exception
                BusinessDate.Text = Now.ToShortDateString
            End Try

            Dim nStoreID As Integer
            Try
                nStoreID = Request.QueryString("storeid")
            Catch ex As Exception
                nStoreID = -1
            End Try

            Try
                Store.SelectedValue = nStoreID
            Catch ex As Exception
                'DoNothing
            End Try

            Try
                'check for next deposit number as default
                Dim tabDeposits As New DataTable
                Dim sSQL As String
                sSQL = "SELECT "
                sSQL &= "   ISNULL(MAX(DepositNumber) + 1, 1) AS DepositNumber "
                sSQL &= "FROM "
                sSQL &= "   [vReport_Deposit] "
                sSQL &= "WHERE"
                sSQL &= "   StoreID = " & nStoreID
                sSQL &= "   AND BusinessDate = '" & BusinessDate.Text & "' "
                sSQL &= "   AND DepositNumber < 255 "
                InvWSS.GetData(sSQL, tabDeposits)
                If Not IsNothing(tabDeposits) AndAlso tabDeposits.Rows.Count > 0 Then
                    DepositNumber.Text = tabDeposits.Rows(0).Item(0)
                    tabDeposits.Dispose()
                End If
            Catch ex As Exception
                'DoNothing
            End Try

        End If
    End Sub

    Sub chkPaidOut_Click(ByVal sender As Object, ByVal e As EventArgs)
        DepositNumber.Enabled = Not chkPaidOut.Checked
        BagNumber.Enabled = Not chkPaidOut.Checked
        'AdjustmentReason.Enabled = Not chkPaidOut.Checked

        If chkPaidOut.Checked Then
            BagNumber.Text = "N/A"
            DepositNumber.Text = "255"
            'AdjustmentReason.Text = InvWSS.Platform.Application.UserID
        Else
            BagNumber.Text = ""
            DepositNumber.Text = ""
            'AdjustmentReason.Text = ""
        End If
    End Sub


    Sub btnSave_click(ByVal sender As Object, ByVal e As EventArgs)
        Dim iTransactionID As Integer
        Dim matchpattern As String = "<(?:[^>=]|='[^']*'|=""[^""]*""|=[^'""][^\s>]*)*>"

        If Len(DepositNumber.Text) = 0 Or Val(DepositNumber.Text) = 0 Then
            trMessage.Visible = True
            lblMessageLabel.Text &= "<br />A Deposit Number is required.<br />"
            Exit Sub
        ElseIf Val(DepositNumber.Text) > 255 Then
            trMessage.Visible = True
            lblMessageLabel.Text &= "<br />Deposit Number is too large.<br />"
            Exit Sub
        End If

        AdjustmentReason.Text = Regex.Replace(AdjustmentReason.Text, matchpattern, String.Empty, RegexOptions.IgnoreCase Or RegexOptions.IgnorePatternWhitespace Or RegexOptions.Multiline Or RegexOptions.Singleline)
        BagNumber.Text = Regex.Replace(BagNumber.Text, matchpattern, String.Empty, RegexOptions.IgnoreCase Or RegexOptions.IgnorePatternWhitespace Or RegexOptions.Multiline Or RegexOptions.Singleline)

        If InvWSS.PostDepositAdjustment(BusinessDate.Text, Store.SelectedValue, Val(DepositNumber.Text), Val(DepositAmount.Text), BagNumber.Text, AdjustmentReason.Text, iTransactionID) Then

            If chkPaidOut.Checked Then
                Dim RollSQL As String = "UPDATE BI_tmDeposit SET IsPaidOut = 1 WHERE EntryID = " & iTransactionID
                RunCommand(RollSQL, Session("ConnectString"))
            End If

            Session("rptopday") = BusinessDate.Text
            Session("rptoprange") = "daily"


            If InvWSS.PostDepositAdjustmentApproval(iTransactionID, AdjustmentReason.Text) Then
                RollupAStore(Store.SelectedValue, BusinessDate.Text)

            Else
                'Response.Write(InvWSS.ErrorMessage)
            End If

            SendEmailNotice()

            Try
                If InvWSS.Platform.PersistentValue("InventoryConfig", "AutoSubmitAdjustments") Then
                    If InvWSS.PostDepositAdjustmentApproval(iTransactionID, AdjustmentReason.Text) Then
                        RollupAStore(Store.SelectedValue, BusinessDate.Text)
                    Else
                        trMessage.Visible = True
                        lblMessageLabel.Text &= "<br />" & InvWSS.ErrorMessage & "<br />"
                    End If
                End If
            Catch exNoPV As Exception
                trMessage.Visible = True
                lblMessageLabel.Text &= "<br />" & exNoPV.Message & "<br />"
            End Try

            Response.Redirect("Admin_DepAdjList.aspx?bdate=" & BusinessDate.Text & "&storeid=" & Store.SelectedValue)
        Else
            trMessage.Visible = True
            lblMessageLabel.Text &= "<br />" & InvWSS.ErrorMessage & "<br />"
        End If

    End Sub

    Sub btnCancel_click(ByVal sender As Object, ByVal e As EventArgs)
        Response.Redirect("Admin_DepAdjList.aspx")
    End Sub

    Sub RollupAStore(ByVal StoreID As Integer, ByVal RollDate As Date)
        Dim RollSQL As String = "EXECUTE SBONet_ps_sa_Reset " & StoreID & ", 'Admin_DepAdjust.aspx.vb'"
        RunCommand(RollSQL, Session("ConnectString"))
    End Sub

    Sub SendEmailNotice()
        Dim tTable As New DataTable
        Dim sSQL As String
        Dim rRow As DataRow

        sSQL = "SELECT T1.EmailAddress, T3.Subject FROM tmAccount T1, tdEmailMessages T2, tmEmailMessages T3  "
        sSQL += "LEFT OUTER JOIN WEBRPT_vjBinPermissions bp ON T1.UniversalAccountIdentifier = bp.UniversalAccountIdentifier "
        sSQL += "WHERE "
        sSQL += "T1.UniversalAccountIdentifier = T2.UniversalAccountIdentifier AND "
        sSQL += "T2.EmailMessageID = T3.EmailMessageID AND "
        sSQL += "T3.Title = 'Deposit Adjustment' AND "
        sSQL += "T1.EmailAddress IS NOT NULL AND "
        sSQL += "T1.EmailAddress <> '' AND "
        sSQL += "T1.ActiveAccount = 1"

        sSQL = "select T3.Subject, acc.EmailAddress from WEBRPT_vjBinPermissions bp inner join tmStore on bp.UniversalStoreIdentifier = tmStore.UniversalStoreIdentifier "
        sSQL &= "left outer join tsUserRole ur on bp.UniversalAccountIdentifier = ur.UniversalAccountIdentifier "
        sSQL &= "left outer join tmRole r on ur.UniversalRoleIdentifier = r.UniversalRoleIdentifier "
        sSQL &= "left outer join tmAccount acc on bp.UniversalAccountIdentifier = acc.UniversalAccountIdentifier "
        sSQL &= "left outer join tdEmailMessages T2 ON acc.UniversalAccountIdentifier = T2.UniversalAccountIdentifier "
        sSQL &= "left outer join tmEmailMessages T3 ON T2.EmailMessageID = T3.EmailMessageID "
        sSQL &= "where tmStore.StoreID = " & Store.SelectedValue & " "
        'sSQL &= "and acc.ActiveAccount <> 0 AND acc.InHouseUser = 0 "
        'Mark Williams Bug 2584 Since the Admin2020 is the same rank as manager this seemed like the best
        'was to get the manager and everone above included
        sSQL &= "and (r.description IN ('Manager', 'Level 200') OR r.rank > 200) "
        sSQL &= "and T3.Title = 'Deposit Adjustment' "
        sSQL &= "and acc.EmailAddress IS NOT NULL "
        sSQL &= "and acc.EmailAddress <> '' "
        sSQL &= "and acc.ActiveAccount <> 0 "

        Try
            InvWSS.GetData(sSQL, tTable)
            If tTable.Rows.Count > 0 Then
                Dim sMessage As String = ""
                Dim sSubject As String = ""
                Dim sEmailAddress As String = ""
                Dim bFirstPass As Boolean = True
                Dim sUserName As String
                For Each rRow In tTable.Rows
                    If Not bFirstPass Then
                        sEmailAddress += ","
                    End If
                    If Not IsDBNull(rRow("EmailAddress")) Then
                        sEmailAddress += rRow("EmailAddress")
                    End If
                    If Not IsDBNull(rRow("Subject")) Then
                        sSubject = rRow("Subject")
                    End If
                    bFirstPass = False
                Next

                Dim tStoreTable As New DataTable
                Dim nStoreNum As Integer = Store.SelectedValue
                InvWSS.GetData("SELECT StoreNum FROM tmStore WHERE StoreID = " & nStoreNum, tStoreTable)
                If tStoreTable.Rows.Count > 0 Then
                    nStoreNum = tStoreTable.Rows(0).Item(0)
                End If
                sUserName = GetUserName()

                sSubject = nStoreNum & " - " & Store.SelectedItem.Text & " - " & sUserName & " - Dep Adjustment Made"
                sMessage = "This is an automated message from SBOnet.</br>"
                sMessage += "A deposit adjustment for Store " & nStoreNum & " - " & Store.SelectedItem.Text & " was added.</br>"
                sMessage += "Adjustment Added By: " & sUserName & "</br>"
                sMessage += "Date/Time Adjustment Added: " & Now() & "</br>"
                sMessage += "Adjusted Date: " & BusinessDate.Text & "</br>"
                sMessage += "Deposit Number: " & Val(DepositNumber.Text) & "</br>"
                sMessage += "Deposit Amount: " & DepositAmount.Text & "</br>"
                sMessage += "Bag Number: " & Val(BagNumber.Text) & "</br>"
                sMessage += "Adjustment Reason: " & AdjustmentReason.Text & "</br>"
                If chkPaidOut.Checked Then
                    sMessage += "Is Paid-Out: True" & "</br>"
                End If

                SendMail(sEmailAddress, sSubject, sMessage)
                'sSQL = "INSERT INTO Message "
                'sSQL += "("
                'sSQL += "[AllRecipients], "
                'sSQL += "[Author], "
                'sSQL += "[DTRcvd], "
                'sSQL += "[DTSent], "
                'sSQL += "[RecordDate], "
                'sSQL += "[HasAttachments], "
                'sSQL += "[MsgHeader], "
                'sSQL += "[Note], "
                'sSQL += "[ParentFolder], "
                'sSQL += "[Subject], "
                'sSQL += "[Viewed], "
                'sSQL += "[ReplyTo], "
                'sSQL += "[IsPackage], "
                'sSQL += "[PackageStatus], "
                'sSQL += "[POP3Account], "
                'sSQL += "[POPMsgID], "
                'sSQL += "[dekaolc], "
                'sSQL += "[GUID], "
                'sSQL += "[FromAlias], "
                'sSQL += "[HTML] "
                'sSQL += ")"
                'sSQL += "VALUES "
                'sSQL += "("
                'sSQL += "'" & sEmailAddress & "', "
                'sSQL += "'<EMAIL>', "
                'sSQL += "'" & Now() & "', "
                'sSQL += "'" & Now() & "', "
                'sSQL += "'" & Now() & "', "
                'sSQL += "0, "
                'sSQL += "'', "
                'sSQL += "'" & sMessage & "', "
                'sSQL += "1, "
                'sSQL += "'" & sSubject & "', "
                'sSQL += "0, "
                'sSQL += "'<EMAIL>', "
                'sSQL += "0, "
                'sSQL += "0, "
                'sSQL += "'<EMAIL>', "
                'sSQL += "'" & InvWSS.Platform.GetGUID() & "<EMAIL>', "
                'sSQL += "0, "
                'sSQL += "NewID(), "
                'sSQL += "'', "
                'sSQL += "''"
                'sSQL += ")"

                ''Response.Write(sSQL)
                'RunCommand(sSQL, "data source=SQL-CLU01;initial catalog=eManager_App4;Password=Reporter001;persist security info=False;user id=sa;workstation id=ASP-WSS-1;packet size=4096")
            End If
        Catch eInvoiceIsSubmitted As Exception
            trMessage.Visible = True
            lblMessageLabel.Text &= "<br />" & eInvoiceIsSubmitted.Message & "<br />"
        End Try
    End Sub
    Function GetUserName() As String
        Dim sSQL As String
        Dim tTable As New DataTable
        Dim row As DataRow
        Dim uname As String = "Unknown"
        Try
            sSQL = "SELECT FirstName, LastName FROM tmAccount WHERE UniversalAccountIdentifier = '" & Session("AccountID") & "'"
            InvWSS.GetData(sSQL, tTable)
            For Each row In tTable.Rows
                If Not IsDBNull(row("FirstName")) Then
                    uname = row("FirstName") & " "
                End If
                If Not IsDBNull(row("LastName")) Then
                    uname &= row("LastName")
                End If
            Next

        Catch ex As Exception

        End Try
        Return uname
    End Function
    Sub RunCommand(ByVal SQL As String, ByVal sConnection As String)
        Dim dbConnect As New SqlConnection(sConnection)
        Dim cmdSQL As SqlCommand
        dbConnect.Open()
        cmdSQL = New SqlCommand(SQL, dbConnect)
        With cmdSQL
            .CommandTimeout = 0
            .ExecuteNonQuery()
            .Dispose()
        End With
        dbConnect.Close()
        dbConnect.Dispose()
    End Sub

End Class
