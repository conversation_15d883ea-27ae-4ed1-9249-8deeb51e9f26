﻿
Option Explicit On

Imports AmazonUtilities.Mail.SimpleEmailService
'2021-05-06 jjc Sprint79w66 SBOD-2223 Changing email from using WebServicesAddons.AmazonSES.SendMail to AmazonUtilities.Mail.SimpleEmailService.SendMail
'2021-07-19 jjc Sprint 79w68 Adding CorpID to request email

Partial Class HelpRequestEdit
    Inherits System.Web.UI.Page
    Dim InvWSS As InventoryWebSiteServices.InventoryServices
    Dim nHelpRequestID As Integer
    Dim tTable As New DataTable
    Dim sPageAddedFrom, sSQL, sUser As String
    Dim tHeaderTable As DataTable
    'Mark Williams 1709
    Dim sFinalize As Boolean = False
    Dim litem As Obout.ListBox.ListBoxItem
    Dim CostThreshold As Integer
    Dim aryStoreDesc() As String
    Dim sOfficeEmail As String
    Dim bIsNewRequest As Boolean

    Friend sOwnerName As String

    Sub Page_Load(ByVal sender As Object, ByVal e As EventArgs) Handles Me.Load
        If Len(Session("UserID")) = 0 Then
            Response.Redirect("login.aspx?timeout=1")
        End If

        InvWSS = Session("IWSS")


        If Request.QueryString("HelpRequestID") = -1 Or Request.QueryString("HelpRequestID") = "" Then
            trBirthday.Visible = False
            Requestor.Enabled = False
            btnDelete.Visible = False
            btnCancel.Visible = True
            'Mark Williams Bug 1709
            bIsNewRequest = True
            trFirstAssign.Visible = True
            lblCostDisclaimer.Visible = False
        ElseIf Request.QueryString("HelpRequestID") > 0 Then
            nHelpRequestID = Request.QueryString("HelpRequestID")
            btnDelete.Visible = False
            'Mark Williams Bug 1709
            Requestor.Enabled = False
            RequestorPhone.ReadOnly = True
            EmailAddress.ReadOnly = True
            AllStores.Visible = False
            AllSendTo.Visible = False
            bIsNewRequest = False
            Subject.ReadOnly = True
            Detail.ReadOnly = True
            btnCancel.Visible = True
            btnFinalize.Visible = True
            ResolutionForm.Visible = True
            CalendarDateResolved.SelectedDate = Today
        End If

        sUser = InvWSS.Platform.Application.AccountIdentifier
        sOwnerName = Session("OrganizationName")
        If Not IsPostBack Then
            PopStores()
            GetUsers(sUser)
            PopSendTo()
            GetHelpInstructions()
            BindGrids()
            If Not bIsNewRequest Then
                DisableCheckBoxList()
                GetCostThreshold()
            End If

        End If
    End Sub

    Sub PopSendTo()
        Dim sRoles As String = ""
        Dim sPVRoles As String
        Dim Roles() As String
        Dim iCount As Integer
        sPVRoles = InvWSS.Platform.PersistentValue("InventoryConfig", "InHouseHelpRequestRoles")
        If sPVRoles <> String.Empty Then
            Roles = sPVRoles.Split(",")
            For iCount = 0 To Roles.GetUpperBound(0)
                sRoles &= IIf(Len(sRoles) > 0, ",'" & Roles(iCount) & "'", "'" & Roles(iCount) & "'")
            Next
        End If

        cblTo.Items.Clear()
        If Len(sRoles) Then
            Dim sbSQL As New StringBuilder
            sbSQL.Append("SELECT ")
            sbSQL.AppendLine("    ACCT.UniversalAccountIdentifier")
            sbSQL.AppendLine("    , ACCT.LastName + ', ' + ACCT.FirstName AS Addressee")
            sbSQL.AppendLine("    , ACCT.EmailAddress")
            sbSQL.AppendLine("FROM ")
            sbSQL.AppendLine("    tmAccount ACCT ")
            sbSQL.AppendLine("    LEFT OUTER JOIN tsUserRole ur ON ACCT.UniversalAccountIdentifier = ur.UniversalAccountIdentifier ")
            sbSQL.AppendLine("    LEFT OUTER JOIN tmRole role ON ur.UniversalRoleIdentifier = role.UniversalRoleIdentifier ")
            sbSQL.AppendLine("    LEFT OUTER JOIN tmAccount SBO ON ACCT.SBOnetTrainer = SBO.UniversalAccountIdentifier ")
            sbSQL.AppendLine("    LEFT OUTER JOIN tmAccount SCH ON ACCT.SchedulerTrainer = SCH.UniversalAccountIdentifier ")
            sbSQL.AppendLine("WHERE ")
            sbSQL.AppendLine("    ACCT.ActiveAccount <> 0 ")
            sbSQL.AppendLine("    AND ACCT.EmailAddress IS NOT NULL ")
            sbSQL.AppendLine("    AND role.UniversalRoleIdentifier IN (" & sRoles & ")")
            sbSQL.AppendLine("    AND ACCT.InHouseUser = 0")
            sbSQL.AppendLine("ORDER BY ")
            sbSQL.AppendLine("    ACCT.LastName, ACCT.Firstname")

            Dim tTable As New DataTable
            If InvWSS.GetData(sbSQL.ToString, tTable) AndAlso tTable.Rows.Count > 0 Then
                cblTo.DataSource = tTable.DefaultView
                cblTo.DataTextField = "Addressee"
                cblTo.DataValueField = "EmailAddress"
                cblTo.DataBind()

                'Mark Williams 1709
                ddlAssignTo.DataSource = tTable.DefaultView
                ddlAssignTo.DataTextField = "Addressee"
                ddlAssignTo.DataValueField = "EmailAddress"
                ddlAssignTo.DataBind()
                tTable.Dispose()

                ddlFirstAssign.DataSource = tTable.DefaultView
                ddlFirstAssign.DataTextField = "Addressee"
                ddlFirstAssign.DataValueField = "EmailAddress"
                ddlFirstAssign.DataBind()
                tTable.Dispose()
            End If
        End If
    End Sub

    Sub GetHelpInstructions()
        Try
            Dim sOwner As String = Session("OrganizationName")
            Dim sServerPath As String = Me.Server.MapPath("ChartImages")
            Dim sFilePath As String = sServerPath & "\" & sOwner & "_HelpInstructions.txt"
            Dim sr As System.IO.StreamReader
            sr = System.IO.File.OpenText(sFilePath)
            Dim sInstructions As String = sr.ReadToEnd()
            divInstructions.InnerHtml = sInstructions
            sr.Close()
        Catch eFileRead As Exception
            divInstructions.InnerHtml = "<p>Please select the office personnel you would like to communicate to. If you need immediate assistance, please call the office directly.</p><p>If you leave a message after 5 on Friday and/or before 8 AM on Monday, your request will be responded to on Monday (unless Monday is a Holiday)</p>"
        End Try
    End Sub

    Sub BindGrids()
        'Mark Williams Bug 1709 Added the ability to detect if it is a new or existing request
        If nHelpRequestID > 0 Then
            sSQL = "SELECT * FROM tmHelpRequest WHERE HelpRequestID = " & nHelpRequestID
            If InvWSS.GetData(sSQL, tTable) Then
                If tTable.Rows.Count > 0 Then
                    HelpRequestID.Text = nHelpRequestID
                    Requestor.SelectedValue = CheckForNull(tTable.Rows(0).Item("Requestor"))
                    RequestorPhone.Text = CheckForNull(tTable.Rows(0).Item("RequestorPhone"))
                    EmailAddress.Text = CheckForNull(tTable.Rows(0).Item("EmailAddress"))
                    'The following checks the selected Stores and "Recipients" in the CheckBoxLists
                    Dim aryTo() As String
                    Dim cbTarget As Obout.Interface.OboutCheckBox
                    aryStoreDesc = Split(tTable.Rows(0).Item("InHouseStoreDesc"), ",")
                    aryTo = Split(tTable.Rows(0).Item("InHouseRecipient"), ";")
                    For i As Integer = 0 To (aryStoreDesc.Length - 1)
                        For Each litem In cblStores.Items
                            cbTarget = CType(litem.FindControl("cbTargetStores"), Obout.Interface.OboutCheckBox)
                            If litem.Text = aryStoreDesc(i).ToString Then
                                cbTarget.Checked = True
                                Exit For
                            End If
                        Next
                    Next
                    For i As Integer = 0 To (aryTo.Length - 1)
                        For Each litem In cblTo.Items
                            cbTarget = CType(litem.FindControl("cbSendTo"), Obout.Interface.OboutCheckBox)
                            If litem.Text = aryTo(i).ToString Then
                                cbTarget.Checked = True
                                Exit For
                            End If
                        Next
                    Next

                    Subject.Text = tTable.Rows(0).Item("Subject")
                    Detail.Text = tTable.Rows(0).Item("Detail")
                    Thumbprint.Text = Session("UserName")
                    Birthday.Text = tTable.Rows(0).Item("Birthday")
                    LastModified.Text = tTable.Rows(0).Item("LastModified")
                    txtAssignedDate.Text = CheckForNull(tTable.Rows(0).Item("InHouseAssignedDate"))
                    txtAssignedTo.Text = CheckForNull(tTable.Rows(0).Item("InHouseAssignee"))
                    txtCost.Text = CheckForNull(tTable.Rows(0).Item("InHouseCostEstimate"))
                    For Each lstitem As ListItem In ddlAssignTo.Items
                        If lstitem.Text = txtAssignedTo.Text Then
                            lstitem.Selected = True
                            Exit For
                        End If
                    Next
                    txtResolutionDetail.Text = CheckForNull(tTable.Rows(0).Item("InHouseResolutionDetail"))
                    DateResolved.Text = CheckForNull(tTable.Rows(0).Item("DateResolved"))
                    If Not DateResolved.Text = "" Then
                        'Determines if the request has been resolved or not
                        txtResolutionDetail.ReadOnly = True
                        DateResolved.ReadOnly = True
                        DateResolved.Visible = True
                        CalendarDateResolved.Visible = False
                        txtResolutionDetail.Rows = 10
                        txtCost.ReadOnly = True
                        trAssignedTo.Visible = False
                        trAssignedDate.Visible = False
                        trAssignDDL.Visible = False
                        btnSave.Visible = False
                        btnFinalize.Visible = False
                        lblCostDisclaimer.Visible = False
                    End If
                End If
                tTable.Dispose()
            End If
        Else
            sSQL = "SELECT * FROM tmAccount WHERE UniversalAccountIdentifier = '" & InvWSS.Platform.Application.AccountIdentifier & "'"
            If InvWSS.GetData(sSQL, tTable) AndAlso tTable.Rows.Count > 0 Then
                RequestorPhone.Text = CheckForNull(tTable.Rows(0).Item("PhoneNumber"))
                EmailAddress.Text = CheckForNull(tTable.Rows(0).Item("EmailAddress"))
                tTable.Dispose()
            End If
        End If
    End Sub

    Function GetHeader() As String
        Dim sStores As String = ""
        Dim sbSQL As New StringBuilder
        'Mark WIlliams Bug 1709 Modified to work with the Obout CheckBoxList
        Dim cbTarget As Obout.Interface.OboutCheckBox
        For Each litem In cblStores.Items
            cbTarget = CType(litem.FindControl("cbTargetStores"), Obout.Interface.OboutCheckBox)
            If cbTarget.Checked = True Then
                sStores &= IIf(Len(sStores) > 0, "," & litem.Value, litem.Value)
            End If
        Next

        If Len(sStores) > 0 Then
            sbSQL.Append("SELECT ")
            sbSQL.AppendLine("  STO.StoreId ")
            sbSQL.AppendLine("  ,STO.StoreNum ")
            sbSQL.AppendLine("  ,STO.StoreDescription ")
            sbSQL.AppendLine("  ,ISNULL(STO.Phone, '') AS Phone ")
            sbSQL.AppendLine("  ,ISNULL(POS.Description, '') AS POSDescription ")
            sbSQL.AppendLine("  ,ISNULL(ST0INV.PurchaseAccount, '') AS PurchaseAccount ")
            sbSQL.AppendLine("  ,ISNULL(ST0INV.PortalSiteNumber, '') AS CorpID")
            sbSQL.AppendLine("  ,CASE WHEN ST0INV.DoInventory = 1 THEN 'RI' ELSE 'R' END As DoInventory ")
            sbSQL.AppendLine("  ,CASE WHEN ST0INV.DoSASExport = 1 THEN 'True' ELSE '' END As DoSASExport ")
            sbSQL.AppendLine("FROM ")
            sbSQL.AppendLine("  tmStore STO ")
            sbSQL.AppendLine("  LEFT OUTER JOIN tmStoreInvt ST0INV ON  STO.StoreID = ST0INV.StoreID ")
            sbSQL.AppendLine("  LEFT OUTER JOIN tmPOSProgram POS ON  ST0INV.POSProgramID = POS.POSProgramID ")
            sbSQL.AppendLine("WHERE ")
            sbSQL.AppendLine("  STO.StoreID IN (" & sStores & ")")
        Else
            sbSQL.Append("SELECT ")
            sbSQL.AppendLine("  '0' AS StoreId ")
            sbSQL.AppendLine("  ,'0' AS StoreNum ")
            sbSQL.AppendLine("  ,'All Stores' AS StoreDescription ")
            sbSQL.AppendLine("  ,'" & RequestorPhone.Text & "' AS Phone ")
            sbSQL.AppendLine("  ,'' AS POSDescription ")
            sbSQL.AppendLine("  ,'' AS PurchaseAccount ")
            sbSQL.AppendLine("  ,'' AS DoInventory ")
            sbSQL.AppendLine("  ,'' AS DoSASExport ")
        End If
        Return sbSQL.ToString
    End Function

    Function CheckForNull(ByVal oValue As Object) As String
        Dim sReturnString As String = ""
        Try
            sReturnString = CType(oValue, System.String)
            Return sReturnString
        Catch
            Return ""
        End Try
    End Function

    Sub GetUsers(Optional ByVal sUniversalAccountIdentifier As String = "")
        Dim tUserTable As New DataTable
        Dim sbSQL As New StringBuilder

        sbSQL.Append("SELECT ")
        sbSQL.AppendLine("  ACCT.UniversalAccountIdentifier")
        sbSQL.AppendLine("  ,ACCT.LastName + ', ' + ACCT.FirstName + ' - ' + ROL.Description AS Requestor")
        sbSQL.AppendLine("  ,PhoneNumber")
        sbSQL.AppendLine("  ,EmailAddress")
        sbSQL.AppendLine("FROM ")
        sbSQL.AppendLine("  tmAccount ACCT")
        sbSQL.AppendLine("  LEFT OUTER JOIN tsUserRole UROL ON ACCT.UniversalAccountIdentifier = UROL.UniversalAccountIdentifier ")
        sbSQL.AppendLine("  LEFT OUTER JOIN tmRole ROL ON  ROL.UniversalRoleIdentifier = UROL.UniversalRoleIdentifier ")
        sbSQL.AppendLine("WHERE")
        sbSQL.AppendLine("  ActiveAccount = 1 ")
        If sUniversalAccountIdentifier <> "" Then
            sbSQL.AppendLine("  AND ACCT.UniversalAccountIdentifier = '" & sUniversalAccountIdentifier & "' ")
        End If
        sbSQL.AppendLine("ORDER BY")
        sbSQL.AppendLine("  UserID")

        If InvWSS.GetData(sbSQL.ToString, tUserTable) Then
            Requestor.DataSource = tUserTable
            Requestor.DataTextField = "Requestor"
            Requestor.DataValueField = "UniversalAccountIdentifier"
            Requestor.DataBind()
            tUserTable.Dispose()
        End If

        Dim row As DataRow
        If tUserTable.Rows.Count > 0 Then
            row = tUserTable.Rows(0)
            If Not IsDBNull(row("PhoneNumber")) Then
                RequestorPhone.Text = row("PhoneNumber")
            End If
            If Not IsDBNull(row("EmailAddress")) Then
                EmailAddress.Text = row("EmailAddress")
            End If
        End If

    End Sub

    Sub btnSave_click(ByVal sender As Object, ByVal e As EventArgs)
        If Subject.Text = String.Empty Then
            lblValidationText.Text = "A 'SUBJECT' IS REQUIRED"
            lblValidationText.Visible = True
            Exit Sub
        End If
        If RequestorPhone.Text = String.Empty Then
            lblValidationText.Text = "A 'REQUESTOR PHONE NUMBER' IS REQUIRED"
            lblValidationText.Visible = True
            Exit Sub
        End If
        Dim sEmailAddress As String = ""
        'Mark Williams 1699
        Dim sAddresses As String = ""
        Dim cbTarget As Obout.Interface.OboutCheckBox
        For Each litem In cblTo.Items
            cbTarget = CType(litem.FindControl("cbSendTo"), Obout.Interface.OboutCheckBox)
            If cbTarget.Checked Then
                sEmailAddress &= IIf(Len(sEmailAddress) > 0, ";" & litem.Value, litem.Value)
                'Mark Williams 1699
                sAddresses &= IIf(Len(sAddresses) > 0, ";" & litem.Text, litem.Text)
            End If
        Next
        If sEmailAddress = String.Empty Then
            lblValidationText.Text = "A ADDRESSEE (To:) IS REQUIRED"
            lblValidationText.Visible = True
            Exit Sub
        End If
        Dim sStoresEffected As String = ""
        For Each litem In cblStores.Items
            cbTarget = CType(litem.FindControl("cbTargetStores"), Obout.Interface.OboutCheckBox)
            If cbTarget.Checked Then
                sStoresEffected &= IIf(Len(sStoresEffected) > 0, ", " & litem.Text, litem.Text)
            End If
        Next
        If sStoresEffected = String.Empty Then
            lblValidationText.Text = "A 'RESTAURANT EFFECTED' IS REQUIRED"
            lblValidationText.Visible = True
            Exit Sub
        End If
        If EmailAddress.Text <> "" Then
            sSQL = "UPDATE tmAccount SET EmailAddress = '" & EmailAddress.Text & "' WHERE (EmailAddress = '' OR EmailAddress IS NULL) AND UniversalAccountIdentifier = '" & InvWSS.Platform.Application.AccountIdentifier & "'"
            RunCommand(sSQL)
        End If

        If RequestorPhone.Text <> "" Then
            sSQL = "UPDATE tmAccount SET PhoneNumber = '" & RequestorPhone.Text & "' WHERE PhoneNumber <> '" & RequestorPhone.Text & "' AND UniversalAccountIdentifier = '" & InvWSS.Platform.Application.AccountIdentifier & "'"
            RunCommand(sSQL)
        End If

        Dim dDateNow As Date = Now

        'Mark Williams Bug 1699 Enables the page to Save Help Request to tmHelpRequest
        'Currently this page does not update Request as the page is not set up to view old request. The below is set up
        'so the page can easily be set up to update a request.
        If IsNumeric(HelpRequestID.Text) AndAlso CInt(HelpRequestID.Text) > 0 Then
            sSQL = "UPDATE tmHelpRequest SET "

            If Not (txtAssignedTo.Text = ddlAssignTo.SelectedItem.Text) Then
                'Unless the user has changed who the Request is assigned to the assignee should NOT be updated
                sSQL &= "InHouseAssignee = '" & ddlAssignTo.SelectedItem.Text & "', "
                sSQL &= "InHouseAssignedDate = '" & dDateNow & "', "
            End If

            sSQL &= "InHouseResolutionDetail = '" & Replace(txtResolutionDetail.Text, "'", "''") & "', "
            sSQL &= "InHouseCostEstimate = '" & txtCost.Text & "', "
            sSQL &= "Thumbprint = '" & InvWSS.Platform.Application.AccountIdentifier & "', "
            sSQL &= "LastModified = '" & dDateNow & "' "
            sSQL &= "WHERE HelpRequestID = " & nHelpRequestID

            RunCommand(sSQL)
            SendEmailNotice()

            Response.Redirect("HelpRequestReport.aspx")
        Else
            sSQL = "INSERT INTO tmHelpRequest "
            sSQL &= "("
            sSQL &= "Subject, "
            sSQL &= "Detail, "
            sSQL &= "Requestor, "
            sSQL &= "RequestorPhone, "
            sSQL &= "StoreID, "
            sSQL &= "InHouseStoreDesc, "
            sSQL &= "IsInHouse, "
            sSQL &= "InHouseRecipient, "
            sSQL &= "InHouseEmail, "
            sSQL &= "InHouseAssignee, "
            sSQL &= "InHouseAssignedDate, "
            sSQL &= "InHouseCostEstimate, "
            sSQL &= "EmailAddress, "
            sSQL &= "Thumbprint "
            sSQL &= ")"
            sSQL &= "VALUES "
            sSQL &= "("
            sSQL &= "'" & Replace(Subject.Text, "'", "''") & "', "
            sSQL &= "'" & Replace(Detail.Text, "'", "''") & "', "
            sSQL &= "'" & InvWSS.Platform.Application.AccountIdentifier & "', "
            sSQL &= "'" & RequestorPhone.Text & "', "

            If cblStores.SelectedValue <> "" Then
                sSQL &= cblStores.SelectedValue & ", "
            Else
                sSQL &= "-1, "
            End If

            If sStoresEffected <> "" Then
                sSQL &= "'" & Replace(sStoresEffected, " ", "") & "', "
            Else
                sSQL &= "-1, "
            End If

            sSQL &= "1, "
            sSQL &= "'" & sAddresses & "', "
            sSQL &= "'" & sEmailAddress & "', "
            sSQL &= "'" & ddlFirstAssign.SelectedItem.Text & "', "
            sSQL &= "'" & dDateNow & "', "
            sSQL &= "'" & txtCost.Text & "', "
            sSQL &= "'" & EmailAddress.Text & "', "
            sSQL &= "'" & InvWSS.Platform.Application.AccountIdentifier & "'"
            sSQL &= ")"

            RunCommand(sSQL)
            SendEmailNotice()

            lblValidationText.Visible = False
            trContinue.Visible = True
            trDisclaimer1.Visible = False
            trForm.Visible = False
            trButtons.Visible = False
            trDisclaimer2.Visible = False
            trCostDisclaimer.Visible = False
        End If


    End Sub

    Sub PopTo(ByVal sender As Object, ByVal e As EventArgs)
        PopSendTo()
    End Sub

    Sub SendEmailNotice()
        Dim sOwnerName As String = ""
        Dim sSubject As String = ""
        Dim sEmailAddress As String = ""
        Dim row As DataRow

        'Mark Williams Bug 1709 this checks to see if this is the first time this is being saved.  If this is
        'updating a request the email is only sent to the orginal requestor and the Assignee.
        If Request.QueryString("HelpRequestID") > 0 Then
            GetCostThreshold()
            GetOfficeEmail()
        End If
        If (sFinalize = True) Then
            'Checks to make sure if the last person Assigned is the orginal requestor they do not get two emails.
            If (ddlAssignTo.SelectedValue = EmailAddress.Text) Then
                sEmailAddress = ddlAssignTo.SelectedValue
            Else
                sEmailAddress = ddlAssignTo.SelectedValue
                sEmailAddress &= ";" & EmailAddress.Text
            End If
        Else
            Dim cbTarget As Obout.Interface.OboutCheckBox
            For Each litem In cblTo.Items
                cbTarget = CType(litem.FindControl("cbSendTo"), Obout.Interface.OboutCheckBox)
                If cbTarget.Checked Then
                    sEmailAddress &= IIf(Len(sEmailAddress) > 0, ";" & litem.Value, litem.Value)
                End If
            Next
        End If

        'adds the office to the email if the Cost Estimate is larger than the PV threshold
        If txtCost.Text = "" Then
            txtCost.Text = 0
        End If
        If (txtCost.Text > CostThreshold) Then
            sEmailAddress &= IIf(Len(sEmailAddress) > 0, ";" & sOfficeEmail, sOfficeEmail)
        End If

        Try
            If InvWSS.GetData("SELECT TOP 1 Owner FROM tmOwner", tTable) Then
                If tTable.Rows.Count > 0 Then
                    sOwnerName = tTable.Rows(0).Item("Owner")
                End If
            Else
                sOwnerName = Session("OrganizationName")
            End If
        Catch eName As Exception
            sOwnerName = Session("OrganizationName")
        End Try

        sSubject = Replace(Subject.Text, "'", "''")

        If InvWSS.GetData(GetHeader(), tHeaderTable) Then

            Dim sbEmailBody As New StringBuilder
            sbEmailBody.AppendLine(sOwnerName & "<br /><br />")
            sbEmailBody.AppendLine("The following Restaurants are affected:  <br /><br />")
            sbEmailBody.AppendLine("<table>")
            sbEmailBody.AppendLine("    <tr>")
            sbEmailBody.AppendLine("        <td>Store ID:</td>")
            sbEmailBody.AppendLine("        <td>Store Num:</td>")
            sbEmailBody.AppendLine("        <td>Site Name:</td>")
            sbEmailBody.AppendLine("        <td>Phone:</td>")
            sbEmailBody.AppendLine("        <td>Corp Num:</td>")
            sbEmailBody.AppendLine("    </tr>")

            For Each row In tHeaderTable.Rows
                sbEmailBody.AppendLine("    <tr>")
                sbEmailBody.AppendLine("        <td>" & row("StoreId") & "</td>")
                sbEmailBody.AppendLine("        <td>" & row("StoreNum") & "</td>")
                sbEmailBody.AppendLine("        <td>" & row("StoreDescription") & "</td>")
                sbEmailBody.AppendLine("        <td>" & CheckForNull(row("Phone")) & "</td>")
                sbEmailBody.AppendLine("        <td>" & CheckForNull(row("CorpID")) & "</td>")
                sbEmailBody.AppendLine("    </tr>")
            Next
            sbEmailBody.AppendLine("</table>")
            sbEmailBody.AppendLine("<br />")
            sbEmailBody.AppendLine("Requestor: " & Requestor.SelectedItem.Text & "<br />")
            sbEmailBody.AppendLine("Email Address: " & EmailAddress.Text & "<br />")
            sbEmailBody.AppendLine("Subject: " & sSubject & vbCrLf & "<br /><br />")
            sbEmailBody.AppendLine(Replace(Detail.Text, "'", "''") & "<br />")

            'Mark Williams Bug 1709 Adds the Resolution Detail
            If Not (txtResolutionDetail.Text = "") Then
                sbEmailBody.AppendLine("<br /><br />" & "Resolution Information: " & "<br /><br />")
                sbEmailBody.AppendLine(Replace(txtResolutionDetail.Text, "'", "''"))
            End If

            'SendEmailAndText(sbEmailBody.ToString, Subject.Text, sEmailAddress)
            SendMail(sEmailAddress, Subject.Text, sbEmailBody.ToString)
        End If
    End Sub

    Sub SendEmailAndText(ByVal vsEmailBody As String, ByVal vsSubject As String, ByVal vsEmailAddress As String)

        'Obsolete Use AmazonUtilities.Mail.SimpleEmailSystem.SendMail instead
        'WebServicesAddons.AmazonSES.SendMail(vsEmailAddress, vsSubject, vsEmailBody)
        
        SendMail(vsEmailAddress, vsSubject, vsEmailBody)

        'Try
        '    sSQL = "INSERT INTO Message "
        '    sSQL += "("
        '    sSQL += "[AllRecipients], "
        '    sSQL += "[Author], "
        '    sSQL += "[DTRcvd], "
        '    sSQL += "[DTSent], "
        '    sSQL += "[RecordDate], "
        '    sSQL += "[HasAttachments], "
        '    sSQL += "[MsgHeader], "
        '    sSQL += "[Note], "
        '    sSQL += "[ParentFolder], "
        '    sSQL += "[Subject], "
        '    sSQL += "[Viewed], "
        '    sSQL += "[ReplyTo], "
        '    sSQL += "[IsPackage], "
        '    sSQL += "[PackageStatus], "
        '    sSQL += "[POP3Account], "
        '    sSQL += "[POPMsgID], "
        '    sSQL += "[dekaolc], "
        '    sSQL += "[GUID], "
        '    sSQL += "[FromAlias], "
        '    sSQL += "[HTML] "
        '    sSQL += ")"
        '    sSQL += "VALUES "
        '    sSQL += "("
        '    sSQL += "'" & vsEmailAddress & "', "
        '    sSQL += "'<EMAIL>', "
        '    sSQL += "'" & Now() & "', "
        '    sSQL += "'" & Now() & "', "
        '    sSQL += "'" & Now() & "', "
        '    sSQL += "0, "
        '    sSQL += "'', "
        '    sSQL += "'" & vsEmailBody & "', "
        '    sSQL += "1, "
        '    sSQL += "'" & vsSubject & "', "
        '    sSQL += "0, "
        '    sSQL += "'<EMAIL>', "
        '    sSQL += "0, "
        '    sSQL += "0, "
        '    sSQL += "'<EMAIL>', "
        '    sSQL += "'" & InvWSS.Platform.GetGUID() & "<EMAIL>', "
        '    sSQL += "0, "
        '    sSQL += "NewID(), "
        '    sSQL += "'', "
        '    sSQL += "''"
        '    sSQL += ")"

        '    RunCommand(sSQL, "data source=SQL-CLU01;initial catalog=eManager_App4;Password=Reporter001;persist security info=False;user id=sa;workstation id=ASP-WSS-2;packet size=4096")
        'Catch ex As Exception
        '    Response.Write(ex.ToString)
        'End Try
    End Sub

    Sub RunCommand(ByVal SQL As String)
        InvWSS.Platform.SBODBExecNonQuerySQL("SBOCore", SQL)
    End Sub

    'Function GetUserRank() As Integer
    '    Dim sSQL As String = "SELECT T2.Rank FROM tsUserRole T1, tmRole T2 WHERE UniversalAccountIdentifier = '" & Session("AccountID") & "' AND T1.UniversalRoleIdentifier = T2.UniversalRoleIdentifier"
    '    Dim tTable As New DataTable
    '    Dim row As DataRow
    '    Dim iRank As Integer
    '    InvWSS.GetData(sSQL, tTable)
    '    For Each row In tTable.Rows
    '        iRank = row("Rank")
    '    Next
    '    tTable.Dispose()
    '    Return iRank
    'End Function

    Sub btnCancel_click(ByVal sender As Object, ByVal e As EventArgs)
        'Mark Williams Bug 1709
        Response.Redirect("HelpRequestReport.aspx")
    End Sub

    Sub btnDelete_click(ByVal sender As Object, ByVal e As EventArgs)

    End Sub

    Sub btnContinue_click(ByVal sender As Object, ByVal e As EventArgs)
        Response.Redirect("mp_portal.aspx")
    End Sub

    Sub PopStores()
        cblStores.Items.Clear()
        Dim oTableStores As New DataTable
        oTableStores = InvWSS.GetPermittedStores()

        With cblStores
            .DataSource = oTableStores.DefaultView
            .DataTextField = "StoreDescription"
            .DataValueField = "StoreID"
            .DataBind()
            If Not Request.QueryString("HelpRequestID") > 0 Then
                If .Items.Count > 0 Then
                    .SelectedValue = .Items(0).Value
                End If
            End If
        End With
        If Not Request.QueryString("HelpRequestID") > 0 Then
            Dim cbTarget As Obout.Interface.OboutCheckBox
            cbTarget = CType(cblStores.Items.Item(0).FindControl("cbTargetStores"), Obout.Interface.OboutCheckBox)
            cbTarget.Checked = True
        End If
    End Sub
    'Mark Williams Bug 1709
    Sub btnFinalize_Click(ByVal sender As Object, ByVal e As EventArgs)
        DateResolved.Text = CalendarDateResolved.SelectedDate
        sFinalize = True

        Dim dDateNow As Date = Now
        If IsNumeric(HelpRequestID.Text) AndAlso CInt(HelpRequestID.Text) > 0 Then
            sSQL = "UPDATE tmHelpRequest SET "
            If Not (txtAssignedTo.Text = ddlAssignTo.SelectedItem.Text) Then
                'Unless the user changes the Assignee is should not be updated
                sSQL &= "InHouseAssignee = '" & ddlAssignTo.SelectedItem.Text & "', "
                sSQL &= "InHouseAssignedDate = '" & dDateNow & "', "
            End If
            sSQL &= "InHouseResolutionDetail = '" & Replace(txtResolutionDetail.Text, "'", "''") & "', "
            sSQL &= "InHouseCostEstimate = '" & txtCost.Text & "', "
            sSQL &= "Thumbprint = '" & InvWSS.Platform.Application.AccountIdentifier & "', "
            sSQL &= "LastModified = '" & dDateNow & "', "
            sSQL &= "DateResolved = '" & DateResolved.Text & "' "
            sSQL &= "WHERE HelpRequestID = " & nHelpRequestID

            RunCommand(sSQL)
            SendEmailNotice()

            Response.Redirect("HelpRequestReport.aspx")
        End If
    End Sub

    Sub DisableCheckBoxList()
        'Mark Williams Bug 1709
        'In order to disable the Obout CBLs and be able to scroll each checkbox needs to be disabled not the list box
        Dim cbTarget As Obout.Interface.OboutCheckBox
        For Each litem In cblStores.Items
            cbTarget = CType(litem.FindControl("cbTargetStores"), Obout.Interface.OboutCheckBox)
            cbTarget.Enabled = False
        Next
        For Each litem In cblTo.Items
            cbTarget = CType(litem.FindControl("cbSendTo"), Obout.Interface.OboutCheckBox)
            cbTarget.Enabled = False
        Next
    End Sub

    Sub GetCostThreshold()
        'Mark Williams Bug 1709
        Dim sSQL As String
        Dim tPersistentVal As New DataTable

        sSQL = "SELECT DefaultValue FROM tmPersistentValue WHERE UniversalPersistentValueIdentifier = '8C7AA36333FB490ABE68D3604A0A16F9'"
        InvWSS.GetData(sSQL, tPersistentVal)
        CostThreshold = CheckForNull(tPersistentVal.Rows(0).Item("DefaultValue"))
        tPersistentVal.Dispose()

        lblCostDisclaimer.Text = "For cost estimates above $" & CostThreshold & " an email will be sent to the main office."
    End Sub

    Sub GetOfficeEmail()
        'Mark Williams Bug 1709
        'Gets the office email from tmStoreInfo
        Dim sSQL As String
        Dim sOfficeSQL As String
        Dim tStoreDesc As New DataTable
        Dim tOfficeEmail As New DataTable
        Dim row As DataRow

        sSQL = "SELECT InHouseStoreDesc FROM tmHelpRequest WHERE HelpRequestID = " & nHelpRequestID
        If InvWSS.GetData(sSQL, tStoreDesc) Then
            aryStoreDesc = Split(tStoreDesc.Rows(0).Item("InHouseStoreDesc"), ",")

            sOfficeSQL = "Select Distinct HomeOfficeEmail FROM tmStoreInfo info INNER JOIN tmStore sto "
            sOfficeSQL &= "ON info.StoreID = sto.StoreID WHERE sto.StoreDescription IN ("
            For i As Integer = 0 To aryStoreDesc.Length - 1
                If i = aryStoreDesc.Length - 1 Then
                    sOfficeSQL &= "'" & aryStoreDesc(i) & "'"
                Else
                    sOfficeSQL &= "'" & aryStoreDesc(i) & "', "
                End If
            Next
            sOfficeSQL &= ")"

            InvWSS.GetData(sOfficeSQL, tOfficeEmail)
            For Each row In tOfficeEmail.Rows
                sOfficeEmail &= IIf(Len(sOfficeEmail) > 0, ";" & CheckForNull(row("HomeOfficeEmail")), CheckForNull(row("HomeOfficeEmail")))
            Next
            tOfficeEmail.Dispose()
        End If
    End Sub

    Protected Overrides Sub Finalize()
        MyBase.Finalize()
    End Sub
End Class
