﻿Option Explicit On

Imports System.Data
Imports System.Data.SqlClient
Imports SBOTypeLibrary
Imports SBOWebSiteServices
Imports InventoryWebSiteServices
Imports WebServicesAddons.SendGridEmailService

Partial Class ExpressMail
    Inherits System.Web.UI.Page

    Private InvWSS As InventoryWebSiteServices.InventoryServices
    Dim EMCon As SqlConnection

    Sub Page_Load(ByVal Sender As Object, ByVal E As EventArgs) Handles Me.Load
        InvWSS = Session("IWSS")

        Session("AdminServer") = "SQL-CLU01"
        If Not IsPostBack Then
            If Len(Request.QueryString("OrgNo")) Then
                OrgID.Text = Request.QueryString("OrgNo")
            End If
            If Len(Request.QueryString("StoreID")) Then
                StoreID.Text = Request.QueryString("StoreID")
            End If
            If Len(Request.QueryString("StoreName")) Then
                StoreName.Text = Request.QueryString("StoreName")
            End If
            SetOrgInfo()
        End If
    End Sub

    Sub SetOrgInfo()
        Dim OrgSQL As String
        Dim tTable As New DataTable
        Dim row As DataRow
        Dim lItem As ListItem
        Dim sName As String = ""
        OrgSQL = "SELECT Server, LocalDatabaseName, tbOrganization.Organization, tbOrganization.OrganizationNumber FROM tbOrganizationDatabase INNER JOIN tbOrganization ON tbOrganizationDatabase.OrganizationNumber = tbOrganization.OrganizationNumber "
        OrgSQL &= "WHERE tbOrganization.OrganizationNumber = " & OrgID.Text
        tTable = InvWSS.Platform.SBODBExecQuerySQL("SBORoot", OrgSQL)
        For Each row In tTable.Rows
            OrgName.Text = row("Organization")
            OrgSQLServer.Text = row("Server")
            OrgSQLDB.Text = row("LocalDatabaseName")
        Next
        Dim OrgNameSQL As String = "SELECT Owner FROM tmOwner"
        Dim tOwner As New DataTable
        tOwner = GetDBTable(OrgNameSQL, OrgSQLServer.Text, OrgSQLDB.Text)
        For Each row In tOwner.Rows
            OrgName.Text = row("Owner")
        Next

        'Pop eMail Addresses
        Dim sEmailAddress As String = ""
        'select lastname, firstname, r.description, r.[rank] from tmAccount a


        If Len(StoreID.Text) > 0 Then
            sEmailAddress = "select DISTINCT acc.LastName, acc.FirstName, r.Description AS UserRole, acc.EmailAddress from WEBRPT_vjBinPermissions bp inner join tmStore on bp.UniversalStoreIdentifier = tmStore.UniversalStoreIdentifier "
            sEmailAddress &= "left outer join tsUserRole ur on bp.UniversalAccountIdentifier = ur.UniversalAccountIdentifier "
            sEmailAddress &= "left outer join tmRole r on ur.UniversalRoleIdentifier = r.UniversalRoleIdentifier "
            sEmailAddress &= "left outer join tmAccount acc on bp.UniversalAccountIdentifier = acc.UniversalAccountIdentifier "
            sEmailAddress &= "where tmStore.StoreID = " & StoreID.Text & " "
            sEmailAddress &= "and acc.ActiveAccount <> 0 AND acc.InHouseUser = 0 "
            sEmailAddress &= "and description IN ('Dealer Support', 'District Manager','Region Manager','Regional Manager','Manager') "
            sEmailAddress &= "order by acc.EmailAddress, acc.LastName"
        Else
            sEmailAddress = "SELECT DISTINCT a.LastName, a.FirstName, a.EmailAddress, r.Description AS UserRole FROM tmAccount a "
            sEmailAddress &= "left outer join tsUserRole ur on a.UniversalAccountIdentifier = ur.UniversalAccountIdentifier "
            sEmailAddress &= "left outer join tmRole r on ur.UniversalRoleIdentifier = r.UniversalRoleIdentifier "
            sEmailAddress &= "WHERE a.ActiveAccount <> 0 AND NOT a.EmailAddress IS NULL ORDER BY a.EmailAddress, a.LastName"

        End If
        Dim tAddress As New DataTable
        tAddress = GetDBTable(sEmailAddress, OrgSQLServer.Text, OrgSQLDB.Text)
        For Each row In tAddress.Rows
            sName = ""
            If Not IsDBNull(row("LastName")) Then
                sName = row("LastName")
            End If
            If Not IsDBNull(row("FirstName")) Then
                sName &= IIf(Len(sName) > 0, ", " & row("FirstName"), row("FirstName"))
            End If
            If Not IsDBNull(row("UserRole")) Then
                sName &= " (" & row("UserRole") & ")"
            End If
            lItem = New ListItem(sName)
            If Not IsDBNull(row("EmailAddress")) Then
                lItem.Value = row("EmailAddress")
            Else
                lItem.Value = " "
                lItem.Enabled = False
            End If

            cbTo.Items.Add(lItem)
        Next

        lItem = New ListItem("<EMAIL>")
        lItem.Value = "<EMAIL>"
        cbCC.Items.Add(lItem)
        lItem.Selected = True
        lItem = New ListItem("<EMAIL>")
        lItem.Value = "<EMAIL>"
        cbCC.Items.Add(lItem)
        lItem.Selected = True

        lItem = New ListItem("<EMAIL>")
        lItem.Value = "<EMAIL>"
        cbCC.Items.Add(lItem)
        lItem = New ListItem("<EMAIL>")
        lItem.Value = "<EMAIL>"
        cbCC.Items.Add(lItem)
        lItem = New ListItem("<EMAIL>")
        lItem.Value = "<EMAIL>"
        cbCC.Items.Add(lItem)
        lItem = New ListItem("<EMAIL>")
        lItem.Value = "<EMAIL>"
        cbCC.Items.Add(lItem)
        lItem = New ListItem("<EMAIL>")
        lItem.Value = "<EMAIL>"
        cbCC.Items.Add(lItem)
        lItem = New ListItem("<EMAIL>")
        lItem.Value = "<EMAIL>"
        cbCC.Items.Add(lItem)


        'Exceptions
        AddException(0, "Select Exception")
        AddException(1, "Closing SMP/QS Call Opened")
        AddException(2, "Not Closed Yet/No Apparent Issue")
        AddException(3, "Not Closed/Hardware Issue")
        AddException(4, "Not Reporting to SBONet/ISP Issue - Closing/RealTime")
        AddException(5, "Closing Issue/Already Opened for business today - generic")
        AddException(6, "No Sales Reporting today")
        AddException(7, "Not Closed/No one to contact")
        AddException(8, "Subway closing")
        AddException(9, "Closing Issue and Opened for business today - SMP")
        AddException(10, "Closing Issue and Employees on the Clock - generic")
        AddException(11, "Closing issue, data erased in POS")
        AddException(12, "Other")


        'stores

        Dim tStoreTable As New DataTable
        Dim rowStore As DataRow
        Dim cbItem As ListItem
        Dim sStoreSQL As String = "SELECT tmStore.StoreID, StoreDescription FROM tmStore "
        sStoreSQL &= "LEFT OUTER JOIN tmStoreInvt sinvt ON tmStore.StoreID = sinvt.StoreID "
        sStoreSQL &= "LEFT OUTER JOIN tmNode Node ON tmStore.UniversalNodeIdentifier = Node.UniversalNodeIdentifier "
        sStoreSQL &= "WHERE Node.ActiveNode <> 0 AND sinvt.ShowOnMCD <> 0 "
        sStoreSQL &= "ORDER BY StoreDescription"

        tStoreTable = GetDBTable(sStoreSQL, OrgSQLServer.Text, OrgSQLDB.Text)
        For Each rowStore In tStoreTable.Rows
            cbItem = New ListItem(rowStore("StoreDescription"))
            cbItem.Value = rowStore("StoreID")
            cbStores1.Items.Add(cbItem)
            cbStores2.Items.Add(cbItem)
            cbStores3.Items.Add(cbItem)
            cbStores4.Items.Add(cbItem)
        Next
    End Sub

    Sub AddException(ByVal iExceptionNo As Integer, ByVal sExceptionTitle As String)
        Dim lItem As ListItem
        lItem = New ListItem(sExceptionTitle)
        lItem.Value = iExceptionNo
        ddException1.Items.Add(lItem)
        ddException2.Items.Add(lItem)
        ddException3.Items.Add(lItem)
        ddException4.Items.Add(lItem)
    End Sub

    Sub PopGrid()
        Dim sSQL As String
        sSQL = "select si.StoreID, si.StoreCmd, si.Parm1, si.Parm2, si.Parm3, si.Parm4, si.ProcessOrder, s.StoreDescription, s.StoreNum, si.OneTimeOnly from tdstoreinstructions si inner join tmStore s ON si.StoreID = s.StoreID WHERE si.StoreID = " & StoreID.Text & " order by s.storedescription, si.processorder"
        Dim tTable As New DataTable
        tTable = GetDBTable(sSQL, OrgSQLServer.Text, OrgSQLDB.Text)
        tTable.Dispose()
    End Sub

    Sub PopStores()
        Dim tStoreTable As New DataTable
        Dim rowStore As DataRow
        Dim cbItem As ListItem
        Dim sStoreSQL As String = "SELECT tmStore.StoreID, StoreDescription FROM tmStore "
        sStoreSQL &= "LEFT OUTER JOIN tmStoreInvt sinvt ON tmStore.StoreID = sinvt.StoreID "
        sStoreSQL &= "LEFT OUTER JOIN tmNode Node ON tmStore.UniversalNodeIdentifier = Node.UniversalNodeIdentifier "
        sStoreSQL &= "WHERE Node.ActiveNode <> 0 AND sinvt.ShowOnMCD <> 0 "
        sStoreSQL &= "ORDER BY StoreDescription"

        tStoreTable = GetDBTable(sStoreSQL, OrgSQLServer.Text, OrgSQLDB.Text)
        For Each rowStore In tStoreTable.Rows
            cbItem = New ListItem(rowStore("StoreDescription"))
            cbItem.Value = rowStore("StoreID")
            'cbStores.Items.Add(cbItem)
            If cbItem.Value = Val(StoreID.Text) Then
                cbItem.Selected = True
            End If
        Next
    End Sub

    Sub DeleteStoreInstructions(ByVal StoreID As Integer)

    End Sub

    Sub RunCommand(ByVal SQL As String, ByVal sConnection As String)
        Dim dbConnect As New SqlConnection(sConnection)
        Dim cmdSQL As SqlCommand
        dbConnect.Open()
        cmdSQL = New SqlCommand(SQL, dbConnect)
        With cmdSQL
            .CommandTimeout = 0
            .ExecuteNonQuery()
            .Dispose()
        End With
        dbConnect.Close()
        dbConnect.Dispose()
    End Sub

    Sub SendButtonClick(ByVal sender As Object, ByVal e As EventArgs)
        EMCon = New SqlConnection("data source=SQL-CLU01;initial catalog=eManager_App4;Password=Reporter001;persist security info=False;user id=sa;workstation id=ASP-WSS-1;packet size=4096")
        EMCon.Open()
        CheckAndSend(ddException1, cbStores1, Comments1)
        CheckAndSend(ddException2, cbStores2, Comments2)
        CheckAndSend(ddException3, cbStores3, Comments3)
        CheckAndSend(ddException4, cbStores4, Comments4)
        EMCon.Close()
        EMCon.Dispose()
        'Response.Write("<script language='javascript'>")
        'Response.Write("alert('Email(s) have been scheduled to send via eManager.');")
        'Response.Write("<")
        'Response.Write("/")
        'Response.Write("script>")
        Page.ClientScript.RegisterClientScriptBlock(GetType(Page), "alert", "alert('Email(s) have been scheduled to send via eManager.');", True)
    End Sub

    Sub CheckAndSend(ByRef ddException As DropDownList, ByRef cblStores As CheckBoxList, ByRef tbComments As TextBox)
        Dim lItem As ListItem
        Dim sEffectedStores As String = ""
        Dim sEmailBody As String = ""
        Dim sHTMLBody As String = ""
        Dim sHTMLStores As String = ""
        Dim sAddresses As String = ""
        Dim sSubject As String = "SBONet Audit Exceptions - " & Replace(OrgName.Text, "'", "")
        For Each lItem In cbTo.Items
            If lItem.Selected Then
                sAddresses &= IIf(Len(sAddresses), ", " & lItem.Value, lItem.Value)
            End If
        Next
        For Each lItem In cbCC.Items
            If lItem.Selected Then
                sAddresses &= IIf(Len(sAddresses), ", " & lItem.Value, lItem.Value)
            End If
        Next

        If ddException.SelectedValue > 0 Then
            For Each lItem In cblStores.Items
                If lItem.Selected Then
                    sEffectedStores &= Replace(lItem.Text, "'", "") & vbCrLf
                    sHTMLStores &= Replace(lItem.Text, "'", "") & "<br></br>"
                End If
            Next
            If sEffectedStores <> String.Empty Then
                sEffectedStores = "The following stores were affected:" & vbCrLf & sEffectedStores & vbCrLf & vbCrLf
                sHTMLStores = "The following stores were affected:<br></br>" & sHTMLStores
            End If
            sHTMLBody = "<HTML>"
            sEmailBody = ddException.SelectedItem.Text & vbCrLf & vbCrLf
            sHTMLBody &= "<b>" & ddException.SelectedItem.Text & "</b>"
            sEmailBody &= ExceptionDescription(ddException.SelectedValue) & vbCrLf & vbCrLf & vbCrLf
            sHTMLBody &= "<p>" & ExceptionDescription(ddException.SelectedValue) & "</p>"
            sEmailBody &= sEffectedStores
            sHTMLBody &= "<p>" & sHTMLStores & "<p>"
            sEmailBody &= tbComments.Text & vbCrLf
            sHTMLBody &= "<p>" & tbComments.Text & "</p></html>"


            If sAddresses <> String.Empty And sSubject <> String.Empty And sHTMLBody <> String.Empty Then
                SendEmail(sAddresses, sSubject, sEmailBody, sHTMLBody)
                Dim sSQLCon As String = BuildDBConnectString("SQL-CLU01", "SBONet_Admin", "sboapplication", "SoftwareA43A")
                Dim dbCon As New SqlConnection(sSQLCon)
                dbCon.Open()
                For Each lItem In cblStores.Items
                    If lItem.Selected = True Then
                        Dim sLogText As String = ""
                        Dim SQL As String = ""
                        sLogText = "Sent eMail to: " & sAddresses
                        sLogText &= vbCrLf & sEmailBody

                        SQL = "INSERT INTO [dbo].[BI_tmAdminMsg] ([Msg],[MsgDate],[MsgAuthor],[OrganizationNumber],[StoreID],[EntryDateTime],[OrganizationName],[StoreDescription],[Status]) "
                        SQL &= "VALUES ('" & sLogText & "', '" & Now.ToShortDateString & "', '" & Session("AccountID") & "', " & Replace(OrgID.Text, "'", "") & ", " & lItem.Value.ToString & ", '" & Now.ToString & "','" & Replace(OrgName.Text, "'", "") & "','" & lItem.Text & "'," & EntryStatus.SelectedValue.ToString & ")"
                        Dim Cmd1 As New SqlCommand(SQL, dbCon)
                        Cmd1.ExecuteNonQuery()
                        Cmd1.Dispose()
                    End If
                Next
                dbCon.Close()
                dbCon.Dispose()
            End If
        End If

    End Sub

    Function ExceptionDescription(ByVal ExceptionNo As Integer) As String
        Dim sResult As String = ""
        Select Case ExceptionNo
            Case 1
                sResult = "An End-of-Day failure has occurred in POS System.  The QS HelpDesk is working to contact the location and complete the close.  Once the closing issues are addressed, all available data will be recovered to SBONet."
            Case 2
                sResult = "Store has not yet performed or is currently performing the POS close for last night.  Site will need to be contacted if process has not been initiated soon.  Closing data will be recovered to SBONet after the POS close has been completed."
            Case 3
                sResult = "Store attempted to perform the POS close for yesterday but was unable to complete the process.  The Quick Service Help Desk to address issues and completing the close for last night.  Once the closing issues are addressed, all available data will be recovered to SBONet."
            Case 4
                sResult = "This location has lost internet connectivity for an extended timeframe and stopped reporting to SBOnet.  All missing data will be recovered once the internet connectivity is restored. Please contact DUMAC Support as soon as the location is online or if assistance is required."
            Case 5
                sResult = "Store did not complete close for last night and already collected sales today. Due to this condition, data for yesterday and today are combining and showing as a single date.  Notified Store Manager of issue."
            Case 6
                sResult = "No sales information has reported to SBOnet from this location for the current business date.  The POS register report at the site reflects no sales activity for today. Please contact the WebServices team at DUMAC if details on this situation are available."
            Case 7
                sResult = "Location has not yet completed the close for last night and no one available onsite to contact.   Left a message on the back office PC for the store to contact Dumac."
            Case 8
                sResult = "Wilco- A Subway location has not reported data to your server. Please contact Dumac once the missing data has been sent to your server.  DUMAC will recover the data at that time."
            Case 9
                sResult = "An End of Day failure occurred in SMP and sales have been collected for the current business date. Due to this condition, data for yesterday and today are combining and showing as a single date.  Notified Store Manager of issue."
            Case 10
                sResult = "Store has not yet completed the close for last night and it cannot be executed remotely due to employees still being on the clock.  Still attempting to contact location to correct.  Please contact Dumac if assistance is needed with closing."
            Case 11
                sResult = "Closing financial data was not archived at this location for yesterdays business date due to POS issues during the EOD process.  Data on SBOnet has been corrected with the last available real time data from the store.  Please contact Dumac if assistance is needed."
            Case 12
                sResult = ""
        End Select
        sResult = Replace(sResult, ". ", ".&nbsp;&nbsp;")
        Return sResult
    End Function

    Sub SendEmail(ByVal sEmailAddress As String, ByVal sSubject As String, ByVal sMessage As String, ByVal sHTML As String)
        WebServicesAddons.SendGridEmailService.SendMail(sEmailAddress, sSubject, sHTML, True)
    End Sub

    Sub GetStoreInfo(ByVal StoreID As Integer, ByRef StoreNum As Integer, ByRef POSType As String)
        Dim tTable As New DataTable
        Dim sSQL As String = "select StoreID, StoreNum, Description from vjStoreInvt Inner Join tmPOSProgram ON vjStoreInvt.POSProgramID = tmPOSProgram.POSProgramID WHERE StoreID=" & StoreID
        tTable = GetDBTable(sSQL, OrgSQLServer.Text, OrgSQLDB.Text)
        Dim row As DataRow
        Try
            For Each row In tTable.Rows
                StoreNum = row("StoreNum")
                POSType = row("Description")
            Next
        Catch eBummer As Exception
        End Try
    End Sub

    Function GetDBTable(ByVal sSQL As String, ByVal sSQLServer As String, ByVal sSQLDatabase As String) As DataTable
        Dim SQLCon As New SqlConnection
        Dim SQLCmd As New SqlDataAdapter
        Dim tTable As New DataTable
        Dim DBConString As String = BuildDBConnectString(sSQLServer, sSQLDatabase, "sboapplication", "SoftwareA43A")
        Dim bResult As Boolean = False
        Try
            SQLCon = New SqlConnection(DBConString)
            SQLCon.Open()

            SQLCmd = New SqlDataAdapter(sSQL, SQLCon)
            SQLCmd.Fill(tTable)
            SQLCon.Close()
            bResult = True
        Catch eGetDataTable As Exception
            'sSQLErr &= sSQLServer & ":" & sSQLDatabase & " - " & eGetDataTable.Message & "<p>"
        Finally
            If Not SQLCmd Is Nothing Then
                SQLCmd.Dispose()
            End If
            If Not SQLCon Is Nothing Then
                SQLCon.Dispose()
            End If
        End Try
        Return tTable
    End Function

    Public Function BuildDBConnectString(Optional ByVal SQLServer As String = "", Optional ByVal SQLDatabase As String = "", Optional ByVal SQLUserID As String = "", Optional ByVal SQLPassword As String = "", Optional ByVal PersistSecurityInfo As Boolean = False) As String
        Dim sConnect As String = ""
        If Len(SQLServer) Then
            sConnect = sConnect & "Data Source=" & SQLServer & ";"
        End If
        If Len(SQLDatabase) Then
            sConnect = sConnect & "Database=" & SQLDatabase & ";"
        End If
        If Len(SQLUserID) Then
            sConnect = sConnect & "User ID=" & SQLUserID & ";"
        End If
        If Len(SQLPassword) Then
            sConnect = sConnect & "Password=" & SQLPassword & ";"
        End If
        If PersistSecurityInfo = True Then
            sConnect = sConnect & "Persist Security Info=True;"
        End If
        Return sConnect
    End Function
End Class
