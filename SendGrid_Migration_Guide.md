# SendGrid Email Migration Guide

## Overview
This document outlines the migration from Amazon SES to SendGrid for email sending in the SBOnet application.

## Changes Made

### 1. Configuration Updates (web.config)
Added SendGrid configuration settings:
```xml
<!-- SendGrid Configuration -->
<add key="SendGrid-ApiKey" value="YOUR_SENDGRID_API_KEY_HERE"/>
<add key="SendGrid-FromEmail" value="<EMAIL>"/>
<add key="SendGrid-FromName" value="SBOnet System"/>
```

### 2. New SendGrid Service
Created `SendGridEmailService.vb` with the following features:
- **SendMail(recipient, subject, body, [isHTML], [sender])** - Basic email sending
- **SendMail(recipient, subject, body, fileStream, [fileName], [isHTML], [sender])** - Email with file attachment
- **SendMail(recipient, subject, body, filePath, [isHTML], [sender])** - Email with file path attachment
- Uses SendGrid v3 API
- Supports multiple recipients (semicolon-separated)
- Handles attachments with base64 encoding
- Configurable sender information

### 3. Files Updated
The following files have been updated to use SendGrid:

#### Core Help Request Files:
- `Admin_HelpRequestEdit.aspx.vb` - Updated SendMail call
- `HelpRequestEdit.aspx.vb` - Updated SendEmailAndText function
- `Admin\HelpRequestAdminReport.aspx.vb` - Updated SendEmail function

#### User Management:
- `Admin_UserProfile.aspx.vb` - Updated dealer alerts and password reset emails

#### Report Files with Attachments:
- `CaliforniaLaborAudit.aspx.vb` - Updated PDF email reports
- `FinancialSummary.aspx.vb` - Updated financial report emails

#### Scheduler:
- `Model\Support\ScheduleEmailer.vb` - Updated employee schedule emails

#### Utility Files:
- `Admin\ExpressMail.aspx.vb` - Updated SendEmail function
- `SMT.aspx.vb` - Updated test email functionality

## Remaining Files to Update

The following files still need to be updated to use SendGrid:

### Files with SendMail calls:
1. `FinSumReport.aspx.vb` - Financial summary reports
2. `CorePerforma.aspx.vb` - Core performance reports  
3. `DashHourLabor.aspx.vb` - Hourly labor reports
4. `Admin_FreestyleTheoretical.aspx.vb` - Theoretical reports
5. `Admin_FinAdjust.aspx.vb` - Financial adjustment reports
6. `NewOrderLTO.aspx.vb` - Order management emails
7. `HistoricalSalesImport.aspx.vb` - Sales import notifications

### Pattern for Updates:
For each file, follow this pattern:

1. **Add Import Statement:**
```vb
Imports WebServicesAddons.SendGridEmailService
```

2. **Replace SendMail Calls:**
```vb
' Old:
SendMail(recipient, subject, body)
AmazonUtilities.Mail.SimpleEmailService.SendMail(recipient, subject, body)

' New:
WebServicesAddons.SendGridEmailService.SendMail(recipient, subject, body)
```

3. **For Attachment Calls:**
```vb
' Old:
SendMail(recipient, subject, body, fileStream, fileName)

' New:
WebServicesAddons.SendGridEmailService.SendMail(recipient, subject, body, fileStream, fileName)
```

## Setup Instructions

### 1. SendGrid Account Setup
1. Create a SendGrid account at https://sendgrid.com
2. Generate an API key with "Mail Send" permissions
3. Update the `SendGrid-ApiKey` value in web.config

### 2. Domain Authentication (Recommended)
1. Set up domain authentication in SendGrid for better deliverability
2. Update `SendGrid-FromEmail` to use your authenticated domain

### 3. Testing
1. Update the API key in web.config
2. Test with a simple email first (SMT.aspx.vb)
3. Verify email delivery and check SendGrid activity logs

## Benefits of SendGrid Migration

1. **Better Deliverability** - SendGrid specializes in email delivery
2. **Advanced Analytics** - Detailed email tracking and analytics
3. **Scalability** - Better handling of high-volume email sending
4. **Modern API** - RESTful API with better error handling
5. **Cost Effectiveness** - Competitive pricing for email volumes

## Rollback Plan

If issues occur, you can temporarily rollback by:
1. Reverting the import statements
2. Changing SendGrid calls back to the original SendMail calls
3. The original Amazon SES configuration remains in web.config

## Notes

- The `AmazonSES.vb` file is marked as obsolete and can be removed after migration
- The `AmazonUtilities.dll` dependency can be removed once all files are migrated
- Consider implementing proper logging for email send failures
- Monitor SendGrid usage and adjust rate limits if needed
