/// <reference path="../external/jquery-1.4.2-vsdoc.js" />
/// <reference path="../external/templating.js" />
/// <reference path="../external/json.js" />
/// <reference path="../dataservice.js" />

// 2020-10-27 jjc Sprint 79w44 SBOD-2014 Added \' to the regex for checking the validity of email addresses.

var emailtimetable = function () {

    var employeesTemplate = null;
    var $divEmployees = null;
    var $daysSelected = null;
    var $employeesSelected = null;
    var $btnSendEmail = null;
    var $txtCustomMessage = null;
    var emailStatusTemplate = null;
    var emailWaitingTemplate = null;
    var $emailResultPopup = null;
    var _scheduleID = null;

    var $chkDays = null;

    function renderEmployees() {

        var filteredEmployees = getFilteredEmployees();

        var params = { "employees": filteredEmployees };

        var output = parseTemplate(employeesTemplate, params);

        $divEmployees.html(output);

        updateEmployeesSelectedCount();
    }

    function getFilteredEmployees() {
        var day1Selected = $chkDays.filter('input#chkDay1').is(':checked');
        var day2Selected = $chkDays.filter('input#chkDay2').is(':checked');
        var day3Selected = $chkDays.filter('input#chkDay3').is(':checked');
        var day4Selected = $chkDays.filter('input#chkDay4').is(':checked');
        var day5Selected = $chkDays.filter('input#chkDay5').is(':checked');
        var day6Selected = $chkDays.filter('input#chkDay6').is(':checked');
        var day7Selected = $chkDays.filter('input#chkDay7').is(':checked');

        var filteredEmployees = [];

        for (var i = 0; i < employees.length; i++) {

            if (day1Selected && employees[i].OnDay1 ||
               day2Selected && employees[i].OnDay2 ||
               day3Selected && employees[i].OnDay3 ||
               day4Selected && employees[i].OnDay4 ||
               day5Selected && employees[i].OnDay5 ||
               day6Selected && employees[i].OnDay6 ||
               day7Selected && employees[i].OnDay7) {
                filteredEmployees.push(employees[i]);
            }
        }

        return filteredEmployees;
    }

    function handleSendEmailRequest() {

        if (!confirm("Are you sure you would like to send schedule information to the selected employees?")) {
            return;
        }

        $emailResultPopup.html(parseTemplate(emailWaitingTemplate, {}));
        $emailResultPopup.show('fast');

        var bargraphIDs = [];
        var employeeIDs = [];
        $chkDays.filter(":checked").each(function () {
            bargraphIDs.push(parseInt($(this).val()));
        });

        $getCheckedEmployees().each(function () {
            employeeIDs.push(parseInt($(this).val()));
        });

        var bargraphIDsJSON = JSON.stringify(bargraphIDs);
        var employeeIDsJSON = JSON.stringify(employeeIDs);

        params = JSON.stringify({
            scheduleID: _scheduleID,
            bargraphIDs: bargraphIDs,
            employeeIDs: employeeIDs,
            customMessage: $txtCustomMessage.val()
        });

        //var params = "{ 'scheduleID': " + _scheduleID + ", 'bargraphIDs': " + bargraphIDsJSON +
        //    ", 'employeeIDs': " + employeeIDsJSON + ", 'customMessage': '" + $txtCustomMessage.val() +
        //    "'}";

        dataservice.callService("SendEmails", params, receiveEmailSendResults);
    }

    function receiveEmailSendResults(data) {

        var params = { 'statuses': data.d };

        var output = parseTemplate(emailStatusTemplate, params);

        $emailResultPopup.html(output);

    }

    function $getCheckedEmployees() {
        return $('div#divEmployees table tr td input:checkbox:checked');
    }

    function updateDaySelectedCount() {
        var count = $chkDays.filter(':checked').length;

        $daysSelected.text("(" + count + " days selected)");
    }

    function updateEmployeesSelectedCount() {
        var count = $getCheckedEmployees().length;

        $employeesSelected.text("(" + count + " selected)");

        if (count > 0) {
            $btnSendEmail.removeAttr('disabled');
            $btnSendEmail.text('Send Emails...');
        } else {
            $btnSendEmail.attr('disabled', 'true');
            $btnSendEmail.text('Select at least 1 employee');
        }
    }


    return {
        initialize: function (scheduleID) {
            employeesTemplate = $('script#employeesTemplate').html();
            $divEmployees = $('div#divEmployees');
            $chkDays = $('div#divDaysToInclude table tr td input:checkbox');
            $daysSelected = $('div#divDaysToInclude span#daysSelected');
            $employeesSelected = $('div#divEmployeeContainer span#employeesSelected');
            $btnSendEmail = $('button#btnSendEmail');
            $txtCustomMessage = $('textarea#txtCustomMessage');
            emailStatusTemplate = $('script#emailStatusTemplate').html();
            emailWaitingTemplate = $('script#emailWaitingTemplate').html();
            $emailResultPopup = $('div#emailResultsPopup');
            _scheduleID = scheduleID;

            $divEmployees.find('input:checkbox').change(function () { updateEmployeesSelectedCount(); });

            $chkDays.change(function () {
                updateDaySelectedCount();
                renderEmployees();
            });

            $('button#btnSelectAllEmployees').click(function () {
                $divEmployees.find(':checkbox').each(function () {
                    if ($(this).attr('disabled')) {
                        return;
                    } else {
                        var employee = emailtimetable.findEmployee($(this).val());
                        employee.Selected = true;
                        $(this).attr('checked', 'checked');
                    }
                });

                updateEmployeesSelectedCount();
                return false;
            });

            $('button#btnSelectAllDays').click(function () {
                $chkDays.each(function () {
                    $(this).attr('checked', 'checked');
                });
                renderEmployees();
                updateDaySelectedCount();
                updateEmployeesSelectedCount();
                return false;
            });

            $('button#btnClearAllDays').click(function () {
                $chkDays.each(function () {
                    $(this).removeAttr('checked');
                });
                renderEmployees();
                updateDaySelectedCount();
                updateEmployeesSelectedCount();
                return false;
            });

            $('button#btnClearAllEmployees').click(function () {
                $divEmployees.find(':checkbox').each(function () {
                    var employee = emailtimetable.findEmployee($(this).val());
                    employee.Selected = false;
                    $(this).removeAttr('checked');
                });
                updateEmployeesSelectedCount();
                return false;
            });

            $('div.heading.expandable').click(function () {
                var $hidableContents = $(this).nextAll('.hidableContent');

                if ($hidableContents.is(':visible'))
                    $hidableContents.slideUp('slow');
                else
                    $hidableContents.slideDown('slow');
            });

            $btnSendEmail.click(function () {
                handleSendEmailRequest();
                return false;
            });

            $('div#divEmployees table tr td input:checkbox').live('change', null, function () {
                var employee = emailtimetable.findEmployee($(this).val());

                employee.Selected = $(this).is(':checked');
                updateEmployeesSelectedCount();
            });

            renderEmployees();

            updateEmployeesSelectedCount();
            updateDaySelectedCount();
        }
    }
} ();

emailtimetable.createEmployeeRow = function (employee) {
    var hasValidEmail = emailtimetable.employeeHasValidEmail(employee);

    // deselect employee if doesn't have a valid email address
    if (!hasValidEmail || employee.Email == '' ) {
        employee.Selected = false;
    }

    var output = "<td><input type='checkbox' value='" + employee.ID + "'";

    if (employee.Email === '') {
        output += " disabled='true'></td><td>" + employee.Name + "</td>";
        output += "<td class='notSet'>Email address not specified</td>";
    } else if (!hasValidEmail) {
        output += " disabled='true'></td><td>" + employee.Name + "</td>";
        output += "<td class='notValid'>" + employee.Email + " (not valid)</td>";
    } else {
        var checked = employee.Selected ? " checked='checked'" : '';
        output += checked + "></td><td>" + employee.Name + "</td>";
        output += "<td>" + employee.Email + "</td>";
    }

    return output;
}

emailtimetable.findEmployee = function (employeeID) {

    employeeID = parseInt(employeeID);

    for (var i = 0; i < employees.length; i++) {
        if (employees[i].ID === employeeID) return employees[i];
    }

    throw new Error("Could not find employee " & employeeID);
}

emailtimetable.employeeHasValidEmail = function (employee) {
    var email = employee.Email;
    var filter = /^([a-zA-Z0-9_\-\.\']+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([a-zA-Z0-9\-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$/;
    return filter.test(email);
}