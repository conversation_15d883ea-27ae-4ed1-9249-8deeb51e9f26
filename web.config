<configuration>
  <configSections>
  </configSections>
  <appSettings>
    <add key="aspnet:MaxHttpCollectionKeys" value="15001" />
    <add key="vs:EnableBrowserLink" value="false" />
    <add key="DemoMode" value="Demo" />
    <add key="DetailedErrorMessages" value="true" />
    <add key="EmailerFromAddress" value="<EMAIL>" />
    <add key="HasDumacFunctions" value="true" />
    <!-- required for specific functionality to Dumac, e.g., US Social Security numbers, extra reports-->
    <add key="SplashLogoUrl" value="Images/DUMAC/SBONet.png" />
    <add key="SplashTitle" value="false" />
    <add key="ShortcutIconPath" value="/Images/DUMAC/favicon.ico" />
    <add key="AWSProfileName" value="SBOnet" />
    <add key="AWSRegion" value="us-east-1" />
    <add key="AWSAccessKey" value="********************" />
    <add key="AWSSecretKey" value="jE8wb5siq3zFAxmqjny4uxVb+ymh52niy1Mj5pG2"/>

    <!-- Old SIGv2 Credentials -->
    <!--<add key="AmazonSES-Username" value="********************"/>-->
    <!--<add key="AmazonSES-Password" value="AjNRrKualXwJ+6LL+5E0EhxORghjI9HdCmlFHR5Co0Re"/>-->
    <!-- New (2021-04-26) SIGv4 Credentials -->
    <add key="AmazonSES-Username" value="********************"/>
    <add key="AmazonSES-Password" value="BGSZ1YVE0rdYHMxAZv5w+o7J+Yu6lIuKadHT0Hru2q6r"/>
    <add key="AmazonSES-Host" value="email-smtp.us-east-1.amazonaws.com"/>
    <add key="AmazonSES-Port" value="587"/>

    <add key="RegrinderUsername" value="SBOjobs"/>
    <add key="RegrinderPassword" value="hn868xI17Z3"/>
    <add key="RegrinderHost" value="sftp.dumac.com" />
    
	<!-- Email Configuration -->
    <add key="EmailAddress.SBOWebWork" value="<EMAIL>" />
    <add key="EmailAddress.HelpDesk" value="<EMAIL>" />
    <add key="EmailAddress.Webservices" value="<EMAIL>" />
    <add key="EmailAddress.Alerts" value="<EMAIL>" />
	<add key="EmailAddress.ProductChanges" value="<EMAIL>" />	 
	
  </appSettings>
  <connectionStrings>
    <add name="HVConnectionString" connectionString="Data Source=ASP-2020-SQL_2005;Initial Catalog=SA-SCHEDULER;Persist Security Info=True;User ID=2020Scheduler;Password=*********"
      providerName="System.Data.SqlClient" />
    <add name="HVConnectionStringAlt" connectionString="Data Source=WEB-SQL-Test01;Initial Catalog=SA-SchedulerTest;Persist Security Info=True;User ID=2020Scheduler;Password=*********;Connect Timeout=0;"
      providerName="System.Data.SqlClient" />
    <add name="SBONet.My.MySettings.HVConnectionString" connectionString="Data Source=ASP-2020-SQL_2005;Initial Catalog=SA-SCHEDULER;Persist Security Info=True;User ID=2020Scheduler"
      providerName="System.Data.SqlClient" />
  </connectionStrings>
  <system.web>
    <httpHandlers>
      <add path="Reserved.ReportViewerWebControl.axd" verb="*" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91" validate="false" />
    </httpHandlers>
    <httpRuntime executionTimeout="300" maxRequestLength="20048" requestValidationMode="2.0" />
    <pages controlRenderingCompatibilityVersion="3.5" clientIDMode="AutoID" validateRequest="false">
      <controls>
        <add tagPrefix="asp" namespace="System.Web.UI" assembly="System.Web.Extensions, Version=3.5.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
        <add tagPrefix="asp" namespace="System.Web.UI.WebControls" assembly="System.Web.Extensions, Version=3.5.0.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add tagPrefix="igchartprop" namespace="Infragistics.UltraChart.Resources.Appearance" assembly="Infragistics.UltraChart.Resources.v5.1, Version=5.1.20051.37, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb" />
        <add tagPrefix="igchart" namespace="Infragistics.WebUI.UltraWebChart" assembly="Infragistics.WebUI.UltraWebChart.v5.1, Version=5.1.20051.37, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb" />
        <add tagPrefix="T2020" namespace="T2020.T2020Controls" />
        <add tagPrefix="rsweb" namespace="Microsoft.Reporting.WebForms" />
      </controls>
      <namespaces>
        <add namespace="T2020.T2020Controls" />
        <add namespace="System.Linq" />
        <add namespace="System.Data" />
        <add namespace="System.Data.SqlClient" />
        <add namespace="System.Data.Linq" />
        <add namespace="System.Collections.Generic" />
        <add namespace="SBONet" />
      </namespaces>
    </pages>
    <!-- TODO: Make scheduler work without Debug="true". - JRP S24 -->
    <compilation debug="true" explicit="true" strict="false" targetFramework="4.5.2">
      <assemblies>
        <clear />

        <!--<add assembly="System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add assembly="System.Configuration, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
                <add assembly="System.Web, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
                <add assembly="System.Data, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add assembly="System.Web.Services, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
                <add assembly="System.Xml, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
                <add assembly="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
                <add assembly="System.EnterpriseServices, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
                <add assembly="System.Web.Mobile, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />-->

        <add assembly="*" />
        <add assembly="System.Runtime.Serialization, Version=3.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL" />
        <add assembly="System.IdentityModel, Version=3.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL" />
        <add assembly="System.ServiceModel, Version=3.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
        <add assembly="System.ServiceModel.Web, Version=3.5.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
        <add assembly="System.WorkflowServices, Version=3.5.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />

        <!--<add assembly="System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
            <add assembly="System.DirectoryServices, Version=2.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
            <add assembly="System.DirectoryServices.Protocols, Version=2.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
            <add assembly="System.Design, Version=2.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
            <add assembly="System.ServiceProcess, Version=2.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
            <add assembly="System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
            <add assembly="System.Web.RegularExpressions, Version=2.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />-->

        <add assembly="Infragistics.UltraChart.Resources.v5.1, Version=5.1.20051.37, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb" />
        <add assembly="Infragistics.WebUI.Shared.v5.1, Version=5.1.20051.37, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb" />
        <add assembly="Infragistics.WebUI.UltraWebChart.v5.1, Version=5.1.20051.37, Culture=neutral, PublicKeyToken=7DD5C3163F2CD0CB" />
        <add assembly="System.Web, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Configuration, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Xml, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Data, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Data.Linq, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Web.Services, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.DirectoryServices, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.DirectoryServices.Protocols, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.EnterpriseServices, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Design, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.ServiceProcess, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Web.RegularExpressions, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Web.Extensions.Design, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="System.Speech, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91" />
        <add assembly="Microsoft.ReportViewer.Common, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91" />
        <add assembly="System.Web.Extensions, Version=3.5.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
      </assemblies>
      <buildProviders>
        <add extension=".rdlc" type="Microsoft.Reporting.RdlBuildProvider, Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91" />
      </buildProviders>

    </compilation>
    <!-- Change to "On" if you would like to see the new error page - JRP S24 -->
    <customErrors mode="Off" defaultRedirect="~/SBOError.aspx" redirectMode="ResponseRewrite" allowNestedErrors="true">
      <!-- This will only catch 404 errors that are handled by ASP.Net. Images etc are not handled by ASP.Net so they won't see this 404-->
      <error statusCode="404" redirect="404.aspx" />
    </customErrors>
    <sessionState cookieless="UseCookies" />
    <!-- JRP: Below is required for .asmx files -->
    <webServices>
      <protocols>
        <add name="HttpGet"/>
        <add name="HttpPost"/>
      </protocols>
    </webServices>
  </system.web>
  <system.web.extensions>
    <scripting>
      <webServices>
        <jsonSerialization maxJsonLength="50000000" />
      </webServices>
    </scripting>
  </system.web.extensions>
  <system.webServer>
    <validation validateIntegratedModeConfiguration="false" />
    <handlers>
      <remove name="WebServiceHandlerFactory-ISAPI-2.0" />
      <remove name="Telerik_Web_UI_WebResource_axd" />
      <add name="Telerik_Web_UI_WebResource_axd" path="Telerik.Web.UI.WebResource.axd" type="Telerik.Web.UI.WebResource" verb="*" preCondition="integratedMode" />
      <add name="Telerik.Web.UI.WebResource" path="Telerik.Web.UI.WebResource.axd" verb="*" type="Telerik.Web.UI.WebResource, Telerik.Web.UI" />
      <add name="ReportViewerWebControlHandler" preCondition="integratedMode" verb="*" path="Reserved.ReportViewerWebControl.axd" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91" />
    </handlers>
  </system.webServer>
  <system.codedom>
    <compilers>
      <compiler
    language="vb;visualbasic"
    extension=".vb"
    type="Microsoft.VisualBasic.VBCodeProvider, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089"
    compilerOptions="/optioninfer+"
    warningLevel="0" />
    </compilers>
  </system.codedom>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.ReportViewer.Common" publicKeyToken="89845dcd8080cc91" />
        <bindingRedirect oldVersion="8.0.0.0" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.ReportViewer.Webforms" publicKeyToken="89845dcd8080cc91" />
        <bindingRedirect oldVersion="8.0.0.0" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Infragistics.WebUI.Shared.v5.1" publicKeyToken="7dd5c3163f2cd0cb" />
        <bindingRedirect oldVersion="5.1.20051.0-5.1.20051.37" newVersion="5.1.20051.37" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Infragistics.WebUI.Design.v5.1" publicKeyToken="7dd5c3163f2cd0cb" />
        <bindingRedirect oldVersion="5.1.20051.0-5.1.20051.37" newVersion="5.1.20051.37" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Infragistics.WebUI.UltraWebCalcManager.v5.1" publicKeyToken="7dd5c3163f2cd0cb" />
        <bindingRedirect oldVersion="5.1.20051.0-5.1.20051.37" newVersion="5.1.20051.37" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Infragistics.WebUI.UltraWebGrid.v5.1" publicKeyToken="7dd5c3163f2cd0cb" />
        <bindingRedirect oldVersion="5.1.20051.0-5.1.20051.37" newVersion="5.1.20051.37" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Infragistics.WebUI.UltraWebGrid.ExcelExport.v5.1" publicKeyToken="7dd5c3163f2cd0cb" />
        <bindingRedirect oldVersion="5.1.20051.0-5.1.20051.37" newVersion="5.1.20051.37" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Infragistics.WebUI.WebCombo.v5.1" publicKeyToken="7dd5c3163f2cd0cb" />
        <bindingRedirect oldVersion="5.1.20051.0-5.1.20051.37" newVersion="5.1.20051.37" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Infragistics.WebUI.WebNavBar.v5.1" publicKeyToken="7dd5c3163f2cd0cb" />
        <bindingRedirect oldVersion="5.1.20051.0-5.1.20051.37" newVersion="5.1.20051.37" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Infragistics.WebUI.UltraWebToolbar.v5.1" publicKeyToken="7dd5c3163f2cd0cb" />
        <bindingRedirect oldVersion="5.1.20051.0-5.1.20051.37" newVersion="5.1.20051.37" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Infragistics.WebUI.UltraWebNavigator.v5.1" publicKeyToken="7dd5c3163f2cd0cb" />
        <bindingRedirect oldVersion="5.1.20051.0-5.1.20051.37" newVersion="5.1.20051.37" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Infragistics.WebUI.WebDateChooser.v5.1" publicKeyToken="7dd5c3163f2cd0cb" />
        <bindingRedirect oldVersion="5.1.20051.0-5.1.20051.37" newVersion="5.1.20051.37" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Infragistics.WebUI.WebDataInput.v5.1" publicKeyToken="7dd5c3163f2cd0cb" />
        <bindingRedirect oldVersion="5.1.20051.0-5.1.20051.37" newVersion="5.1.20051.37" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Infragistics.WebUI.UltraWebTab.v5.1" publicKeyToken="7dd5c3163f2cd0cb" />
        <bindingRedirect oldVersion="5.1.20051.0-5.1.20051.37" newVersion="5.1.20051.37" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Infragistics.WebUI.UltraWebListbar.v5.1" publicKeyToken="7dd5c3163f2cd0cb" />
        <bindingRedirect oldVersion="5.1.20051.0-5.1.20051.37" newVersion="5.1.20051.37" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Infragistics.WebUI.Misc.v5.1" publicKeyToken="7dd5c3163f2cd0cb" />
        <bindingRedirect oldVersion="5.1.20051.0-5.1.20051.37" newVersion="5.1.20051.37" />
      </dependentAssembly>
      <!--Chart Section Begin-->
      <dependentAssembly>
        <assemblyIdentity name="Infragistics.UltraChart.Resources.v5.1" publicKeyToken="7dd5c3163f2cd0cb" />
        <bindingRedirect oldVersion="5.1.20051.0-5.1.20051.37" newVersion="5.1.20051.37" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Infragistics.WebUI.UltraWebChart.v5.1" publicKeyToken="7dd5c3163f2cd0cb" />
        <bindingRedirect oldVersion="5.1.20051.0-5.1.20051.37" newVersion="5.1.20051.37" />
      </dependentAssembly>
      <!--Chart Section End-->
    </assemblyBinding>
  </runtime>
</configuration>
