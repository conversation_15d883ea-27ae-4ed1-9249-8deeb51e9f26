<%@ Page Language="VB" MasterPageFile="site.master" EnableViewState="true" Debug="true" Inherits="LateNight" CodeFile="LateNight.aspx.vb" %>
<%@ Import Namespace="System.Data" %>
<%@ Import Namespace="System.Data.SqlClient" %>
<%@ Import Namespace="System.Data.OleDb" %>
<%@ Import NameSpace="System.IO" %>
<%@ Import NameSpace="iTextSharp.text" %>
<%@ Import NameSpace="iTextSharp.text.pdf" %>
<%@ Import Namespace="SBOTypeLibrary" %>
<%@ Import Namespace="SBOWebSiteServices" %>
<%@ Register TagPrefix="obout" Namespace="Obout.ComboBox" Assembly="obout_ComboBox" %>
<%@ Register TagPrefix="obout" Namespace="Obout.Grid" Assembly="obout_Grid_NET" %>
<%@ Register TagPrefix="obout" Namespace="Obout.Interface" Assembly="obout_Interface" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="ajaxToolkit" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" Runat="Server">
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">


<script language="javascript" type="text/javascript">
    //These variables are used to make sure all user input is valid so that the 
    //please wait message will show.
     var ValStoresCheck, ValFinCodesCheck, ValDatesCheck;
     
     function exportToExcel() {
         MyDataGrid.exportToExcel();
     }

     function showAdd(){
          document.getElementById("newcsops").style.display = '';
          return false;
     }
     function hideAdd(){
          document.getElementById("newcsops").style.display = 'none';
          return false;
     }    

    function showops()
    {
        var opdiv = document.getElementById('options');
        opdiv.style.display = 'inline';
    }

    function hideops()
    {
        var opdiv = document.getElementById('options');
        opdiv.style.display = 'none';    
    }

    function selectall(tname, selall)
    {
        var gtable = document.getElementById(tname);
        var cbox;
        for (ti = 0; ti < gtable.rows.length; ti++)
        {	    
            cbox = document.getElementById(tname + "_" + ti);
            cbox.checked=selall;
        }
    }    
</script>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>Dumac Business Systems Inventory Manager</title>            
    <link rel="STYLESHEET" href="clsWendysApp.css" type="text/css" />
    <link rel="stylesheet" href="jquery-ui.css" />    
    <script src="jquery-1.8.3.js" type="text/javascript" ></script>    
    <script src="jquery-ui.js" type="text/javascript" ></script>          
    <style type="text/css" media="screen">
        #options p { cursor: move; } 
        .no-sorting
        {
            display: none !important;
        }
        .obftable {width:100%;border-style:none;border-width:0px;border-collapse:collapse;font-size:8pt;}
        .obfcell {border-style:none none solid none;border-width:1px;border-color:Black;}
    </style>
</head>         
<body class="pagebody">
	<form runat="server" id="Form1">
        <ajaxToolkit:ToolkitScriptManager ID="ToolkitScriptManager1" runat="server" />
	    <div id="updatediv" style="position:absolute;visibility:hidden;z-index:100;text-align:center;background-color:white;position:absolute;top:50%;left:50%;margin-left:-70px; margin-top: -50px;width:120px;border: 1px solid #000000;padding:20px;">
            <table><tr><td><img id="waitimage"  src="images/wait.gif" alt="Please Wait" /></td><td style="font-family: Verdana, Arial, Helvetica, sans-serif;padding-left: 10px;">Updating...&nbsp;Please&nbsp;wait.</td></tr></table>
	    </div>
	    <table class="maintable">
	        <tr><td class="maintabletitle">Late Night<br /><asp:Label ID="SubHeader" runat="server"/></td></tr>
		    <tr><td class="maintablebody">
		        <table width="100%">
		            <tr>
                        <td class="tdbuttons">
		                    <input type="button" onclick="showops();" value="Report Options" class="buttons" onmouseover="javascript:this.style.cursor = 'hand';" />
		                    <asp:Button ID="btnPDFExport" runat="server" OnClick="ExportGridToPDF" Text="Export To PDF" visible="true" class="buttons" onmouseover="javascript:this.style.cursor = 'hand';"/>
		                </td>
		            </tr>
		        </table>
		    </td></tr>	    
		    <tr>
                <td class="maintablebody">
			        <Obout:Grid id="MyDataGrid" 
                        runat="server" 
                        CallbackMode="true" 
			            Serialize="true" 
			            AutoGenerateColumns="false" 
			            OnColumnsCreated="OnColumnsCreated"
			            EnableRecordHover="false"
			            ShowLoadingMessage="true" 
			            AllowSorting="false" 
			            AllowGrouping="true" 
			            ShowColumnsFooter="true"  
			            ShowGroupFooter="true" 
			            AllowRecordSelection="false" 
			            AllowMultiRecordSelection="false"
			            OnRowDataBound="Grid1_OnRowDataBound"
			            AllowAddingRecords="false" 
			            allowfiltering="false"
			            AllowPageSizeSelection="false" 
			            AllowPaging="false" 
			            PageSize="-1" 
			            FolderStyle="styles/premiere_blue"
			            FolderExports="ChartImages" 
			            OnRowExported="Grid1_RowExported">
			            <ExportingSettings ExportColumnsFooter="true" ExportGroupFooter="true"/>
				        <Columns>				          
                            <obout:Column ID="C0" runat="server" DataField="HourName" HeaderText="Late Night" Width="80" align="right" Visible="TRUE"/>
                            <obout:Column ID="C1" runat="server" DataField="Day1" HeaderText="Day1" Wrap="true" Width="80" align="right" Visible="TRUE"/>
                            <obout:Column ID="C2" runat="server" DataField="Day2" HeaderText="Day2" Wrap="true" Width="80" align="right" Visible="TRUE"/>
                            <obout:Column ID="C3" runat="server" DataField="Day3" HeaderText="Day3" Wrap="true" Width="80" align="right" Visible="TRUE"/>
                            <obout:Column ID="C4" runat="server" DataField="Day4" HeaderText="Day4" Wrap="true" Width="80" align="right" Visible="TRUE"/>
                            <obout:Column ID="C5" runat="server" DataField="Day5" HeaderText="Day5" Wrap="true" Width="80" align="right" Visible="TRUE"/>
                            <obout:Column ID="C6" runat="server" DataField="Day6" HeaderText="Day6" Wrap="true" Width="80" align="right" Visible="TRUE"/>
                            <obout:Column ID="C7" runat="server" DataField="Day7" HeaderText="Day7" Wrap="true" Width="80" align="right" Visible="TRUE"/>                              
                            <obout:Column ID="C8" runat="server" DataField="Store" HeaderText="Store" Visible="false"/>
                            <obout:Column ID="C9" runat="server" DataField="HourSort" Visible="false"/>
                            <obout:Column ID="C10" runat="server" DataField="WeekTotal" HeaderText="Week $" Width="80" align="right" Visible="TRUE"/>
                            <obout:Column ID="C11" runat="server" DataField="WeekPct" HeaderText="Week %" Width="80" align="right" Visible="TRUE"/>
                            <obout:Column ID="C12" runat="server" DataField="LNTotal" HeaderText="Total" Width="80" align="right" Visible="FALSE"/>
				        </Columns>
			        </Obout:Grid>
		        </td>
		    </tr>
	    </table>	
        <div id="options" class="popupoptiondiv"  style="z-index:10;">
            <table id="taboptions" runat="server" class="maintable">
                <tr>
                    <td class="maintabletitle">Report Options</td>
                </tr>
                <tr>  
                    <td class="maintablebody">	
                        <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                            <ContentTemplate> 
                                <table border="0" style="font-family:verdana;font-size:9pt;">
                                    <tr>
                                        <td class="reportoptionlabel">Year:</td>
                                        <td class="reportoptioncell">                                    
                                            <asp:dropdownlist id="lstYear" runat="server" onselectedindexchanged="lstYear_Changed" autopostback="true" class="inputcontrols" style="font-size:9pt;width: 80px;"/>
                                        </td>
                                    </tr>
                                    <tr id="trWeek" runat="server">
                                        <td class="reportoptionlabel">Week:</td>
                                        <td class="reportoptioncell">     
                                            <asp:dropdownlist id="lstWeek" runat="server" class="inputcontrols" style="font-size:9pt;width: 200px;"/>
                                        </td>                             
                                    </tr>	                                       
                                </table>
                            </ContentTemplate>
                        </asp:UpdatePanel> 
                        <table border="0" style="font-family:verdana;font-size:9pt;">
                            <tr>		
                                <td colspan="2" style="padding-top: 20px;text-align: center;"><%--Mark Williams Bug 1409 (Validators)--%>
                                    <input type="button" onclick="hideops();" value="Cancel" class="buttons" onmouseover="javascript:this.style.cursor = 'hand';"/>&nbsp;                                      
                                    <asp:button id="btnApplyChanges" text="Apply Options" onclick="refresh_report" runat="server" class="buttons" onmouseover="javascript:this.style.cursor = 'hand';"/>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>	    
            </table>
        </div>                                  	
	</form>
</body>
</html>
</asp:Content> 