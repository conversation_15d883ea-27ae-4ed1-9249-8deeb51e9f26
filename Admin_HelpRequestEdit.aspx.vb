Option Explicit On

Imports AmazonUtilities.Mail.SimpleEmailService

'2021-06-07 jjc Sprint79w67 SBOD-2254 Updating email addresses:
'   helpdesk@dumac.<NAME_EMAIL>
'   Productchanges@dumac.<NAME_EMAIL>
'   sbonet@dumac.<NAME_EMAIL>
'2021-07-19 jjc Sprint 79w68 Adding CorpID to request email
'2021-12-30 jjc Sprint 79w38 Rmoving QS from emails to be sent to SBOnet support

Partial Class Admin_HelpRequestEdit
    Inherits System.Web.UI.Page

    Dim InvWSS As InventoryWebSiteServices.InventoryServices
    Dim nHelpRequestID As Integer
    Dim tTable As New DataTable
    Dim sPageAddedFrom, sSQL, sUser As String
    Dim tHeaderTable As DataTable

    Sub Page_Load(ByVal sender As Object, ByVal e As EventArgs) Handles Me.Load
        If Len(Session("UserID")) = 0 Then
            Response.Redirect("login.aspx?timeout=1")
        End If

        InvWSS = Session("IWSS")

        If Request.QueryString("HelpRequestID") = -1 Then
            trHelpRequestID.Visible = False
            trPageAddedFrom.Visible = False
            trBirthday.Visible = False
            trThumbprint.Visible = False
            trLastModified.Visible = False
            trHeader.Visible = False
            Requestor.Enabled = False
            btnDelete.Visible = False
            sPageAddedFrom = Request.QueryString("PageAddedFrom")
            sUser = InvWSS.Platform.Application.AccountIdentifier
        ElseIf Request.QueryString("HelpRequestID") > 0 Then
            nHelpRequestID = Request.QueryString("HelpRequestID")
        End If

        If Not IsPostBack Then
            PopStores()
            GetUsers(sUser)
            BindGrids()
        End If

        If Session("UserRank") < 900 And Request.QueryString("HelpRequestID") > 0 Then
            HelpRequestID.Enabled = False
            PageAddedFrom.Enabled = False
            Requestor.Enabled = False
            RequestorPhone.Enabled = False
            RequestorPhoneIsCell.Enabled = False
            EmailAddress.Enabled = False
            ContactName.Enabled = False
            ContactPhone.Enabled = False
            ContactPhoneIsCell.Enabled = False
            Subject.Enabled = False
            Detail.Enabled = False
            Birthday.Enabled = False
            Thumbprint.Enabled = False
            LastModified.Enabled = False

            trButtons.Visible = False
            trDisclaimer2.Visible = False
            trContinue.Visible = False
            'Mark Williams Bug 804
            cblStores.Enabled = False
            AllStores.Visible = False
        End If
    End Sub

    Sub BindGrids()
        If nHelpRequestID > 0 Then
            sSQL = "SELECT [THR].[RequestType], [THR].[Subject], [THR].[Detail], [THR].[PageAddedFrom], [THR].[Requestor], [THR].[RequestorPhone], [THR].[RequestorPhoneIsCell], [THR].[ContactName], [THR].[StoreID], [THR].[ContactPhone], [THR].[ContactPhoneIsCell], [TA].[EmailAddress], CONCAT([TA].[LastName], ', ', [TA].[FirstName]) [Thumbprint], [THR].[Birthday], [THR].[LastModified] FROM [dbo].[tmHelpRequest] AS [THR] LEFT JOIN [dbo].[tmAccount] AS [TA] ON [TA].[UniversalAccountIdentifier] = [THR].[Thumbprint] WHERE HelpRequestID = " & nHelpRequestID
            If InvWSS.GetData(sSQL, tTable) Then
                If tTable.Rows.Count > 0 Then
                    If tTable.Rows(0).Item("RequestType") = "QS" Then
                        radRequestType1.Checked = True
                    ElseIf tTable.Rows(0).Item("RequestType") = "PC" Then
                        radRequestType2.Checked = True
                    Else
                        radRequestType3.Checked = True
                    End If
                    HelpRequestID.Text = nHelpRequestID
                    Subject.Text = tTable.Rows(0).Item("Subject")
                    Detail.Text = tTable.Rows(0).Item("Detail")
                    PageAddedFrom.Text = tTable.Rows(0).Item("PageAddedFrom")
                    Requestor.SelectedValue = CheckForNull(tTable.Rows(0).Item("Requestor"))
                    RequestorPhone.Text = CheckForNull(tTable.Rows(0).Item("RequestorPhone"))
                    RequestorPhoneIsCell.Checked = tTable.Rows(0).Item("RequestorPhoneIsCell")
                    ContactName.Text = CheckForNull(tTable.Rows(0).Item("ContactName"))
                    cblStores.SelectedValue = CheckForNull(tTable.Rows(0).Item("StoreID"))
                    ContactPhone.Text = CheckForNull(tTable.Rows(0).Item("ContactPhone"))
                    ContactPhoneIsCell.Checked = tTable.Rows(0).Item("ContactPhoneIsCell")
                    EmailAddress.Text = CheckForNull(tTable.Rows(0).Item("EmailAddress"))
                    Thumbprint.Text = tTable.Rows(0).Item("Thumbprint")
                    Birthday.Text = tTable.Rows(0).Item("Birthday")
                    LastModified.Text = tTable.Rows(0).Item("LastModified")

                    If InvWSS.GetData(GetHeader(), tHeaderTable) AndAlso tHeaderTable.Rows.Count > 0 Then
                        lblStoreID.Text = tHeaderTable.Rows(0).Item("StoreId")
                        lblStoreNum.Text = tHeaderTable.Rows(0).Item("StoreNum")
                        lblStoreDescription.Text = tHeaderTable.Rows(0).Item("StoreDescription")
                        lblPhone.Text = tHeaderTable.Rows(0).Item("Phone")
                        lblPOSProgram.Text = tHeaderTable.Rows(0).Item("POSDescription")
                        lblPurchaseAccount.Text = tHeaderTable.Rows(0).Item("PurchaseAccount")
                        lblDoInventory.Text = tHeaderTable.Rows(0).Item("DoInventory")
                        lblDoSASExport.Text = tHeaderTable.Rows(0).Item("DoSASExport")
                        lblOwner.Text = tHeaderTable.Rows(0).Item("Owner")
                    End If

                    tHeaderTable.Dispose()
                End If
                tTable.Dispose()
            End If
        Else
            sSQL = "SELECT * FROM tmAccount WITH(NOLOCK) WHERE UniversalAccountIdentifier = '" & InvWSS.Platform.Application.AccountIdentifier & "'"
            If InvWSS.GetData(sSQL, tTable) AndAlso tTable.Rows.Count > 0 Then
                RequestorPhone.Text = CheckForNull(tTable.Rows(0).Item("PhoneNumber"))
                EmailAddress.Text = CheckForNull(tTable.Rows(0).Item("EmailAddress"))
                tTable.Dispose()
            End If
        End If
    End Sub

    Function GetHeader() As String
        Dim sStores As String = ""
        Dim sbSQL As New StringBuilder
        Dim litem As ListItem

        For Each litem In cblStores.Items
            If litem.Selected = True Then
                sStores &= IIf(Len(sStores) > 0, "," & litem.Value, litem.Value)
            End If
        Next

        If Len(sStores) > 0 Then
            sbSQL.AppendLine("SELECT")
            sbSQL.AppendLine("  STO.StoreId")
            sbSQL.AppendLine("  ,STO.StoreNum")
            sbSQL.AppendLine("  ,STO.StoreDescription")
            sbSQL.AppendLine("  ,ISNULL(STO.Phone, '') AS Phone")
            sbSQL.AppendLine("  ,ISNULL(POS.Description, '') AS POSDescription")
            sbSQL.AppendLine("  ,ISNULL(ST0INV.PurchaseAccount, '') AS PurchaseAccount")
            sbSQL.AppendLine("  ,ISNULL(ST0INV.PortalSiteNumber, '') AS CorpID")
            sbSQL.AppendLine("  ,CASE WHEN ST0INV.DoInventory = 1 THEN 'RI' ELSE 'R' END As DoInventory")
            sbSQL.AppendLine("  ,CASE WHEN ST0INV.DoSASExport = 1 THEN 'True' ELSE 'False' END As DoSASExport")
            sbSQL.AppendLine("  ,OWN.Owner")
            sbSQL.AppendLine("FROM ")
            sbSQL.AppendLine("  tmStore STO WITH(NOLOCK)")
            sbSQL.AppendLine("  LEFT OUTER JOIN tmStoreInvt ST0INV WITH(NOLOCK) ON STO.StoreID = ST0INV.StoreID")
            sbSQL.AppendLine("  LEFT OUTER JOIN tmPOSProgram POS WITH(NOLOCK) ON ST0INV.POSProgramID = POS.POSProgramID")
            sbSQL.AppendLine("  LEFT OUTER JOIN tmOwner OWN WITH(NOLOCK) ON STO.OwnerNum = OWN.OwnerNum")
            sbSQL.AppendLine("WHERE")
            sbSQL.AppendLine("  STO.StoreID IN (" & sStores & ")")
        Else
            sbSQL.AppendLine("SELECT")
            sbSQL.AppendLine("  '0' AS StoreId")
            sbSQL.AppendLine("  ,'0' AS StoreNum")
            sbSQL.AppendLine("  ,'All Stores' AS StoreDescription")
            sbSQL.AppendLine("  ,'" & RequestorPhone.Text & "' AS Phone")
            sbSQL.AppendLine("  ,'' AS POSDescription")
            sbSQL.AppendLine("  ,'' AS PurchaseAccount")
            sbSQL.AppendLine("  ,'' AS DoInventory")
            sbSQL.AppendLine("  ,'' AS DoSASExport")
            sbSQL.AppendLine("  ,'' AS Owner")
        End If

        Return sbSQL.ToString
    End Function

    Function CheckForNull(ByVal oValue As Object) As String
        Dim sReturnString As String = ""
        Try
            sReturnString = CType(oValue, System.String)
            Return sReturnString
        Catch
            Return ""
        End Try
    End Function

    Sub GetUsers(Optional ByVal sUniversalAccountIdentifier As String = "")
        Dim tUserTable As New DataTable
        Dim sbSQL As New StringBuilder
        sbSQL.AppendLine("SELECT")
        sbSQL.AppendLine("  ACCT.UniversalAccountIdentifier")
        sbSQL.AppendLine("  ,ACCT.LastName + ', ' + ACCT.FirstName + ' - ' + ROL.Description AS Requestor")
        sbSQL.AppendLine("FROM")
        sbSQL.AppendLine("  tmAccount ACCT WITH(NOLOCK)")
        sbSQL.AppendLine("  LEFT OUTER JOIN tsUserRole UROL WITH(NOLOCK) ON  ACCT.UniversalAccountIdentifier = UROL.UniversalAccountIdentifier")
        sbSQL.AppendLine("  LEFT OUTER JOIN tmRole ROL WITH(NOLOCK) ON  ROL.UniversalRoleIdentifier = UROL.UniversalRoleIdentifier")
        sbSQL.AppendLine("WHERE")
        sbSQL.AppendLine("  ActiveAccount = 1")
        If sUniversalAccountIdentifier <> "" Then
            sbSQL.AppendLine("  AND ACCT.UniversalAccountIdentifier = '" & sUniversalAccountIdentifier & "'")
        End If
        sbSQL.AppendLine("ORDER BY")
        sbSQL.AppendLine("  UserID")
        If InvWSS.GetData(sbSQL.ToString, tUserTable) Then
            Requestor.DataSource = tUserTable
            Requestor.DataTextField = "Requestor"
            Requestor.DataValueField = "UniversalAccountIdentifier"
            Requestor.DataBind()
            tUserTable.Dispose()
        End If
    End Sub

    Sub btnSave_click(ByVal sender As Object, ByVal e As EventArgs)
        Dim sbSQL As StringBuilder
        If IsNumeric(HelpRequestID.Text) AndAlso CInt(HelpRequestID.Text) > 0 Then
            sbSQL = New StringBuilder
            sbSQL.AppendLine("UPDATE")
            sbSQL.AppendLine("  tmHelpRequest")
            sbSQL.AppendLine("SET")
            sbSQL.AppendLine("  Subject = '" & Replace(Subject.Text, "'", "''") & "'")
            sbSQL.AppendLine("  ,Detail = '" & Replace(Detail.Text, "'", "''") & "'")
            sbSQL.AppendLine("  ,PageAddedFrom = '" & PageAddedFrom.Text & "'")
            sbSQL.AppendLine("  ,Requestor = '" & Requestor.SelectedValue & "'")
            sbSQL.AppendLine("  ,RequestorPhone = '" & RequestorPhone.Text & "'")
            sbSQL.AppendLine("  ,RequestorPhoneIsCell = '" & RequestorPhoneIsCell.Checked & "'")
            sbSQL.AppendLine("  ,StoreID = " & cblStores.SelectedValue)
            sbSQL.AppendLine("  ,ContactName = '" & ContactName.Text & "'")
            sbSQL.AppendLine("  ,ContactPhone = '" & ContactPhone.Text & "'")
            sbSQL.AppendLine("  ,ContactPhoneIsCell = '" & ContactPhoneIsCell.Checked & "'")
            sbSQL.AppendLine("  ,EmailAddress = '" & EmailAddress.Text & "'")
            sbSQL.AppendLine("  ,DateResolved = NULL")
            sbSQL.AppendLine("  ,Thumbprint = '" & InvWSS.Platform.Application.AccountIdentifier & "'")
            If radRequestType1.Checked Then
                sbSQL.AppendLine("  ,RequestType = 'QS'")
            ElseIf radRequestType2.Checked Then
                sbSQL.AppendLine("  ,RequestType = 'PC'")
            Else
                sbSQL.AppendLine("  ,RequestType = 'WS'")
            End If
            sbSQL.AppendLine("WHERE")
            sbSQL.AppendLine("  HelpRequestID = " & nHelpRequestID)
            InvWSS.Platform.SBODBExecNonQuerySQL("SBOCore", sbSQL.ToString)
            Response.Redirect("Admin_HelpRequests.aspx")
        Else
            sbSQL = New StringBuilder
            sbSQL.AppendLine("INSERT INTO")
            sbSQL.AppendLine("  tmHelpRequest")
            sbSQL.AppendLine("  (")
            sbSQL.AppendLine("      Subject")
            sbSQL.AppendLine("      ,Detail")
            sbSQL.AppendLine("      ,PageAddedFrom")
            sbSQL.AppendLine("      ,Requestor")
            sbSQL.AppendLine("      ,RequestorPhone")
            sbSQL.AppendLine("      ,RequestorPhoneIsCell")
            sbSQL.AppendLine("      ,StoreID")
            sbSQL.AppendLine("      ,ContactName")
            sbSQL.AppendLine("      ,ContactPhone")
            sbSQL.AppendLine("      ,ContactPhoneIsCell")
            sbSQL.AppendLine("      ,EmailAddress")
            sbSQL.AppendLine("      ,Thumbprint")
            sbSQL.AppendLine("      ,RequestType")
            sbSQL.AppendLine("  )")
            sbSQL.AppendLine("VALUES")
            sbSQL.AppendLine("  (")
            sbSQL.AppendLine("      '" & Replace(Subject.Text, "'", "''") & "'")
            sbSQL.AppendLine("      ,'" & Replace(Detail.Text, "'", "''") & "'")
            sbSQL.AppendLine("      ,'" & sPageAddedFrom & "'")
            sbSQL.AppendLine("      ,'" & InvWSS.Platform.Application.AccountIdentifier & "'")
            sbSQL.AppendLine("      ,'" & RequestorPhone.Text & "'")
            sbSQL.AppendLine("      ,'" & RequestorPhoneIsCell.Checked & "'")
            If cblStores.SelectedValue <> "" Then
                sbSQL.AppendLine("      ," & cblStores.SelectedValue)
            Else
                sbSQL.AppendLine("      ,-1")
            End If
            sbSQL.AppendLine("      ,'" & ContactName.Text & "'")
            sbSQL.AppendLine("      ,'" & ContactPhone.Text & "'")
            sbSQL.AppendLine("      ,'" & ContactPhoneIsCell.Checked & "'")
            sbSQL.AppendLine("      ,'" & EmailAddress.Text & "'")
            sbSQL.AppendLine("      ,'" & InvWSS.Platform.Application.AccountIdentifier & "'")
            If radRequestType1.Checked Then
                sbSQL.AppendLine("  ,'QS'")
            ElseIf radRequestType2.Checked Then
                sbSQL.AppendLine("  ,'PC'")
            Else
                sbSQL.AppendLine("  ,'WS'")
            End If
            sbSQL.AppendLine("  )")
            InvWSS.Platform.SBODBExecNonQuerySQL("SBOCore", sbSQL.ToString)

            If EmailAddress.Text <> "" Then
                sbSQL = New StringBuilder
                sbSQL.AppendLine("UPDATE")
                sbSQL.AppendLine("  tmAccount")
                sbSQL.AppendLine("SET")
                sbSQL.AppendLine("  EmailAddress = '" & EmailAddress.Text & "'")
                sbSQL.AppendLine("WHERE")
                sbSQL.AppendLine("  EmailAddress <> '" & EmailAddress.Text & "'")
                sbSQL.AppendLine("  AND UniversalAccountIdentifier = '" & InvWSS.Platform.Application.AccountIdentifier & "'")
                InvWSS.Platform.SBODBExecNonQuerySQL("SBOCore", sbSQL.ToString)
            End If

            If RequestorPhone.Text <> "" Then
                sbSQL = New StringBuilder
                sbSQL.AppendLine("UPDATE")
                sbSQL.AppendLine("  tmAccount")
                sbSQL.AppendLine("SET")
                sbSQL.AppendLine("  PhoneNumber = '" & RequestorPhone.Text & "'")
                sbSQL.AppendLine("WHERE")
                sbSQL.AppendLine("  PhoneNumber <> '" & RequestorPhone.Text & "'")
                sbSQL.AppendLine("  AND UniversalAccountIdentifier = '" & InvWSS.Platform.Application.AccountIdentifier & "'")
                InvWSS.Platform.SBODBExecNonQuerySQL("SBOCore", sbSQL.ToString)
            End If

            SendEmailNotice()

            trContinue.Visible = True
            trForm.Visible = False
            trButtons.Visible = False
            trDisclaimer2.Visible = False
        End If
    End Sub

    Sub PopRestaurantInfo(ByVal sender As Object, ByVal e As EventArgs)
        PopRestaurantInfo()
    End Sub

    Sub PopRestaurantInfo()
        'Mark Williams Bug 804
        sSQL = "SELECT Phone, ManagerName FROM tmStore WITH(NOLOCK) WHERE StoreID = " & cblStores.SelectedValue
        If InvWSS.GetData(sSQL, tTable) AndAlso tTable.Rows.Count > 0 Then
            ContactName.Text = CheckForNull(tTable.Rows(0).Item("ManagerName"))
            ContactPhone.Text = CheckForNull(tTable.Rows(0).Item("Phone"))
            tTable.Dispose()
        End If

    End Sub

    Sub SendEmailNotice()

        Dim sOwnerName As String = ""
        Dim sEmailBody As New StringBuilder
        Dim sSubject As String = ""
        Dim sDetail As String = ""
        Dim sEmailAddress As String = ""

        Try
            If InvWSS.GetData("SELECT TOP 1 Owner FROM tmOwner WITH(NOLOCK)", tTable) Then
                If tTable.Rows.Count > 0 Then
                    sOwnerName = Replace(tTable.Rows(0).Item("Owner"), "'", "''")
                End If
            Else
                sOwnerName = Replace(Session("OrganizationName"), "'", "''")
            End If
        Catch eName As Exception
            sOwnerName = Replace(Session("OrganizationName"), "'", "''")
        End Try

        sSubject = Replace(Subject.Text, "'", "''")
        sDetail = Replace(Detail.Text, "'", "''")



        'add Bryan's email address 2012-01-19 BT
        'changed Steve K's <NAME_EMAIL> to <EMAIL> BT 2012-07-09
        'Mark Williams Bug 1716 Web Services' on call email added for use with the new smart phones.
        If radRequestType1.Checked Then
            sSubject = "Product Changes - " & sSubject
            sEmailAddress = "<EMAIL>;<EMAIL>"
        ElseIf radRequestType2.Checked Then
            sSubject = "Quick Service - " & sSubject
            sEmailAddress = "<EMAIL>"
        Else
            sSubject = "SBOnet Support - " & sSubject
            sEmailAddress = "<EMAIL>"
        End If

        If InvWSS.GetData(GetHeader(), tHeaderTable) AndAlso tHeaderTable.Rows.Count > 0 Then
            sEmailBody.AppendLine(sOwnerName & "<br /><br />")
            sEmailBody.AppendLine("The following Restaurants are affected:<br /><br />")
            For Each row As DataRow In tHeaderTable.Rows
                sEmailBody.AppendLine("Store ID: " & row("StoreId") & " / Store Num: " & row("StoreNum") & " / " & row("StoreDescription") & " / " & CheckForNull(row("Phone")) & " / Corp Num: " & CheckForNull(row("CorpID")) & " / " & "<br />")
            Next
            sEmailBody.AppendLine("<br />")
            sEmailBody.AppendLine("Requestor: " & Requestor.SelectedItem.Text & "<br />")
            sEmailBody.AppendLine("Email Address: " & EmailAddress.Text & "<br />")
            sEmailBody.AppendLine("Subject: " & sSubject & "<br /><br />")
            If Len(sPageAddedFrom) > 0 Then
                sEmailBody.AppendLine("Page Added From: " & sPageAddedFrom & "<br /><br />")
            End If
            sEmailBody.AppendLine("Details: " & sDetail)

            'Send email w/ Amazon SES
            If Not SendMail(sEmailAddress, sSubject, sEmailBody.ToString) Then
                'Do something?
                Response.Headers.Add("X-SES-Exception", "Error sending mail")
            End If
            
            '2020-06-15 jjc Sprint 79W24 SBOD-1925 Amazon's sending has changed so that there is far less consistiency in the sening number,  making this unmanageable for her.
               'Removing code per her request.
            'If radRequestType3.Checked Then

            '    Dim prMessage As New Amazon.SimpleNotificationService.Model.PublishRequest
            '    prMessage.Message = sEmailBody.Replace("<br /><br />", vbCrLf).Replace("<br />", vbCrLf).ToString
            '    prMessage.PhoneNumber = "+13153851203"  'JF's Cell

            '    'Send text message w/ Amazon SNS
            '    Dim awsClient As New Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient
            '    awsClient.Publish(prMessage)

            '    'DAF 2016-12-25
            '    'Removed PER JF, but asked to save just incase she wants it put back
            '    'Dim sOnCallNumber As String = InvWSS.Platform.SBODBScalarGet("SBORoot", "vjCSROnCallSchedule", "CellPhoneEmail", "GetDate() BETWEEN StartDate AND EndDate")
            '    'If Len(sOnCallNumber) Then

            '    '    Dim regexObj As Regex = New Regex("[^\d]")
            '    '    sOnCallNumber = regexObj.Replace(sOnCallNumber, "")

            '    '    If Len(sOnCallNumber) = 10 Then
            '    '        sOnCallNumber = Convert.ToInt64(sOnCallNumber).ToString("##########")

            '    '        prMessage.PhoneNumber = "+1" & sOnCallNumber
            '    '        awsClient.Publish(prMessage)
            '    '    End If

            '    'End If

            '    awsClient.Dispose()

            '    'This is how to publish a message to the SBOnet topic on Amazon SNS
            '    'awsClient.Publish("arn:aws:sns:us-east-1:094631939696:SBOnet", sEmailBody.Replace("<br /><br />", vbCrLf).Replace("<br />", vbCrLf).ToString)

            'End If
        End If
    End Sub

    Sub RunCommand(ByVal SQL As String)
        InvWSS.Platform.SBODBExecNonQuerySQL("SBOCore", SQL)
    End Sub

    Sub btnCancel_click(ByVal sender As Object, ByVal e As EventArgs)
        Response.Redirect("Admin_HelpRequests.aspx")
    End Sub

    Sub btnDelete_click(ByVal sender As Object, ByVal e As EventArgs)
        sSQL = "DELETE FROM tmHelpRequest WHERE HelpRequestID = " & nHelpRequestID
        RunCommand(sSQL)
        Response.Redirect("Admin_HelpRequests.aspx")
    End Sub

    Sub btnContinue_click(ByVal sender As Object, ByVal e As EventArgs)
        Response.Redirect(sPageAddedFrom)
    End Sub

    'Mark Williams Bug 804
    Sub PopStores()

        cblStores.Items.Clear()
        Dim tabStores As New DataTable
        tabStores = InvWSS.GetPermittedStores()
        cblStores.DataSource = tabStores
        cblStores.DataTextField = "StoreDescription"
        cblStores.DataValueField = "StoreID"
        cblStores.DataBind()
        cblStores.Items(0).Selected = True
        tabStores.Dispose()

        PopRestaurantInfo()
    End Sub

End Class
