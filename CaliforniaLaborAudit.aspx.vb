﻿Option Explicit On

Imports System.IO
Imports System.Data
Imports System.Data.SqlClient
Imports SBOTypeLibrary
Imports SBOWebSiteServices
Imports InventoryWebSiteServices
Imports iTextSharp.text
Imports iTextSharp.text.pdf
Imports AmazonUtilities.Mail.SimpleEmailService
Imports WebServicesAddons.SendGridEmailService


''' <summary>
''' 2019-10-30 jjc Sprint 73 SBOD-826/1179 Added functionality to obey PayrollIdentifier and EmployeeIDMaskLength persistent values.
''' 2025-07-14 Mari SBOO-864 Switching email service to SendGrid
''' </summary>

Partial Class CaliforniaLaborAudit
    Inherits System.Web.UI.Page

    Private InvWSS As InventoryWebSiteServices.InventoryServices
    Private SelRegionID As Integer
    Private SelDistrictID As Integer
    Private SelFranchiseID As Integer
    Private app As ISBOApplication
    Private strCon As String
    Private RangeTitle As String
    Private sRange As String
    Private sSubject As String
    Friend sResponseMsg As String
    Private iCurrentPeriodID As Integer
    Private iCurrentWeekID As Integer
    Private iWeekID(10) As Integer
    Dim sStoreLabel As String = "Choose Store(s)"
    Dim sOrgName As String = ""
    Dim sOrgSQLServer As String = ""
    Dim sOrgSQLDatabase As String = ""

    Sub Page_Load(ByVal sender As Object, ByVal e As EventArgs) Handles Me.Load

        If Len(Session("UserID")) = 0 Then
            If Len(Request.QueryString("org")) Then
                SetOrgInfo(Request.QueryString("org"))
                If sOrgName = String.Empty Then
                    Response.Redirect("https://sbonet.ncrsaas.com")
                End If
                If Len(Request.QueryString("function")) Then
                    If Request.QueryString("function") = "mail" Then
                        MailRun()
                        Response.Redirect("https://sbonet.ncrsaas.com")
                        Exit Sub
                    End If
                End If
            Else
                Response.Redirect("login.aspx?timeout=1")
            End If
        Else
            If Len(Request.QueryString("org")) Then
                SetOrgInfo(Request.QueryString("org"))
            Else

                SetOrgInfo(Session("OrganizationName"))
                InvWSS = Session("IWSS")
            End If
            strCon = Replace(Session("ConnectString"), "Provider=SQLOLEDB.1;", "")
            InvWSS = Session("IWSS")
        End If


        'DAF 01/24/2011
        'disable the button after clicked so the user doesnt get click happy
        btnSubmit.Attributes.Add("onclick", "javascript: ShowProgress();" + ClientScript.GetPostBackEventReference(btnSubmit, ""))

        If Not IsPostBack Then

            txtStartDate.Text = Now.AddDays(-1).ToShortDateString
            txtEndDate.Text = Now.AddDays(-1).ToShortDateString
            PopAlerts()

            'BDean: Sprint 12 // B-02239 // TK-01595 - Populate the "Region" and "District" ddl's
            PopRegions()
            PopDistricts()

            PopStores()
            ApplyOptionPreferences()

        End If
    End Sub

    Sub MailRun()
        Dim sSQLUser As String
        sSQLUser = "SELECT"
        sSQLUser &= "   T1.UniversalAccountIdentifier"
        sSQLUser &= "   ,T1.EmailAddress"
        sSQLUser &= "   ,T3.Subject"
        sSQLUser &= "   ,T2.LastReportDate"
        sSQLUser &= " FROM"
        sSQLUser &= "   tmAccount T1 WITH(NOLOCK) "
        sSQLUser &= "   ,tdEmailMessages T2 WITH(NOLOCK) "
        sSQLUser &= "   ,tmEmailMessages T3 WITH(NOLOCK) "
        sSQLUser &= "WHERE "
        sSQLUser &= "   T1.UniversalAccountIdentifier = T2.UniversalAccountIdentifier "
        sSQLUser &= "   AND T2.EmailMessageID = T3.EmailMessageID "
        sSQLUser &= "   AND T3.Title = 'Labor Audit' "
        sSQLUser &= "   AND T1.EmailAddress IS NOT NULL "
        sSQLUser &= "   AND T1.EmailAddress <> '' "
        sSQLUser &= "   AND T1.ActiveAccount <> 0"
        If Len(Request.QueryString("Address")) Then
            sSQLUser &= "   AND T1.EmailAddress = '" & Request.QueryString("Address") & "'"
        End If

        Dim tTable As DataTable = GetDBTable(sSQLUser)
        If tTable.Rows.Count Then
            Dim sGUID As String = Guid.NewGuid.ToString
            Dim Yesterday As Date = DateAdd(DateInterval.Day, -1, CDate(Now.ToShortDateString))

            For Each row As DataRow In tTable.Rows
                'Session("AccountID") = row("UniversalAccountIdentifier")

                Dim Platform As ISBOPlatformV100
                InvWSS = New InventoryWebSiteServices.InventoryServices
                Platform = InvWSS.Login(Request.QueryString("org"), row("UniversalAccountIdentifier"), True)

                If Len(Platform.Application.UserID) > 0 Then
                    Session("AccountID") = Platform.Application.AccountIdentifier
                    Session("UserID") = Platform.Application.UserID
                    Session("PWD") = ""
                    Session("IWSS") = InvWSS
                    Session("organizationName") = Platform.Application.Organization
                    Session("ConnectString") = InvWSS.ConnectString()
                End If

                sGUID = "LaborAudit-" & row("UniversalAccountIdentifier")

                If Not IsDBNull(row("LastReportDate")) Then
                    'If the last report sent to the user was before the most current then send the most current one

                    txtStartDate.Text = Now.AddDays(-1).ToShortDateString
                    txtEndDate.Text = Now.AddDays(-1).ToShortDateString
                    PopAlerts()
                    PopRegions()
                    PopDistricts()
                    PopStores()
                    ApplyOptionPreferences()

                    Dim StoreItem As System.Web.UI.WebControls.ListItem
                    For Each StoreItem In cblStores.Items
                        StoreItem.Selected = True
                    Next
                    Grid_ExportPDF(1, sGUID)

                    Dim sEM As String = row("EmailAddress")
                    If SendEmailReport_AmazonSES(row("EmailAddress"), sGUID) = True Then
                        SetLastReport(row("UniversalAccountIdentifier"), Yesterday)
                    End If
                    Try
                        File.Delete(Server.MapPath("ChartImages/" & sGUID & ".pdf"))
                    Catch ex As Exception

                    End Try

                Else
                    'If the User has never received a scorecard then send the latest one.  This is in-case someone just checked the email option in their user profile

                    txtStartDate.Text = Now.AddDays(-1).ToShortDateString
                    txtEndDate.Text = Now.AddDays(-1).ToShortDateString
                    PopAlerts()
                    PopRegions()
                    PopDistricts()
                    PopStores()
                    ApplyOptionPreferences()

                    Dim StoreItem As System.Web.UI.WebControls.ListItem
                    For Each StoreItem In cblStores.Items
                        StoreItem.Selected = True
                    Next
                    Grid_ExportPDF(1, sGUID)

                    If SendEmailReport_AmazonSES(row("EmailAddress"), sGUID) = True Then
                        SetLastReport(row("UniversalAccountIdentifier"), Yesterday)
                    End If
                    Try
                        File.Delete(Server.MapPath("ChartImages/" & sGUID & ".pdf"))
                    Catch ex As Exception

                    End Try
                    End If
                    Session("AccountID") = Nothing
                    Session("UserID") = Nothing
                    Session("PWD") = Nothing
                    Session("IWSS") = Nothing
                    Session("organizationName") = Nothing
                    Session("ConnectString") = Nothing
            Next
        End If
    End Sub

    Sub SetLastReport(ByVal sUAI As String, ByVal dtLastScorecard As Date)
        Dim sSQL As String
        sSQL = "UPDATE tdEmailMessages SET LastReportDate = '" & dtLastScorecard.ToShortDateString & "' "
        sSQL &= "WHERE EmailMessageID = (select EmailMessageID from tmEmailMessages WITH(NOLOCK) WHERE  "
        sSQL &= "   Title = 'Labor Audit') "

        sSQL &= "AND UniversalAccountIdentifier = '" & sUAI & "'"
        RunCommand(sSQL)
    End Sub

    Sub SetOptions()
    End Sub


    Sub PopAlerts()
        cblAlerts.Items.Clear()
        cblAlerts.Items.Add(New System.Web.UI.WebControls.ListItem("Emp Break < 30 Minutes", "ShortBreakAlert"))
        cblAlerts.Items.Add(New System.Web.UI.WebControls.ListItem("Minor Worked > 2 Hours Without Break", "MinorBreakAlert"))
        cblAlerts.Items.Add(New System.Web.UI.WebControls.ListItem("Worked > 5 hours before first break", "FirstBreakAlert"))
        cblAlerts.Items.Add(New System.Web.UI.WebControls.ListItem("Worked > 8 hours without a break", "NoBreakAlert"))
        cblAlerts.Items.Add(New System.Web.UI.WebControls.ListItem("Worked > 10 hours without a second break", "TenHourAlert"))
    End Sub


    Sub PopStores()
        cblStores.Items.Clear()
        Dim row As DataRow
        Dim tStores As New DataTable
        Dim StoreItem As System.Web.UI.WebControls.ListItem

        Dim sbSQL As New StringBuilder
        sbSQL.AppendLine("SELECT StoreID, StoreDescription")
        sbSQL.AppendLine("FROM [dbo].[tmStore]")
        sbSQL.AppendLine("WHERE STOREID IN")
        sbSQL.AppendLine("	(")
        sbSQL.AppendLine("	SELECT tmStore.[StoreID]")
        sbSQL.AppendLine("	FROM [WEBRPT_vjBinPermissions] bp")
        sbSQL.AppendLine("		INNER JOIN [dbo].[tmStore]")
        sbSQL.AppendLine("			ON bp.[UniversalStoreIdentifier] = tmStore.[UniversalStoreIdentifier]")
        sbSQL.AppendLine("		INNER JOIN [dbo].[tmNode]")
        sbSQL.AppendLine("			ON tmStore.[UniversalNodeIdentifier] = tmNode.[UniversalNodeIdentifier]")
        sbSQL.AppendLine("WHERE bp.[BinLevel] = 1")
        sbSQL.AppendLine("	AND tmNode.[ActiveNode] <> 0 ")
        sbSQL.AppendLine("	AND bp.[UniversalAccountIdentifier] = '" & Session("AccountID") & "'")

        'BDean: Sprint 12 // B-02239 // TK-01595 - If a "Region" is selected --> Append the WHERE clause
        If Regions.SelectedValue <> "-1" Then
            sbSQL.AppendLine("	AND bp.[UniversalRegionIdentifier] = ( SELECT [UniversalRegionIdentifier] FROM [dbo].[tmRegion] WITH(NOLOCK) WHERE [RegionNum] = " & Regions.SelectedValue & " )")
        End If

        'BDean: Sprint 12 // B-02239 // TK-01595 - If a "District" is selected --> Append the WHERE clause
        If Districts.SelectedValue <> "-1" Then
            sbSQL.AppendLine("	AND bp.[UniversalDistrictIdentifier] = ( SELECT [UniversalDistrictIdentifier] FROM [dbo].[tmDistrict] WITH(NOLOCK) WHERE [DistrictNum] = " & Districts.SelectedValue & " )")
        End If

        sbSQL.AppendLine("	)")

        InvWSS.GetData(sbSQL.ToString, tStores)

        If Not tStores Is Nothing Then
            For Each row In tStores.Rows
                StoreItem = New System.Web.UI.WebControls.ListItem(row("StoreDescription"))
                StoreItem.Value = row("StoreID")
                cblStores.Items.Add(StoreItem)
            Next row
            row = Nothing
            tStores.Dispose()
            StoreItem = Nothing
        End If


    End Sub


    Sub btnSubmit_click(ByVal sender As Object, ByVal e As EventArgs)
        Session("saro-export-excel") = "false"
        GoSubmit()
    End Sub

    Sub ExcelButtonClick(ByVal sender As Object, ByVal e As EventArgs)
        GoSubmit()
        Dim sServerPath As String = Me.Server.MapPath("ChartImages")
        Dim sFileName As String = grdAudit.ExportToExcel("Test", True, False, False, True)

        Dim objSourceFileInfo As System.IO.FileInfo = New System.IO.FileInfo(sServerPath & "/" & sFileName)

        With Response
            .ContentType = "application/octet-stream"
            .AddHeader("Content-Disposition", "attachment; filename=" & sFileName)
            .AddHeader("Content-Length", objSourceFileInfo.Length.ToString)
            .WriteFile(objSourceFileInfo.FullName)
            .Flush()
            .End()
        End With
    End Sub

    Sub Button_ExportPDF(ByVal sender As Object, ByVal e As EventArgs)
        Grid_ExportPDF(0)
    End Sub
    Function Grid_ExportPDF(ByVal Target As Integer, Optional ByVal FileGUID As String = "") As Boolean
        GoSubmit()
        'The grid
        Dim grid As Obout.Grid.Grid = grdAudit

        'Stream which will be used to render the data
        Dim fileStream As MemoryStream = New MemoryStream()
        Dim fStream As FileStream
        Dim bSuccess As Boolean = False

        If Target = 0 Then
            fileStream = New MemoryStream()
        Else
            fStream = New FileStream(Server.MapPath("ChartImages/" & FileGUID & ".pdf"), FileMode.OpenOrCreate)
        End If

        'Create Document class object and set its size to letter and give space left, right, Top, Bottom Margin
        Dim doc As Document = New Document(iTextSharp.text.PageSize.LETTER, 10, 10, 42, 35)
        'Set the document to be landscape
        doc.SetPageSize(iTextSharp.text.PageSize.LETTER.Rotate())
        Try

            Dim wri As PdfWriter
            If Target = 0 Then
                wri = PdfWriter.GetInstance(doc, fileStream)
            Else
                wri = PdfWriter.GetInstance(doc, fStream)
            End If
            'Open Document to write
            doc.Open()

            'Create the PDF title
            Dim pdfTitle As Paragraph = New Paragraph("Labor Audit Report")
            pdfTitle.Alignment = Element.ALIGN_CENTER
            pdfTitle.SpacingAfter = 20

            'Add the PDF title to the document
            doc.Add(pdfTitle)

            Dim font8 As Font = FontFactory.GetFont("Arial", 9)

            'Count the number of visible columns in the grid
            Dim iVisibleColCount As Integer = 0
            For Each col As Obout.Grid.Column In grid.Columns
                If col.Visible = True AndAlso col.HeaderText <> "" Then
                    iVisibleColCount += 1
                End If
            Next

            'Create instance of the pdf table and set the number of column in that table
            Dim PdfTable As PdfPTable = New PdfPTable(iVisibleColCount)
            Dim PdfPCell As PdfPCell = Nothing

            'Add headers to the pdf table
            For Each col As Obout.Grid.Column In grid.Columns

                If col.Visible = True AndAlso col.HeaderText <> "" Then
                    PdfPCell = New PdfPCell(New Phrase(New Chunk(col.HeaderText, font8)))
                    'Vertically align the text
                    PdfPCell.VerticalAlignment = Element.ALIGN_MIDDLE

                    PdfTable.AddCell(PdfPCell)
                End If

            Next

            'How add the data from the Grid to pdf table
            For i As Integer = 0 To grid.Rows.Count - 1

                Dim dataItem As Hashtable = grid.Rows(i).ToHashtable()
                Dim cellData As String

                For Each col As Obout.Grid.Column In grid.Columns

                    If col.Visible = True AndAlso col.HeaderText <> "" Then

                        'String formatting for dates and decimals
                        If col.DataField = "BUSINESSDATE" Then
                            Dim bDate = dataItem(col.DataField).ToString()
                            Dim convertedDate As Date
                            'Use tryparse to avoid exceptions
                            Date.TryParse(bDate, result:=convertedDate)
                            cellData = convertedDate.Date
                        ElseIf col.DataField = "TOTWORKED" Or col.DataField = "TotalDayHours" Then
                            Dim dItem = dataItem(col.DataField).ToString()
                            Dim unroundedDecimal As Decimal
                            'Use tryparse to avoid exceptions
                            Decimal.TryParse(dItem, result:=unroundedDecimal)
                            Dim roundedDecimal As Decimal = Math.Round(unroundedDecimal, 2)
                            cellData = roundedDecimal.ToString()
                        Else
                            cellData = dataItem(col.DataField).ToString()
                        End If

                        PdfPCell = New PdfPCell(New Phrase(New Chunk(cellData, font8)))
                        'Vertically align the text
                        PdfPCell.VerticalAlignment = Element.ALIGN_MIDDLE
                        PdfTable.AddCell(PdfPCell)

                    End If

                Next

            Next i

            doc.Add(PdfTable)

        Catch docEx As DocumentException

            'Handle pdf document exception if any  

        Catch ioEx As IOException

            'Handle IO exception 

        Catch ex As Exception

            'Handle other exception if occurs  

        Finally

            'Close document and writer
            doc.Close()

        End Try

        'Send the data and the appropriate headers to the browser
        If Target = 0 Then
            Dim sExportFileName As String = sOrgName & "-LaborAudit.pdf" 'Add WeekEndDate later
            Response.Clear()
            Response.AddHeader("content-disposition", "attachment;filename=" & sExportFileName)
            Response.ContentType = "application/pdf"
            Response.BinaryWrite(fileStream.ToArray())
            Response.End()
        Else

        End If
        bSuccess = True
        Return bSuccess
    End Function


    Sub GoSubmit()
        
        grdAudit.DataSource = Nothing
        grdAudit.DataBind()

        SetUserPreferences()
        Dim bShortBreakAlert As Boolean
        Dim bMinorBreakAlert As Boolean
        Dim bFirstBreakAlert As Boolean
        Dim bNoBreakAlert As Boolean
        Dim bTenHourAlert As Boolean


        For Each liAlert As System.Web.UI.WebControls.ListItem In cblAlerts.Items
            Select Case liAlert.Value
                Case "ShortBreakAlert"
                    bShortBreakAlert = liAlert.Selected
                Case "MinorBreakAlert"
                    bMinorBreakAlert = liAlert.Selected
                Case "FirstBreakAlert"
                    bFirstBreakAlert = liAlert.Selected
                Case "NoBreakAlert"
                    bNoBreakAlert = liAlert.Selected
                Case "TenHourAlert"
                    bTenHourAlert = liAlert.Selected
            End Select
        Next

        Try
            Dim sbSQL As New StringBuilder
            Dim tabResults As New DataTable
            Dim dStartDate As Date
            Dim dEndDate As Date

            Date.TryParse(txtStartDate.Text, dStartDate)
            Date.TryParse(txtEndDate.Text, dEndDate)

            With sbSQL
                .AppendLine("DECLARE @StoreList StoreList")
                .AppendLine("")
                .AppendLine("DECLARE @StartDate DATE")
                .AppendLine("DECLARE @EndDate DATE")
                .AppendLine("")
                .AppendLine("DECLARE @ShortBreakAlert BIT")
                .AppendLine("DECLARE @MinorBreakAlert BIT")
                .AppendLine("DECLARE @FirstBreakAlert BIT")
                .AppendLine("DECLARE @NoBreakAlert BIT")
                .AppendLine("DECLARE @TenHourAlert BIT")
                .AppendLine("")
                For Each liStore As System.Web.UI.WebControls.ListItem In cblStores.Items
                    If liStore.Selected Then
                        .Append("INSERT @StoreList ([StoreID]) ")
                        .AppendLine("VALUES(" & liStore.Value & ")")
                    End If
                Next
                '.AppendLine("INSERT @StoreList ([StoreID])")
                '.AppendLine("SELECT [StoreID]")
                '.AppendLine("FROM [dbo].[tmStore]")
                '.AppendLine("WHERE [StoreID] = 44")
                .AppendLine("")
                .AppendLine("")
                If Not dStartDate = Date.MinValue Then
                    .AppendLine("SET @StartDate = '" & dStartDate.ToString("yyyy-MM-dd") & "'")
                Else
                    .AppendLine("SET @StartDate = '" & Date.Now.AddDays(-1).ToString("yyyy-MM-dd") & "'")
                End If
                If Not dStartDate = Date.MinValue Then
                    .AppendLine("SET @EndDate = '" & dEndDate.ToString("yyyy-MM-dd") & "'")
                Else
                    .AppendLine("SET @EndDate = '" & Date.Now.AddDays(-1).ToString("yyyy-MM-dd") & "'")
                End If
                .AppendLine("")
                .AppendLine("SET @ShortBreakAlert = " & IIf(bShortBreakAlert, "1", "0") & " --> 30 Minutes")
                .AppendLine("SET @MinorBreakAlert = " & IIf(bMinorBreakAlert, "1", "0") & " --If under 18 shift over 2 hours")
                .AppendLine("SET @FirstBreakAlert = " & IIf(bFirstBreakAlert, "1", "0") & " --over 5 hours for first break")
                .AppendLine("SET @NoBreakAlert = " & IIf(bNoBreakAlert, "1", "0") & " --Shift Over 8 Hours")
                .AppendLine("SET @TenHourAlert = " & IIf(bTenHourAlert, "1", "0") & " --Working over 10 Hours reqires 2 breaks")
                .AppendLine("")
                .AppendLine("SELECT")
                .AppendLine("	 [BUSINESSDATE]")
                .AppendLine("	,[StoreNum]")
                .AppendLine("	,[EmployeeIdentifier]")
                .AppendLine("	,[EMP_NAME]")
                .AppendLine("	,[JOBCODE]")
                .AppendLine("	,[AlertType]")
                .AppendLine("	,[CLOCKIN]")
                .AppendLine("	,[CLOCKOUT]")
                .AppendLine("	,[TOTWORKED]")
                .AppendLine("	,[TotalDayHours]")
                .AppendLine("	,[BreakMins]")
                .AppendLine("	,[STOREID]")
                .AppendLine("FROM [dbo].[pf_rpt_CaliforniaLaborAudit]")
                .AppendLine("	(@StoreList")
                .AppendLine("	,@StartDate")
                .AppendLine("	,@EndDate")
                .AppendLine("	,@ShortBreakAlert")
                .AppendLine("	,@MinorBreakAlert")
                .AppendLine("	,@FirstBreakAlert")
                .AppendLine("	,@NoBreakAlert")
                .AppendLine("	,@TenHourAlert")
                .AppendLine("	)")
                .AppendLine("ORDER BY")
                .AppendLine("	 [StoreNum]")
                .AppendLine("	,[BUSINESSDATE]")
                .AppendLine("	,[EmployeeIdentifier]")

                'BDean: Sprint 2 // D-01060 // TK-01059 - Updated to sort the table by "CLOCKIN" time, and removed "AlertType" as it is no longer needed
                .AppendLine("   ,LTRIM(RTRIM(REPLACE([EMP_NAME], '*', '')))")
                .AppendLine("   ,[CLOCKIN]")
                '.AppendLine("	,[AlertType]")

            End With

            InvWSS.GetData(sbSQL.ToString(), tabResults)

            If tabResults.Rows.Count > 0 Then

                Dim dvAudit As DataView = tabResults.DefaultView

                grdAudit.DataSource = dvAudit
                grdAudit.DataBind()

                tabResults.Dispose()

            End If

        Catch

            'Do stuff...

        End Try
    End Sub


    Protected Function FindControlRecursive(ctlToSearch As Control, sControlID As String) As WebControl
        If (ctlToSearch.ID = sControlID) Then
            Return ctlToSearch
        End If

        For Each ctlToCheck As Control In ctlToSearch.Controls
            Dim ctlFoundIt As WebControl = FindControlRecursive(ctlToCheck, sControlID)

            If (ctlFoundIt IsNot Nothing) Then
                Return ctlFoundIt
            End If
        Next
        Return Nothing
    End Function


    Private Sub ApplyOptionPreferences()
        Dim sSQL As String = "SELECT * FROM tdUserPreference WITH(NOLOCK) WHERE UniversalAccountIdentifier = '" & Session("AccountID") & "' AND PageName = 'CaliforniaLaborAudit'"

        Dim tabUserPrefs As New DataTable
        InvWSS.GetData(sSQL, tabUserPrefs)
        If Not IsNothing(tabUserPrefs) AndAlso tabUserPrefs.Rows.Count > 0 Then
            For Each row As DataRow In tabUserPrefs.Rows
                If row("ObjectType").ToLower = "cbl" Then

                    Dim cblGeneric As CheckBoxList = CType(FindControlRecursive(Me, row("ObjectName")), WebControl)
                    For Each item As WebControls.ListItem In cblGeneric.Items
                        If item.Value = row("SelectedValue") Then
                            item.Selected = CBool(row("Value"))
                            Exit For
                        End If
                    Next
                    cblGeneric.Dispose()
                ElseIf row("ObjectType").ToLower = "chk" Then
                    CType(FindControlRecursive(Me, row("ObjectName")), CheckBox).Checked = CBool(row("Value"))
                End If
            Next
        Else
            For Each item As WebControls.ListItem In cblAlerts.Items
                item.Selected = True
            Next

            SetUserPreferences()
        End If
    End Sub


    Private Sub SetUserPreferences()
        Dim cmdUserPreferences As New SqlCommand
        Try

            With cmdUserPreferences
                .CommandText = "ps_dm_UserPreferenceUpdateInsert"
                .CommandType = CommandType.StoredProcedure
                .Parameters.Add("@UniversalAccountIdentifier", SqlDbType.VarChar)
                .Parameters.Add("@PageName", SqlDbType.VarChar)
                .Parameters.Add("@ObjectName", SqlDbType.VarChar)
                .Parameters.Add("@ObjectType", SqlDbType.VarChar)
                .Parameters.Add("@SelectedValue", SqlDbType.VarChar)
                .Parameters.Add("@Value", SqlDbType.VarChar)
                .Parameters.Item("@UniversalAccountIdentifier").Value = InvWSS.Platform.Application.AccountIdentifier
                .Parameters.Item("@PageName").Value = "CaliforniaLaborAudit"
                For Each item As WebControls.ListItem In cblAlerts.Items

                    .Parameters.Item("@ObjectName").Value = cblAlerts.ID
                    .Parameters.Item("@ObjectType").Value = "cbl"
                    .Parameters.Item("@SelectedValue").Value = item.Value
                    .Parameters.Item("@Value").Value = item.Selected

                    InvWSS.Platform.SBODBExecNonQuerySQLCommand("SBOCore", cmdUserPreferences)
                Next
            End With
        Catch ex As Exception
            Throw ex
        End Try
    End Sub

    Sub SetOrgInfo(ByVal sOrganization As String)
        Dim SQL As String = "SELECT Server, LocalDatabaseName, tbOrganization.Organization FROM tbOrganizationDatabase WITH(NOLOCK) INNER JOIN tbOrganization WITH(NOLOCK) ON tbOrganizationDatabase.OrganizationNumber = tbOrganization.OrganizationNumber "
        SQL &= "WHERE tbOrganization.Organization = '" & sOrganization & "'"
        Dim OrgDBDir As DataTable = GetDBTable(SQL, "SQL-CLU01", "SBORoot")
        For Each row As DataRow In OrgDBDir.Rows
            sOrgSQLServer = row("Server")
            sOrgSQLDatabase = row("LocalDatabaseName")

            Dim tabOwner As DataTable = GetDBTable("SELECT TOP 1 [Owner] FROM [tmOwner]", sOrgSQLServer, sOrgSQLDatabase)
            For Each drOwner As DataRow In tabOwner.Rows
                sOrgName = drOwner("Owner")
            Next
            tabOwner.Dispose()
        Next
    End Sub

    Function GetDBTable(ByVal sQuery As String, Optional ByVal sSQLServer As String = "", Optional ByVal sSQLDatabase As String = "") As DataTable
        Dim SQLCon As New SqlConnection
        Dim SQLCmd As New SqlDataAdapter
        Dim tTable As New DataTable
        If sSQLServer = String.Empty Then
            sSQLServer = sOrgSQLServer
        End If
        If sSQLDatabase = String.Empty Then
            sSQLDatabase = sOrgSQLDatabase
        End If
        Dim DBConString As String = BuildDBConnectString(sSQLServer, sSQLDatabase, "sboapplication", "SoftwareA43A")
        Dim bResult As Boolean = False
        Try
            SQLCon = New SqlConnection(DBConString)
            SQLCon.Open()

            SQLCmd = New SqlDataAdapter(sQuery, SQLCon)
            SQLCmd.SelectCommand.CommandTimeout = 720
            SQLCmd.Fill(tTable)
            SQLCon.Close()
            bResult = True
        Catch eGetDataTable As Exception
            'sSQLErr &= sSQLServer & ":" & sSQLDatabase & " - " & eGetDataTable.Message & "<p>"
        Finally
            If Not SQLCmd Is Nothing Then
                SQLCmd.Dispose()
            End If
            If Not SQLCon Is Nothing Then
                SQLCon.Dispose()
            End If
        End Try
        Return tTable
    End Function

    Public Function BuildDBConnectString(Optional ByVal SQLServer As String = "", Optional ByVal SQLDatabase As String = "", Optional ByVal SQLUserID As String = "", Optional ByVal SQLPassword As String = "", Optional ByVal PersistSecurityInfo As Boolean = False) As String
        Dim sConnect As String = ""
        If Len(SQLServer) Then
            sConnect = sConnect & "Data Source=" & SQLServer & ";"
        End If
        If Len(SQLDatabase) Then
            sConnect = sConnect & "Database=" & SQLDatabase & ";"
        End If
        If Len(SQLUserID) Then
            sConnect = sConnect & "User ID=" & SQLUserID & ";"
        End If
        If Len(SQLPassword) Then
            sConnect = sConnect & "Password=" & SQLPassword & ";"
        End If
        If PersistSecurityInfo = True Then
            sConnect = sConnect & "Persist Security Info=True;"
        End If
        Return sConnect
    End Function
    Function SendEmailReport_AmazonSES(ByVal sAddress As String, ByVal sFileGUID As String) As Boolean
        Dim bResult As Boolean = False
        Dim fStream As FileStream
        Dim sMessage As String = "This is an automated message from SBOnet." & vbCrLf & vbCrLf
        Dim sSubject As String = "Labor Audit Report"
        Dim sEmailAddress As String = sAddress
        Dim sFileName As String = sOrgName & "-LaborAudit.pdf"

        fStream = New FileStream(Server.MapPath("ChartImages/" & sFileGUID & ".pdf"), FileMode.Open)
        'WebServicesAddons.AmazonSES.SendMail(sAddress, sSubject, sMessage, fStream,,, sFileName)
        WebServicesAddons.SendGridEmailService.SendMail(sEmailAddress, sSubject, sMessage, fStream, sFileName)
        fStream.Close()
        bResult = True
        Try
            fStream.Close()
        Catch eFileClose As Exception
        End Try

        Try
            File.Delete("ChartImages/" & sFileGUID & ".pdf")
        Catch eFileDelete As Exception
        End Try


        Return bResult

    End Function
    Function SendEmailReport(ByVal sAddress As String, ByVal sFileGUID As String) As Boolean

        Dim sSQL As String
        Dim bResult As Boolean = False
        Dim msReport As MemoryStream
        Dim EmailStream As FileStream
        Dim sMessageGUID As String = Guid.NewGuid.ToString
        Dim sMessage As String = "This is an automated message from SBOnet." & vbCrLf & vbCrLf
        Dim sSubject As String = "Labor Audit Report"
        Dim bFirstPass As Boolean = True
        Dim sEmailAddress As String = sAddress
        Dim sFileName As String = sOrgName & "-LaborAudit"

        EmailStream = New FileStream(Server.MapPath("ChartImages/" & sFileGUID & ".pdf"), FileMode.Open)

        msReport = New MemoryStream
        CopyData(EmailStream, msReport)

        If EMAddAttachment(sMessageGUID.ToUpper, sFileName & ".pdf", msReport) = True Then
            sSQL = "INSERT INTO Message "
            sSQL += "("
            sSQL += "[AllRecipients], "
            sSQL += "[Author], "
            sSQL += "[DTRcvd], "
            sSQL += "[DTSent], "
            sSQL += "[RecordDate], "
            sSQL += "[HasAttachments], "
            sSQL += "[MsgHeader], "
            sSQL += "[Note], "
            sSQL += "[ParentFolder], "
            sSQL += "[Subject], "
            sSQL += "[Viewed], "
            sSQL += "[ReplyTo], "
            sSQL += "[IsPackage], "
            sSQL += "[PackageStatus], "
            sSQL += "[POP3Account], "
            sSQL += "[POPMsgID], "
            sSQL += "[dekaolc], "
            sSQL += "[GUID], "
            sSQL += "[FromAlias], "
            sSQL += "[HTML] "
            sSQL += ")"
            sSQL += "VALUES "
            sSQL += "("
            sSQL += "'" & sEmailAddress & "', "
            sSQL += "'<EMAIL>', "
            sSQL += "'" & Now() & "', "
            sSQL += "'" & Now() & "', "
            sSQL += "'" & Now() & "', "
            sSQL += "1, "
            sSQL += "'', "
            sSQL += "'" & sMessage & "', "
            sSQL += "1, "
            sSQL += "'" & sSubject & "', "
            sSQL += "0, "
            sSQL += "'<EMAIL>', "
            sSQL += "0, "
            sSQL += "0, "
            sSQL += "'<EMAIL>', "
            sSQL += "'" & Guid.NewGuid.ToString & "<EMAIL>', "
            sSQL += "0, "
            sSQL += "'" & sMessageGUID.ToUpper & "', "
            sSQL += "'', "
            sSQL += "''"
            sSQL += ")"

            Dim SQLConEM As New SqlConnection("data source=SQL-CLU01;initial catalog=eManager_App4;Password=Reporter001;persist security info=False;user id=sa;workstation id=ASP-WSS-1;packet size=4096")
            SQLConEM.Open()
            Dim cmdSQL As New SqlCommand(sSQL, SQLConEM)
            With cmdSQL
                .CommandTimeout = 0
                .ExecuteNonQuery()
                .Dispose()
            End With
            SQLConEM.Close()
            SQLConEM.Dispose()
            bResult = True
        End If

        Try
            EmailStream.Close()
        Catch eFileClose As Exception
        End Try

        Try
            File.Delete("ChartImages/" & sFileGUID & ".pdf")
        Catch eFileDelete As Exception
        End Try


        Return bResult
    End Function

    Function EMAddAttachment(ByVal MsgGUID As String, ByVal sFileName As String, ByVal AttachmentStream As MemoryStream) As Boolean
        Dim bResponse As Boolean = False
        Dim oDB As New SqlConnection("data source=SQL-CLU01;initial catalog=eManager_App4;Password=Reporter001;persist security info=False;user id=sa;workstation id=ASP-WSS-1;packet size=4096")
        Try

            oDB.Open()

            Dim oCmd As SqlCommand = New SqlCommand("INSERT INTO Attachment (Contents, FileName, GUID, MessageGUID) VALUES (@Contents, @FileName, @GUID, @MessageGUID)", oDB)
            oCmd.Parameters.Add(New SqlParameter("@Contents", SqlDbType.Image, AttachmentStream.Length))
            oCmd.Parameters.Add(New SqlParameter("@FileName", SqlDbType.NVarChar, 255))
            oCmd.Parameters.Add(New SqlParameter("@GUID", SqlDbType.NVarChar, 50))
            oCmd.Parameters.Add(New SqlParameter("@MessageGUID", SqlDbType.NVarChar, 50))

            oCmd.Parameters("@Contents").Value = AttachmentStream.ToArray()
            oCmd.Parameters("@FileName").Value = sFileName
            oCmd.Parameters("@GUID").Value = Guid.NewGuid.ToString.Replace("-", "")
            oCmd.Parameters("@MessageGUID").Value = MsgGUID

            oCmd.ExecuteNonQuery()
            oCmd.Dispose()
            bResponse = True
        Catch eAddAttachment As Exception
            bResponse = False
            'Response.Write(eAddAttachment.ToString)
        Finally
            oDB.Close()
        End Try
        Return bResponse
    End Function

    Sub RunCommand(ByVal SQL As String)
        InvWSS.Platform.SBODBExecNonQuerySQL("SBOCore", SQL)
    End Sub

    Private Sub CopyData(ByRef FromStream As Stream, ByRef ToStream As Stream)
        Try
            Dim nBytesRead As Integer
            Dim m_Size As Integer = 4096
            Dim bytes(m_Size) As Byte
            FromStream.Position = 0
            ToStream.Position = 0
            If FromStream.Length < m_Size Then m_Size = FromStream.Length
            nBytesRead = FromStream.Read(bytes, 0, m_Size)
            While nBytesRead > 0
                ToStream.Write(bytes, 0, nBytesRead)
                If FromStream.Length - FromStream.Position < m_Size Then m_Size = FromStream.Length - FromStream.Position
                nBytesRead = FromStream.Read(bytes, 0, m_Size)
            End While
        Catch eCopyData As Exception
            Throw eCopyData
        End Try
    End Sub

    'BDean: Sprint 12 // B-02239 // TK-01595 - Added code required to populate "Region" and "District" ddl's, and handle selection changes by the user ( 4 Subs Below )
    Sub PopRegions()
        Regions.Items.Clear()
        Dim oRegion As DataRow
        Dim RegionItem As System.Web.UI.WebControls.ListItem
        Dim tRegions As DataTable

        tRegions = InvWSS.GetPermittedRegions()

        'If only one row is returned --> Hide the ddl
        'Else --> Add an "All Regions" option
        If tRegions.Rows.Count < 2 Then
            rowReportRegion.Visible = False
        Else
            RegionItem = New System.Web.UI.WebControls.ListItem("All Regions")
            RegionItem.Value = -1
            Regions.Items.Add(RegionItem)
        End If

        For Each oRegion In tRegions.Rows
            RegionItem = New System.Web.UI.WebControls.ListItem(oRegion("Region"))
            RegionItem.Value = oRegion("RegionNum")
            Regions.Items.Add(RegionItem)
        Next oRegion
        oRegion = Nothing
        tRegions.Dispose()
        RegionItem = Nothing
    End Sub

    Sub PopDistricts()
        Districts.Items.Clear()
        Dim row As DataRow
        Dim tDistricts As New DataTable
        Dim DistrictItem As System.Web.UI.WebControls.ListItem

        If Not Regions.SelectedItem Is Nothing Then

            tDistricts = InvWSS.GetPermittedDistricts(Regions.SelectedItem.Value)

            'If only one row is returned --> Hide the ddl
            'Else --> Add an "All Districts" option
            If tDistricts.Rows.Count < 2 Then
                rowReportDistrict.Visible = False
            Else
                DistrictItem = New System.Web.UI.WebControls.ListItem("All Districts")
                DistrictItem.Value = -1
                Districts.Items.Add(DistrictItem)
            End If

            For Each row In tDistricts.Rows
                DistrictItem = New System.Web.UI.WebControls.ListItem(row("District"))
                DistrictItem.Value = row("DistrictNum")
                Districts.Items.Add(DistrictItem)
            Next row
        End If
        row = Nothing
        DistrictItem = Nothing
        tDistricts.Dispose()
    End Sub

    Sub RegionChange(ByVal sender As Object, ByVal e As EventArgs)
        PopDistricts()
        PopStores()
        tbShowOps.Text = "True"
    End Sub

    Sub DistrictChange(ByVal sender As Object, ByVal e As EventArgs)
        PopStores()
        tbShowOps.Text = "True"
    End Sub

End Class
