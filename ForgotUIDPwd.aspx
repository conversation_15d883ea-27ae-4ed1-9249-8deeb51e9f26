<%@ Page Language="VB" Debug="true" %>
<%@ Import Namespace="System.Data" %>
<%@ Import Namespace="System.Data.SqlClient" %>
<%@ Import Namespace="SBOTypeLibrary" %>
<%@ Import Namespace="SBOWebSiteServices" %>
<%@ Import Namespace="System.Security.Cryptography" %>
<%@ Import Namespace="System.IO" %>
<%@ Import Namespace="AmazonUtilities.Mail.SimpleEmailService" %>
<%@ Import Namespace="WebServicesAddons" %>

<script language="VB" runat="server">    
    '2021-06-07 jjc Sprint79w67 SBOD-2254 Updating <NAME_EMAIL> to <EMAIL>
    '   HelpDesk@dumac.<NAME_EMAIL>
    '   sbonet@dumac.<NAME_EMAIL>
    '   SOnCall@dumac.<NAME_EMAIL>
    '   Alerts-SBOnet@dumac.<NAME_EMAIL>
    '   Webservices@dumac.<NAME_EMAIL>


    Dim InvWSS As New InventoryWebSiteServices.InventoryServices

    Sub Page_Load(ByVal sender As Object, ByVal e As EventArgs)
        If Not IsPostBack Then
            ReplyInputs.Visible = False
            Dim sInstructions As String = "<p><b><u>Forgot your SBOnet password?</u></b></p><p>- Enter your current SBOnet userid and click [Submit].&nbsp;&nbsp;"
            sInstructions &= "A temporary password will be sent to the e-mail address which is set for this SBOnet account, "
            sInstructions &= "The e-mail will also provide steps to setup a new password. </p>"
            sInstructions &= "<p>- If the SBOnet userid cannot be found, or there is no email address assigned, "
            sInstructions &= "you will be instructed to contact the WebServices team.</p>"

            Dim sInstructions2 As String
            sInstructions2 = "<p><b><u>Forgot your SBOnet UserID?</u></b></p><p>"
            sInstructions2 &= "- Enter your e-mail address and click [Submit]. If the e-mail address matches the one on file, "
            sInstructions2 &= "the SBOnet userid will be sent to this e-mail address. </p>"
            sInstructions2 &= "<p>- If the e-mail address cannot be found, you will be instructed to contact the WebServices team.</p>"

            Dim sInstructions3 As String = "<b><u>Please note:</u></b> Any requests which involve the reactivation of an inactive user account will "
            sInstructions3 &= "require that the user to speak with a member of the WebServices team directly.<br>&nbsp;<br>"
            sInstructions3 &= "Requests for Staff Scheduler access must be handled by directly contacting the WebServices team.<br>&nbsp;<br>"
            lblInstructions.Text = sInstructions
            lblInstructions2.Text = sInstructions2
            lblInstructions3.Text = sInstructions3
        End If
    End Sub

    'save    
    Sub btnSave_click(ByVal sender As Object, ByVal e As EventArgs)
        Dim sSQL As String
        Dim tRootTable As New DataTable
        Dim tOrgTable As New DataTable
        Dim rowRoot As DataRow
        Dim rowOrg As DataRow
        Dim sEmailSubject As String = "Your SBONet Account"
        Dim sEmailMessage As String

        ' Hide the CAPTCHA error when the "Submit" button is clicked ( clean up from previous attempts )
        lblCaptchaInstructions.Visible = False

        ' If the user did not enter a CAPTCHA code, or the code they entered does not match the code stored in the session --> Display error and do nothing
        If Len(txtCaptcha.Text) = 0 Or String.Compare(txtCaptcha.Text, Session("captcha-code"), False) <> 0 Then

            lblCaptchaInstructions.Visible = True
            txtCaptcha.Text = ""

            Exit Sub

        End If

        ' Clear out the CAPTCHA code textbox ( the validation has passed, and the form is going to reset regardless of what the user does next, which will change the CAPTCHA )
        txtCaptcha.Text = ""

        If Len(txtUserID.Text) Then
            Dim sqlCmd As SqlCommand

            'Search tbOrganizationDatabase for UserID
            'sSQL = "DECLARE @UserID NVARCHAR(30) "
            sSQL = "SELECT UO.UserID, UO.PassWord, OD.Server, OD.LocalDatabaseName FROM tbUserOrganization UO "
            sSQL &= "LEFT OUTER JOIN tbOrganizationDatabase OD ON UO.OrganizationNumber = OD.OrganizationNumber "
            sSQL &= "WHERE UO.UserID = @UserID"
            sqlCmd = New SqlCommand(sSQL)
            sqlCmd.CommandType = CommandType.Text
            With sqlCmd
                .Parameters.AddWithValue("@UserID", txtUserID.Text)
            End With

            tRootTable = GetDBTable(sqlCmd, "SQL-CLU01", "SBORoot")
            If tRootTable.Rows.Count Then
                For Each rowRoot In tRootTable.Rows     'what if there is more than one row returned?
                    'Query Org DB tmAccount for UserID - Get EmailAddress
                    sqlCmd = New SqlCommand

                    'sSQL = "DECLARE @UserID NVARCHAR(30) "
                    sSQL = "SELECT EmailAddress, UniversalAccountIdentifier, ActiveAccount FROM tmAccount WHERE UserID = @UserID"
                    sqlCmd = New SqlCommand(sSQL)
                    sqlCmd.CommandType = CommandType.Text
                    With sqlCmd
                        .Parameters.AddWithValue("@UserID", txtUserID.Text)
                    End With

                    tOrgTable = GetDBTable(sqlCmd, rowRoot("Server"), rowRoot("LocalDatabaseName"))
                    If tOrgTable.Rows.Count Then
                        For Each rowOrg In tOrgTable.Rows
                            'Is Active Account?
                            If Not IsDBNull(rowOrg("ActiveAccount")) Then
                                If rowOrg("ActiveAccount") <> 0 Then
                                Else
                                    'Is Not Active
                                    lblInstructions.Text = "The account you are attempting to access has been marked as ""Inactive"". "
                                    lblInstructions.Text &= "Please enter the information below and click [Submit], so a WebServices "
                                    lblInstructions.Text &= "team member may assist you:"
                                    'turn on data entry
                                    lblInstructions2.Visible = False
                                    lblInstructions3.Visible = False
                                    UserInputs.Visible = False
                                    btnSave.Visible = True
                                    ReplyInputs.Visible = True
                                    btnCan.Text = "Cancel"
                                    txtUserID.Text = ""
                                    txtEmailAddress.Text = ""
                                    Exit Sub

                                End If
                            Else
                                lblInstructions.Text = "The account you are attempting to access has been marked as ""Inactive"". "
                                lblInstructions.Text &= "Please enter the information below and click [Submit], so a WebServices "
                                lblInstructions.Text &= "team member may assist you:"
                                lblInstructions2.Visible = False
                                lblInstructions3.Visible = False
                                'turn on data entry
                                UserInputs.Visible = False
                                btnSave.Visible = True
                                ReplyInputs.Visible = True
                                btnCan.Text = "Cancel"
                                txtUserID.Text = ""
                                txtEmailAddress.Text = ""
                                Exit Sub
                            End If
                            'what if there is more than one row returned?  This should not happen.
                            'because the Admin_UserProfile page prevents duplicate userids across all db's.
                            'First check to see if there is an email address
                            If Not IsDBNull(rowOrg("EmailAddress")) Then
                                Dim sEmailAddress As String = rowOrg("EmailAddress")
                                If sEmailAddress = String.Empty Then
                                    lblInstructions.Text = "There is no email address associated with the UserID you entered.  Please contact WebServices for further instructions."
                                    lblInstructions2.Visible = False
                                    lblInstructions3.Visible = False
                                    UserInputs.Visible = False
                                    btnSave.Visible = True
                                    ReplyInputs.Visible = True
                                    btnCan.Text = "Cancel"
                                    txtUserID.Text = ""
                                    txtEmailAddress.Text = ""
                                    Exit Sub
                                End If
                            Else 'no matching userid
                                lblInstructions.Text = "No e-mail address matches the one you have provided. Please enter the information below and click (Submit), so WebServices team member may assist you."
                                lblInstructions2.Visible = False
                                lblInstructions3.Visible = False
                                UserInputs.Visible = False
                                btnSave.Visible = True
                                ReplyInputs.Visible = True
                                btnCan.Text = "Cancel"
                                txtUserID.Text = ""
                                txtEmailAddress.Text = ""

                                Exit Sub
                            End If
                            'Create new random password (8 characters) and set in tmAccount

                            Dim NewPassword As String = RandomPassword(8)     'returns a random alphanumeric string that is 8 characters in length
                            Dim EncryptedPassword As String = StringHashN(NewPassword, 20)   'encrypts the password and returns a 20 character string


                            'Update tmAccount with new password (sets password as the encrypted password)
                            Dim DBConString As String = BuildDBConnectString(rowRoot("Server"), rowRoot("LocalDatabaseName"), "sboapplication", "SoftwareA43A")
                            Dim sSQLUpdate As String = "UPDATE tmAccount SET Password = '" & EncryptedPassword & "' WHERE UniversalAccountIdentifier = '" & rowOrg("UniversalAccountIdentifier") & "'"
                            RunCommand(sSQLUpdate, DBConString)

                            'Update tbUserOrganization in SBORoot with new password - (sets password as the encrypted password)
                            DBConString = BuildDBConnectString("SQL-CLU01", "SBORoot", "sboapplication", "SoftwareA43A")
                            sSQLUpdate = "UPDATE tbUserOrganization SET PassWord = '" & EncryptedPassword & "' WHERE UserID = '" & txtUserID.Text & "'"
                            RunCommand(sSQLUpdate, DBConString)

                            sEmailMessage = "Here is a temporary password for the SBOnet account you are attempting to access: " & vbCrLf
                            sEmailMessage &= NewPassword & vbCrLf
                            sEmailMessage &= "Please logon onto SBOnet and confirm that you have access to the correct information. "
                            sEmailMessage &= "Contact your supervisor or the WebServices team to update your password, if you do not "
                            sEmailMessage &= "have permission to do so yourself."

                            SendEmailNotice(rowOrg("EmailAddress"), sEmailMessage, sEmailSubject)
                            lblInstructions.Text = "Your new SBONet password has been sent to " & rowOrg("EmailAddress")
                            UserInputs.Visible = False
                            btnSave.Visible = False
                            btnCan.Text = "Login"

                        Next
                    Else
                        'No User Found
                        lblInstructions.Text = "The account you are attempting to access has been marked as ""Inactive"".  Please "
                        lblInstructions.Text &= "enter the information below and click [Submit], so a WebServices team member may assist you:"
                        UserInputs.Visible = False
                        btnSave.Visible = True
                        btnCan.Text = "Cancel"
                        ReplyInputs.Visible = True
                        txtUserID.Text = ""
                        txtEmailAddress.Text = ""
                    End If
                Next
            Else
                'No User Found
                lblInstructions.Text = "The account you are attempting to access has been marked as ""Inactive"".  Please "
                lblInstructions.Text &= "enter the information below and click (Submit), so a WebServices team member may assist you:"
                UserInputs.Visible = False
                btnSave.Visible = True
                btnCan.Text = "Cancel"
                ReplyInputs.Visible = True
                txtUserID.Text = ""
                txtEmailAddress.Text = ""
            End If

        ElseIf Len(txtEmailAddress.Text) Then
            sSQL = "SELECT LocalDatabaseName, Server FROM tbOrganizationDatabase WHERE LocalDatabaseName LIKE 'SBONet%'"
            'Search all dbs for Email Address
            tRootTable = GetDBTable(sSQL, "SQL-CLU01", "SBORoot")
            Dim FoundIt As Boolean = False
            Dim sUserID As String = ""

            For Each rowRoot In tRootTable.Rows
                Dim sqlCmd As SqlCommand

                sSQL = "SELECT UserID, EmailAddress FROM tmAccount WHERE EmailAddress = @EmailAddress"
                sqlCmd = New SqlCommand(sSQL)
                sqlCmd.CommandType = CommandType.Text
                sqlCmd.CommandText = sSQL
                With sqlCmd
                    .Parameters.AddWithValue("@EmailAddress", txtEmailAddress.Text)
                End With

                tOrgTable = GetDBTable(sSQL, rowRoot("Server"), rowRoot("LocalDatabaseName"))
                If tOrgTable.Rows.Count Then
                    For Each rowOrg In tOrgTable.Rows
                        sUserID = rowOrg("UserID")
                        FoundIt = True
                        Exit For
                    Next
                End If
                If FoundIt Then
                    Exit For
                End If
            Next
            If FoundIt Then
                'Send Email
                sEmailMessage = "Here is userid for the SBOnet account you are attempting to access: " & vbCrLf
                sEmailMessage &= sUserID & vbCrLf
                sEmailMessage &= "Please logon onto SBOnet and confirm that you have access to the correct information. "
                sEmailMessage &= "Contact the WebServices team if you are still not able to access SBOnet."

                SendEmailNotice(txtEmailAddress.Text, sEmailMessage, sEmailSubject)
                lblInstructions.Text = "Your UserID has been sent to your Email Address"
            Else
                'No Email Found
                lblInstructions.Text = "No e-mail address matches the one you have provided.  Please enter the information below and click (Submit), so a WebServices team member may assist you:"
                UserInputs.Visible = False
                btnSave.Visible = True
                btnCan.Text = "Cancel"
                ReplyInputs.Visible = True
                txtUserID.Text = ""
                txtEmailAddress.Text = ""
            End If
        ElseIf ReplyInputs.Visible Then
            'send email to WebServices
            If txtName.Text <> String.Empty And txtCompany.Text <> String.Empty And txtBusinessPhone.Text <> String.Empty And txtEmail.Text <> String.Empty And txtPhone.Text <> String.Empty Then
                sEmailMessage = "Login Help Requested - " & vbCrLf
                sEmailMessage &= "Name: " & txtName.Text & vbCrLf
                sEmailMessage &= "Company: " & txtCompany.Text & vbCrLf
                sEmailMessage &= "Business Phone: " & txtBusinessPhone.Text & vbCrLf
                sEmailMessage &= "E-Mail Address: " & txtEmail.Text & vbCrLf
                sEmailMessage &= "Contact Phone: " & txtPhone.Text & vbCrLf
                SendEmailNotice("<EMAIL>", sEmailMessage, "Login Help Requested")

                Dim sEmailAddress As String = "<EMAIL>;<EMAIL>;"

                Dim sOnCallNumber As String = ""
                If Not IsNothing(InvWSS.Platform) Then
                    sOnCallNumber = InvWSS.Platform.SBODBScalarGet("SBORoot", "vjCSROnCallSchedule", "CellPhoneEmail", "GetDate() BETWEEN StartDate AND EndDate")
                End If


                If Len(sOnCallNumber) Then
                    sEmailAddress &= sOnCallNumber & ";"
                End If

                SendEmailNotice(sEmailAddress, sEmailMessage, "Login Help Requested")

                lblInstructions.Text = "Your request has been sent."
                UserInputs.Visible = False
                ReplyInputs.Visible = False
                btnSave.Visible = False
                btnCan.Text = "Login"
            Else
                lblInstructions.Text = "All fields are required."
            End If
        End If
    End Sub

    Function RandomPassword(ByVal cb As Integer) As String
        Randomize()
        Dim rgch As String
        Dim RandomString As String = ""
        rgch = "abcdefghijklmnopqrstuvwxyz"
        rgch = rgch & UCase(rgch) & "0123456789"

        Dim i As Long
        For i = 1 To cb
            RandomString = RandomString & Mid$(rgch, Int(Rnd() * Len(rgch) + 1), 1)
        Next
        Return RandomString
    End Function

    'cancel
    Sub btnCancel_click(ByVal sender As Object, ByVal e As EventArgs)
        Response.Redirect("https://sbonet.ncrsaas.com")
    End Sub

    Function GetDBTable(ByVal sSQL As String, ByVal sSQLServer As String, ByVal sSQLDatabase As String) As DataTable
        Dim SQLCon As New SqlConnection
        Dim SQLCmd As New SqlDataAdapter
        Dim tTable As New DataTable
        Dim DBConString As String = BuildDBConnectString(sSQLServer, sSQLDatabase, "sboapplication", "SoftwareA43A")
        Dim bResult As Boolean = False
        Try
            SQLCon = New SqlConnection(DBConString)
            SQLCon.Open()

            SQLCmd = New SqlDataAdapter(sSQL, SQLCon)
            SQLCmd.Fill(tTable)
            SQLCon.Close()
            bResult = True
        Catch eGetDataTable As Exception
            'sSQLErr &= sSQLServer & ":" & sSQLDatabase & " - " & eGetDataTable.Message & "<p>"
        Finally
            If Not SQLCmd Is Nothing Then
                SQLCmd.Dispose()
            End If
            If Not SQLCon Is Nothing Then
                SQLCon.Dispose()
            End If
        End Try
        Return tTable
    End Function

    Function GetDBTable(ByVal objCmd As SqlCommand, ByVal sSQLServer As String, ByVal sSQLDatabase As String) As DataTable
        Dim SQLCon As New SqlConnection
        Dim SQLCmd As New SqlDataAdapter
        Dim tTable As New DataTable
        Dim DBConString As String = BuildDBConnectString(sSQLServer, sSQLDatabase, "sboapplication", "SoftwareA43A")
        Dim bResult As Boolean = False
        Try
            SQLCon = New SqlConnection(DBConString)
            SQLCon.Open()

            SQLCmd = New SqlDataAdapter(objCmd)
            objCmd.Connection = SQLCon
            SQLCmd.Fill(tTable)
            SQLCon.Close()
            bResult = True
        Catch eGetDataTable As Exception
            'sSQLErr &= sSQLServer & ":" & sSQLDatabase & " - " & eGetDataTable.Message & "<p>"
        Finally
            If Not SQLCmd Is Nothing Then
                SQLCmd.Dispose()
            End If
            If Not SQLCon Is Nothing Then
                SQLCon.Dispose()
            End If
        End Try
        Return tTable
    End Function

    Public Function BuildDBConnectString(Optional ByVal SQLServer As String = "", Optional ByVal SQLDatabase As String = "", Optional ByVal SQLUserID As String = "", Optional ByVal SQLPassword As String = "", Optional ByVal PersistSecurityInfo As Boolean = False) As String
        Dim sConnect As String = ""
        If Len(SQLServer) Then
            sConnect = sConnect & "Data Source=" & SQLServer & ";"
        End If
        If Len(SQLDatabase) Then
            sConnect = sConnect & "Database=" & SQLDatabase & ";"
        End If
        If Len(SQLUserID) Then
            sConnect = sConnect & "User ID=" & SQLUserID & ";"
        End If
        If Len(SQLPassword) Then
            sConnect = sConnect & "Password=" & SQLPassword & ";"
        End If
        If PersistSecurityInfo = True Then
            sConnect = sConnect & "Persist Security Info=True;"
        End If
        Return sConnect
    End Function

    Public Sub SendEmailNotice(ByVal sEmailAddress As String, ByVal sMessage As String, ByVal sSubject As String)
        ' Try SendGrid first, fallback to Amazon SES if not available
        Try
            Response.Write("<script>console.log('ForgotPassword: Attempting to send email');</script>")

            ' Try SendGrid first
            Try
                Dim success As Boolean = WebServicesAddons.SendGridEmailService.SendMail(sEmailAddress, sSubject, sMessage)
                If success Then
                    Response.Write("<script>console.log('ForgotPassword: Email sent via SendGrid');</script>")
                    Exit Sub
                End If
            Catch sgEx As Exception
                Response.Write("<script>console.warn('ForgotPassword: SendGrid not available, trying fallback');</script>")
            End Try

            ' Fallback to Amazon SES
            Try
                AmazonUtilities.Mail.SimpleEmailService.SendMail(sEmailAddress, sSubject, sMessage)
                Response.Write("<script>console.log('ForgotPassword: Email sent via Amazon SES');</script>")
            Catch sesEx As Exception
                Response.Write("<script>console.error('ForgotPassword: All email methods failed');</script>")
            End Try

        Catch ex As Exception
            Response.Write("<script>console.error('ForgotPassword: General exception occurred');</script>")
        End Try
        'Dim sSQL As String
        'sSQL = "INSERT INTO Message "
        'sSQL += "("
        'sSQL += "[AllRecipients], "
        'sSQL += "[Author], "
        'sSQL += "[DTRcvd], "
        'sSQL += "[DTSent], "
        'sSQL += "[RecordDate], "
        'sSQL += "[HasAttachments], "
        'sSQL += "[MsgHeader], "
        'sSQL += "[Note], "
        'sSQL += "[ParentFolder], "
        'sSQL += "[Subject], "
        'sSQL += "[Viewed], "
        'sSQL += "[ReplyTo], "
        'sSQL += "[IsPackage], "
        'sSQL += "[PackageStatus], "
        'sSQL += "[POP3Account], "
        'sSQL += "[POPMsgID], "
        'sSQL += "[dekaolc], "
        'sSQL += "[GUID], "
        'sSQL += "[FromAlias], "
        'sSQL += "[HTML] "
        'sSQL += ")"
        'sSQL += "VALUES "
        'sSQL += "("
        'sSQL += "'" & sEmailAddress & "', "
        'sSQL += "'<EMAIL>', "
        'sSQL += "'" & Now() & "', "
        'sSQL += "'" & Now() & "', "
        'sSQL += "'" & Now() & "', "
        'sSQL += "0, "
        'sSQL += "'', "
        'sSQL += "'" & sMessage & "', "
        'sSQL += "1, "
        'sSQL += "'" & sSubject & "', "
        'sSQL += "0, "
        'sSQL += "'<EMAIL>', "
        'sSQL += "0, "
        'sSQL += "0, "
        'sSQL += "'<EMAIL>', "
        'sSQL += "'" & Guid.NewGuid().ToString & "<EMAIL>', "
        'sSQL += "0, "
        'sSQL += "NewID(), "
        'sSQL += "'', "
        'sSQL += "''"
        'sSQL += ")"

        ''Response.Write(sSQL)
        'RunCommand(sSQL, "data source=SQL-CLU01;initial catalog=eManager_App4;Password=Reporter001;persist security info=False;user id=sa;workstation id=ASP-WSS-1;packet size=4096")
    End Sub

    Sub RunCommand(ByVal SQL As String, ByVal sConnection As String)
        Try

            Dim dbConnect As New SqlConnection(sConnection)
            Dim cmdSQL As SqlCommand
            dbConnect.Open()
            cmdSQL = New SqlCommand(SQL, dbConnect)
            With cmdSQL
                .CommandTimeout = 0
                .ExecuteNonQuery()
                .Dispose()
            End With
            dbConnect.Close()
            dbConnect.Dispose()
        Catch ex As Exception

        End Try
    End Sub

    Sub RunCommand(ByVal objCmd As SqlCommand, ByVal sConnection As String)
        Try

            Dim dbConnect As New SqlConnection(sConnection)
            Dim cmdSQL As SqlCommand
            dbConnect.Open()
            'cmdSQL = New SqlCommand(Sql, dbConnect)
            objCmd.Connection = dbConnect
            With objCmd
                .CommandTimeout = 0
                .ExecuteNonQuery()
                .Dispose()
            End With
            dbConnect.Close()
            dbConnect.Dispose()
        Catch ex As Exception

        End Try
    End Sub

    Public Shared Function StringHash(ByVal vsText As String) As String
        Dim objSHA1 As SHA1CryptoServiceProvider
        Dim ySource As Byte()
        Dim yHash As Byte()

        Try
            objSHA1 = New SHA1CryptoServiceProvider
            ySource = System.Text.Encoding.UTF8.GetBytes(vsText)
            yHash = objSHA1.ComputeHash(ySource)
            objSHA1.Clear()
            Return Convert.ToBase64String(yHash)
        Catch X As Exception
            Throw X
        End Try
    End Function

    Public Shared Function StringHashN(ByVal vsText As String, ByVal vnMaxLength As Integer) As String
        ' Description :
        ' Pass        : Nothing
        Dim sRet As String = ""

        Try
            sRet = StringHash(vsText)
            If sRet.Length > vnMaxLength Then
                sRet = sRet.Substring(0, vnMaxLength)
            End If
        Catch X As Exception

            Throw X

        End Try
        Return sRet
    End Function
</script>
<html>
	<head>
		<title>Dumac Business Systems Inventory Manager</title>            
		<link rel="stylesheet" href="clsBarMenu.css" type="text/css" />
		<link rel="stylesheet" href="clsWendysApp.css" type="text/css" />   
		<link rel="stylesheet" href="clsListBuddy.css" type="text/css" />
	</head>
	<body class="pagebody">
		<form runat="server" id="Form1">
        <table style="height: 100%; width: 100%;" border="0">
            <tr>
                <td valign="middle" align="center" style="text-align: center;">
                    <center>
                        <table class="maintable" width="500px">
                            <tr>
                                <td style="text-align: center; padding: 10px;" colspan="2"><img src="images/sbonetr.jpg" border="0" alt="Dumac Business Systems Inventory Manager" /></td>
                            </tr>				
				            <tr class="rowtitle"><td class="TDmajor">SBONet Login Help</td></tr>
				            <tr>
				                <td align="center">
                                    <table width="400px" cellspacing="0px" cellpadding="10px" style="border-collapse:collapse;">
                                        <tr>
                                            <td style="border-style:solid;border-width:2px;border-color:Black;">
                                                <asp:Label ID="lblInstructions" runat="server" width="400px" text="" style="font-size:9pt;"/>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="border-style:solid;border-width:2px;border-color:Black;">
                                                <asp:Label ID="lblInstructions2" runat="server" width="400px" text="" style="font-size:9pt;"/>
                                            </td>
                                        </tr>
                                    </table>
                                    <br />
                                    <table width="400px">
                                        <tr>
                                            <td>
                                                <asp:Label ID="lblInstructions3" runat="server" width="400px" text="" style="font-size:9pt;"/>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>				
                            <tr id="UserInputs" runat="server">
                                <td class="TDmajor">
                                    <table>
                                        <tr>
                                            <td class="ReportOptionLabel">User ID:</td>	
                                            <td><asp:textbox runat="server" id="txtUserID" cssclass="inputcontrols"/></td>
                                        </tr>
                                        <tr>
                                            <td class="ReportOptionLabel">Email Address:</td>	
                                            <td><asp:textbox runat="server" id="txtEmailAddress" cssclass="inputcontrols"/></td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr id="ReplyInputs" runat="server">
                                <td class="TDmajor">
                                    <table>
                                        <tr>
                                            <td class="ReportOptionLabel">Name:</td>	
                                            <td><asp:textbox runat="server" id="txtName" cssclass="inputcontrols"/></td>
                                        </tr>
                                        <tr>
                                            <td class="ReportOptionLabel">Company you work for:</td>
                                            <td><asp:textbox runat="server" id="txtCompany" cssclass="inputcontrols"/></td>
                                        </tr>
                                        <tr>
                                            <td class="ReportOptionLabel">Business Phone Number:</td>
                                            <td><asp:textbox runat="server" id="txtBusinessPhone" cssclass="inputcontrols"/></td>
                                        </tr>
                                        <tr>
                                            <td class="ReportOptionLabel">E-Mail Address:</td>
                                            <td><asp:textbox runat="server" id="txtEmail" cssclass="inputcontrols"/></td></tr>
                                        <tr>
                                            <td class="ReportOptionLabel">Phone to contact you:</td>
                                            <td><asp:textbox runat="server" id="txtPhone" cssclass="inputcontrols"/></td>
                                        </tr>
                                    </table>
				                    <table width="400px" style="font-size:9pt;">
				                        <tr>
				                            <td>Requests may require approval from the company you noted before they are completed.</td>
				                        </tr>
				                    </table>
				                </td>
				            </tr>
                            <tr>
                                <td class="TDmajor">
                                    <table>
                                        <tr>
                                            <td class="ReportOptionLabel">Validation Code:</td>
                                            <td><asp:TextBox ID="txtCaptcha" runat="server" cssclass="inputcontrols" /></td>                                
                                        </tr>
                                        <tr>
                                            <td colspan="2">
                                                <div class="captcha-container">
                                                    <img src="Captcha.aspx" />
                                                    <asp:Label ID="lblCaptchaInstructions" runat="server" text="Incorrect Validation Code entered, please try again." Visible="false" />
                                                </div>                                                
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
				            <tr>
				                <td class="tdbuttons">
						            <asp:Button id="btnSave" runat="server" Text="Submit" ToolTip="Submit" cssclass="buttons" onclick="btnSave_click"/>
                                    <asp:Button id="btnCan" runat="server" Text="Cancel" ToolTip="Cancel" cssclass="buttons" onclick="btnCancel_click"/>
				                </td>
				            </tr>
			            </table>
			        </center> 
	            </td>
            </tr>
        </table>
		</form>  
	</body>
</html>