﻿Imports System.Web.Services
Imports System.Web.Services.Protocols
Imports System.Web.Caching
Imports System.Collections.Generic
Imports System.ComponentModel
Imports SBONet.Model.SchedEdit
Imports SBONet.Model.SchedEdit.Dtos
Imports SBONet.Model.Support
Imports SBONet.Model.Support.Dtos
Imports System.Data
Imports System.Data.SqlClient
Imports SBONet.Data.Hypervision
Imports System.Text

'2019-02-12 jjc Sprint 61 SBOD-348/379 Added MaxReportingPeriodDate() Function

Namespace Service

    ' To allow this Web Service to be called from script, using ASP.NET AJAX, uncomment the following line.
    <System.Web.Script.Services.ScriptService()> _
    <System.Web.Services.WebService(Namespace:="http://Service.DataService")> _
        <System.Web.Services.WebServiceBinding(ConformsTo:=WsiProfiles.BasicProfile1_1)> _
        <ToolboxItem(False)> _
    Public Class DataService
        Inherits System.Web.Services.WebService


#Region "Schedule editor info data"
        <WebMethod(EnableSession:=True)> _
        Public Function GetBargraphs(ByVal scheduleID As Integer) As IList(Of BarGraphDataDTO)

            Try
                Dim sched = Me.Context.Cache.CachedSchedule(scheduleID)

                Dim weeklyForecast = New WeeklyForecast(sched)

                Dim guides = New WeeklyGuideHours(weeklyForecast, sched.CachedStore)

                Dim bgdata = sched.OrderedBargraphs()
                Dim output = New List(Of BarGraphDataDTO)

                For i = 0 To bgdata.Count() - 1
                    output.Add(bgdata(i).GetBargraphDTO(weeklyForecast, guides))
                Next

                Return output
            Catch ex As Exception
                Throw New ApplicationException(Me.GetErrorMessage(ex))
            End Try

        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function GetShifts(ByVal bargraphID As Integer) As IList(Of ShiftDTO)
            Try
                Dim bargraph = Me.Context.Cache.CachedBargraph(bargraphID)
                Return bargraph.GetShiftsDTO()
            Catch ex As Exception
                Throw New ApplicationException(Me.GetErrorMessage(ex))
            End Try
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function SaveBarGraph(ByVal bargraphID As Integer, ByVal shifts As ShiftDTO()) As Boolean

            Try
                Me.Session.CheckBargraphAccess(bargraphID)
                Dim results = New ScheduleRepo().SaveBargraph(bargraphID, shifts)
                Dim bargraph = Me.Context.Cache.CachedBargraph(bargraphID)
                Me.Context.Cache.RefreshBargraph(bargraph.Schedule_ID.GetValueOrDefault(), bargraph.BarGraph_ID)
                Return results
            Catch ex As Exception
                Throw New ApplicationException(Me.GetErrorMessage(ex))
            End Try
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function GetScheduleData(ByVal scheduleID As Integer) As ScheduleDataDTO
            Try
                Dim schedule = Me.Context.Cache.CachedSchedule(scheduleID)

                Return schedule.GetScheduleDataDTO()
            Catch ex As Exception
                Throw New ApplicationException(Me.GetErrorMessage(ex))
            End Try
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function GetEmployeePicks(ByVal positionID As Integer, ByVal storeID As String) As IList(Of EmployeePickDTO)

            Try
                Me.Session.CheckPositionAccess(positionID, storeID)

                Return Me.Context.Cache.CachedStore(storeID).GetEmployeePicksDTO(positionID)

            Catch ex As Exception
                Throw New ApplicationException(Me.GetErrorMessage(ex))
            End Try
        End Function

#End Region

#Region "Miscellaneous functions"

        <WebMethod(EnableSession:=True)>
        Public Function GenerateSchedule(ByVal startDate As String, ByVal schedName As String) As IList(Of Model.Support.Dtos.ScheduleListItemDTO)


            Dim repo = New SupportRepo()
            Dim schedRepo As New ScheduleRepo()
            Dim startd = CDate(startDate)
            Dim store = Me.Context.Cache.CachedStore(Me.Session.LoggedInUser().DefaultStore)

            LogDSSInfo("DSS GenerateSchedule date=" & startDate & " store=" & store.Store_ID)

            Dim schedBuilder = New Model.ScheduleBuilder(startd, schedName, store)
            Me.Context.Cache.RefreshCachedStore(store.Store_ID)

            Dim bUseManagerSchedule As Boolean = Session.InvWSS.Platform.PersistentValue("DSS_Settings", "UseManagerSchedule")

            schedBuilder.AutoFillBargraphs(bUseManagerSchedule)

            LogDSSInfo("DSS GenerateSchedule DataPersistSchedule")
            schedBuilder.DataPersistSchedule()

            Dim Forecasts = repo.GetForecastsForWeek(CDate(startDate), store, SupportRepo.ForecastGenerationModel.PreviousWeeks)
            Dim weekForcast As Decimal = 0
            For Each Day As ForecastDayDTO In Forecasts

                weekForcast += Day.Intervals.Sum(Function(I) I.CurrForecast)
            Next Day

            For Each Day As ForecastDayDTO In Forecasts
                Dim result = Day.Intervals.Sum(Function(I) I.CurrForecast)
                Dim breakfast = Day.Intervals.Where(Function(I) store.HasBreakfast AndAlso I.HalfHourIndex < store.LunchStartIndexed(Day.BDate.DayOfWeek) AndAlso I.HalfHourIndex >= store.OpenBusinessIndexed(Day.BDate.DayOfWeek)).Sum(Function(I) I.CurrForecast)
                Dim bg = schedBuilder.ScheduleInst.Bargraphs.Where(Function(B) B.Business_Date.Value = Day.BDate).First()
                Dim bSuccess As Boolean = InsertNonServiceAmounts(store.Store_ID, bg.BarGraph_ID, result, breakfast, 0, bg.Business_Date)
                bSuccess = InsertNonServiceAmounts(store.Store_ID, bg.BarGraph_ID, weekForcast, 0, 1, bg.Business_Date)
                If result = 0.0 Then

                    schedRepo.FinalizeClosedBargraph(bg.BarGraph_ID, Me.Session.LoggedInUser())

                End If

            Next Day

            LogDSSInfo("DSS GenerateSchedule Complete")
            Return Me.GetActiveScheduleList()
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function GetSchedPrintoutData(ByVal scheduleID As Integer) As SchedPrintoutDTO

            Try
                Dim sched = Me.Context.Cache.CachedSchedule(scheduleID)

                Return sched.GetSchedPrintoutDTO()
            Catch ex As Exception
                Throw New ApplicationException(Me.GetErrorMessage(ex))
            End Try
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function GetStoreList() As IList(Of Model.Support.Dtos.StoreListItemDTO)
            Try
                Dim user = Me.Context.Session.LoggedInUser()

                Dim stores = New SupportRepo().GetStoreList(user, user.SystemAdmin)

                ' ensure the store is in the cache
                Me.Context.Cache.CachedStore(user.DefaultStore)
                Return stores
            Catch ex As Exception
                Throw New ApplicationException(Me.GetErrorMessage(ex))
            End Try
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function RefreshSession() As Boolean

            Me.Session.LoggedInUser()
            Return True

        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function SetEmployeeSsnSin(ByVal employeeID As Integer, ByVal newSsnSin As String) As String

            Try
                ' check whether user can access 
                Dim user = Me.Session.LoggedInUser()
                Me.Session.CheckEmployeeAccess(employeeID, user.DefaultStore)   ' will throw exception if user cannot access

                Dim repo = New SupportRepo()
                repo.ChangeEmployeeSsnSin(employeeID, newSsnSin)

                Me.Context.Cache.RefreshStoreEmployees(user.DefaultStore)

                If (Me.Application.HasDumacFunctions()) Then
                    Return newSsnSin.AnonymizedSSN()
                Else
                    Return newSsnSin
                End If
            Catch ex As Exception
                Throw New ApplicationException("Could not change employee ssn/sin: " + ex.Message, ex)
            End Try

        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function GetEmployeeActualHrsForDate(ByVal storeID As String, ByVal businessDate As String) As Model.Support.Dtos.DailyEmpoyeeHrsDTO

            Dim repo = New SupportRepo()

            Dim bDate = CDate(businessDate)

            Dim bargraphID = repo.GetBargraphIDForBusinessDate(storeID, bDate)
            Dim store = Context.Cache.CachedStore(storeID)         ' based on defaultstore for user .
            '                                                        No, this should be based on the store we are requesting for.




            ' if negative, no bargraph found; throw an exception
            If (bargraphID < 0) Then Throw New ApplicationException(String.Format("No schedule for '{0}'", businessDate))

            Dim bg = Me.Context.Cache.CachedBargraph(bargraphID)
            Dim bgStartMinute = bg.BargraphStartMinutes

            Dim shiftsList = New List(Of Model.Support.Dtos.ShiftInfo)
            Dim RPID As Integer
            RPID = GetReportingPeriodID(storeID, bDate)
            For Each sh In bg.Shifts
                Dim shiftInfo = New Model.Support.Dtos.ShiftInfo()
                With shiftInfo
                    .EID = sh.Employee_ID.GetValueOrDefault()
                    .ET = mCommonFunctions.MinuteToTimeStr(sh.AbsEndTime, True)
                    .PN = store.GetStorePosition(sh.Position_ID.GetValueOrDefault, RPID).Abbreviation
                    .TH = sh.Duration_Minutes.GetValueOrDefault()
                    .BH = sh.GetBreakTime()
                    .ST = mCommonFunctions.MinuteToTimeStr(sh.AbsStartTime, True)
                End With

                shiftsList.Add(shiftInfo)
            Next

            Dim actualHrs = repo.GetEmployeeActualHrsForDate(storeID, bDate)

            Dim result = New Model.Support.Dtos.DailyEmpoyeeHrsDTO()
            result.ShiftData = shiftsList
            result.EmployeeData = actualHrs

            Return result
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function GetScheduleGuideStats(ByVal scheduleID As Integer) As IList(Of Model.SchedEdit.Dtos.BargraphGuideStatsDTO)

            Me.Session.CheckScheduleAccess(scheduleID)

            Dim sched = Me.Context.Cache.CachedSchedule(scheduleID)

            Dim forecasted = New Model.SchedEdit.WeeklyForecast(sched)
            Dim guides = New Model.SchedEdit.WeeklyGuideHours(forecasted, sched.CachedStore)

            Dim schedGuides = New List(Of Model.SchedEdit.Dtos.BargraphGuideStatsDTO)
            Dim store = sched.CachedStore

            Dim htCrewPerf As Hashtable

            Try
                htCrewPerf = getCrewPerformanceLevel(store.Store_ID)
            Catch ex As Exception

            End Try
            Dim dMaxDaily As Double = 0
            Dim dMaxWeekly As Double = 0
            Dim bEnableApprove As Boolean = False

            Try
                Dim sbSQL As New StringBuilder
                Dim tabResults As New DataTable

                With sbSQL

                    .AppendLine("SELECT")
                    .AppendLine("	[ValueName],ISNULL(Value,DefaultValue) as Value")
                    .AppendLine("FROM ")
                    .AppendLine("	tdPersistentValue dPV")
                    .AppendLine("	RIGHT JOIN tmPersistentValue mPV")
                    .AppendLine("		on mPV.UniversalPersistentValueIdentifier = dPV.UniversalPersistentValueIdentifier")
                    .AppendLine("WHERE ")
                    .AppendLine("	mPV.UniversalPersistentValueIdentifier IN ('D6E122A8C3E84FC784505EB879EE21BF','F2C2E49904F24B0980A306586999A5EA','2ADF4AB23EFC429EBDC8590F9FBB57C2')")

                End With

                Session.InvWSS.GetData(sbSQL.ToString, tabResults)

                Dim sDebug = ""
                If tabResults.Rows.Count > 0 Then
                    For Each Row As DataRow In tabResults.Rows
                        Select Case CStr(Row("ValueName"))
                            Case "DailyVarianceLimit"
                                dMaxDaily = CDec(Row("Value"))
                            Case "WeeklyVarianceLimit"
                                dMaxWeekly = CDec(Row("Value"))
                            Case "AllowAboveStoreApproval"
                                bEnableApprove = (CInt(Row("Value")) = 1)
                        End Select

                        sDebug &= CStr(Row("Value")) & ","

                    Next


                End If
                'Throw New ApplicationException(sDebug)
            Catch ex As Exception
                'Throw ex
            End Try

            Me.Context.Session.LoggedInUser().LoadUser()

            Dim URank As Integer = Session("UserRank")

            For Each bg In sched.Bargraphs
                Dim htNonService0 As Hashtable = getNonServiceAmounts(store.Store_ID, bg.BarGraph_ID, forecasted.GetForecast(bg.DayOfWeek), forecasted.GetForecastBreakfast(bg.DayOfWeek), 0, bg.Business_Date)
                Dim htNonService7 As Hashtable = getNonServiceAmounts(store.Store_ID, bg.BarGraph_ID, forecasted.TotalWeekForecast(), 0, 7, bg.Business_Date)

                Dim bgGuideStats = New Model.SchedEdit.Dtos.BargraphGuideStatsDTO()
                Dim intervals As Model.SchedEdit.Dtos.BarGraphIntervalsDTO = bg.GetIntervalsDTO(New WeeklyForecast(sched))
                With bgGuideStats
                    .BgID = bg.BarGraph_ID
                    .AllowApprove = bEnableApprove
                    .CanReopen = Me.Context.Session.LoggedInUser().CanReopenSched
                    .UserRank = URank
                    If store.UseIPTLaborMatrix Then
                        .MaxVarianceDaily = dMaxDaily
                        .MaxVarianceWeekly = dMaxWeekly
                        .WeeklyAdmin = CDbl(htNonService7.Item("Weekly Admin"))
                        .WeeklyClean = CDbl(htNonService7.Item("Weekly Clean"))
                        .WeeklyStock = CDbl(htNonService7.Item("Weekly Stock"))
                        .BA = 0
                        .BS = intervals.Intervals.Where(Function(i) i.Period = "DP1").Sum(Function(i) i.RH) / 2
                        .BT = CDbl(htNonService0.Item("BreakfastTransition"))
                        .RA = CDbl(htNonService0.Item("Post Rush")) + CDbl(htNonService0.Item("Post Rush/Re-Prep")) + CDbl(htNonService0.Item("Pre Close")) + CDbl(htNonService0.Item("Prep")) + CDbl(htNonService0.Item("Lettuce Prep"))
                        .RO = CDbl(htNonService0.Item("Open")) + CDbl(htNonService0.Item("Lunch Open"))
                        .ROB = CDbl(htNonService0.Item("Open Breakfast")) + CDbl(htNonService0.Item("Breakfast Open"))
                        .RS = intervals.Intervals.Where(Function(i) i.Period = "DP2").Sum(Function(i) i.RH) / 2
                        .DS = intervals.Intervals.Where(Function(i) i.Period = "DP3").Sum(Function(i) i.RH) / 2
                        .DA = 0
                        .LA = 0
                        .LS = intervals.Intervals.Where(Function(i) i.Period = "DP4").Sum(Function(i) i.RH) / 2
                        .LC = CDbl(htNonService0.Item("Close"))
                        .HLN = forecasted.HasLateNight(bg.DayOfWeek)
                        .AdminPostRush = CDbl(htNonService0.Item("Post Rush")) + CDbl(htNonService0.Item("Post Rush/Re-Prep"))
                        .AdminPreClose = CDbl(htNonService0.Item("Pre Close"))
                        .AdminPrep = CDbl(htNonService0.Item("Prep"))
                        .AdminLettucePrep = CDbl(htNonService0.Item("Lettuce Prep"))
                        If Not htCrewPerf Is Nothing Then
                            .CrewPerformanceDP1 = CDbl(htCrewPerf.Item("DP1"))
                            .CrewPerformanceDP2 = CDbl(htCrewPerf.Item("DP2"))
                            .CrewPerformanceDP3 = CDbl(htCrewPerf.Item("DP3"))
                            .CrewPerformanceDP4 = CDbl(htCrewPerf.Item("DP4"))
                        Else
                            .CrewPerformanceDP1 = -1
                            .CrewPerformanceDP2 = -1
                            .CrewPerformanceDP3 = -1
                            .CrewPerformanceDP4 = -1
                        End If

                    Else
                        .MaxVarianceDaily = dMaxDaily
                        .BA = guides.GetBreakfastAdminHours(bg.DayOfWeek)
                        .BS = guides.GetBreakfastServiceHours(bg.DayOfWeek)
                        .BT = guides.GetBreakfastTransitionHours(bg.DayOfWeek)
                        .RA = guides.GetRegularAdminHours(bg.DayOfWeek)
                        .RO = guides.GetRegularOpenHours(bg.DayOfWeek)
                        .RS = guides.GetRegularServiceHours(bg.DayOfWeek)
                        .DS = guides.GetDinnerServiceHours(bg.DayOfWeek)
                        .DA = guides.GetDinnerAdminHours(bg.DayOfWeek)
                        .LA = guides.GetLateNightAdminHours(bg.DayOfWeek)
                        .LS = guides.GetLateNightServiceHours(bg.DayOfWeek)
                        .LC = guides.GetLateNightCloseHours(bg.DayOfWeek)
                        .HLN = forecasted.HasLateNight(bg.DayOfWeek)
                    End If
                End With

                schedGuides.Add(bgGuideStats)
            Next

            Return schedGuides
        End Function

        Public Function InsertNonServiceAmounts(ByVal storeID As String, ByVal Bargraph_ID As Integer, ByVal fcast As Double, ByVal fcastBfst As Double, ByVal DayWeek As Integer, Optional ByVal BusinessDate As DateTime = Nothing) As Boolean
            Dim bFailed As Boolean = False
            Dim sql As String
            sql = "EXEC getNonServiceGuide @Store_ID = @store , @SalesForcast = @forecast, @BreakfastForcast = @breakfast, @Weekly = @dayweek, @BusinessDate = @bdate"

            Try
                Dim DB As New HVDatabase
                DB.Open()
                Dim cmd As SqlCommand = New SqlCommand(sql, DB.objConn)
                cmd.Parameters.Add(DB.makeSQLParam("@store", storeID, SqlDbType.VarChar))
                cmd.Parameters.Add(DB.makeSQLParam("@forecast", fcast, SqlDbType.Float))
                cmd.Parameters.Add(DB.makeSQLParam("@breakfast", fcastBfst, SqlDbType.Float)) 'ToDo
                cmd.Parameters.Add(DB.makeSQLParam("@dayweek", DayWeek, SqlDbType.Int))
                cmd.Parameters.Add(DB.makeSQLParam("@bdate", BusinessDate, SqlDbType.DateTime))
                Dim dr As SqlDataReader = cmd.ExecuteReader()


                Dim sqlInsert As New StringBuilder

                While dr.Read()
                    sqlInsert.AppendLine("INSERT INTO [dss].[NONSERVICEGUIDEHOURS] (NonServiceFinalCalcID, NonServiceFinalCalcDescription, Amount, Bargraph_ID, DayWeek) VALUES")
                    sqlInsert.AppendLine("(" & dr("NonServiceFinalCalcID") & ",'" & dr("NonServiceFinalCalcDescription") & "'," & dr("Amount") & "," & Bargraph_ID & "," & DayWeek & ")")
                End While

                Using cmdInsert As SqlCommand = New SqlCommand(String.Format(sqlInsert.ToString, Session("DSS_Path")))
                    Session.InvWSS.Platform.SBODBExecNonQuerySQLCommand("SBOCore", cmdInsert)
                End Using

                DB.Close()
                dr.Close()
            Catch ex As Exception
                Dim serror As String
                serror = ex.ToString
            End Try
            Return bFailed
        End Function

        Public Function getNonServiceAmounts(ByVal storeID As String, ByVal Bargraph_ID As Integer, ByVal fcast As Double, ByVal fcastBfst As Double, ByVal DayWeek As Integer, Optional ByVal BusinessDate As DateTime = Nothing) As Hashtable
            Dim sql As String
            Dim bFailed As Boolean = False
            Dim htWages As New Hashtable

            Try
                Dim sqlGet As String
                Dim rowCount As Integer
                If DayWeek = 0 Then
                    sqlGet = "SELECT * FROM [dss].[NONSERVICEGUIDEHOURS] WHERE Bargraph_ID = " & Bargraph_ID & " AND DayWeek = 0"
                Else
                    sqlGet = "SELECT * FROM [dss].[NONSERVICEGUIDEHOURS] WHERE Bargraph_ID = " & Bargraph_ID & " AND DayWeek <> 0"
                End If
                Dim DB As New HVDatabase
                DB.Open()
                Dim cmd As SqlCommand = New SqlCommand(sqlGet, DB.objConn)
                Dim dr As SqlDataReader = cmd.ExecuteReader()
                While dr.Read()
                    rowCount += 1
                    'htWages.Add(dr("Server"), dr("LocalDatabaseName"))
                    htWages.Add(dr("NonServiceFinalCalcDescription"), dr("Amount"))  'dr("NonServiceFinalCalcDescription"), 
                End While
                DB.Close()
                dr.Close()
                If rowCount > 0 Then
                    Return htWages
                    Exit Function
                End If
            Catch ex As Exception

            End Try


            sql = "EXEC getNonServiceGuide @Store_ID = @store , @SalesForcast = @forecast, @BreakfastForcast = @breakfast, @Weekly = @dayweek, @BusinessDate = @bdate"
            Try
                Dim DB As New HVDatabase
                DB.Open()
                Dim cmd As SqlCommand = New SqlCommand(sql, DB.objConn)
                cmd.Parameters.Add(DB.makeSQLParam("@store", storeID, SqlDbType.VarChar))
                cmd.Parameters.Add(DB.makeSQLParam("@forecast", fcast, SqlDbType.Float))
                cmd.Parameters.Add(DB.makeSQLParam("@breakfast", fcastBfst, SqlDbType.Float)) 'ToDo
                cmd.Parameters.Add(DB.makeSQLParam("@dayweek", DayWeek, SqlDbType.Int))
                cmd.Parameters.Add(DB.makeSQLParam("@bdate", BusinessDate, SqlDbType.DateTime))
                Dim dr As SqlDataReader = cmd.ExecuteReader()



                While dr.Read()
                    'htWages.Add(dr("Server"), dr("LocalDatabaseName"))
                    htWages.Add(dr("NonServiceFinalCalcDescription"), dr("Amount"))  'dr("NonServiceFinalCalcDescription"), 
                End While

                DB.Close()
                dr.Close()
            Catch ex As Exception
                Dim exs As String = ex.Message
                bFailed = True
            End Try

            If bFailed Then
                sql = "EXEC getNonServiceGuide @Store_ID = @store , @SalesForcast = @forecast, @Weekly = @dayweek"

                'Dim htWages As New Hashtable
                Try
                    Dim DB As New HVDatabase
                    DB.Open()
                    Dim cmd As SqlCommand = New SqlCommand(sql, DB.objConn)
                    cmd.Parameters.Add(DB.makeSQLParam("@store", storeID, SqlDbType.VarChar))
                    cmd.Parameters.Add(DB.makeSQLParam("@forecast", fcast, SqlDbType.Float))
                    cmd.Parameters.Add(DB.makeSQLParam("@dayweek", DayWeek, SqlDbType.Int))
                    cmd.Parameters.Add(DB.makeSQLParam("@bdate", BusinessDate, SqlDbType.Date))
                    Dim dr As SqlDataReader = cmd.ExecuteReader()

                    While dr.Read()
                        'htWages.Add(dr("Server"), dr("LocalDatabaseName"))
                        htWages.Add(dr("NonServiceFinalCalcDescription"), dr("Amount"))  'dr("NonServiceFinalCalcDescription"), 
                    End While

                    DB.Close()
                    dr.Close()
                Catch ex As Exception
                    Dim exs As String = ex.Message
                End Try
            End If

            Return htWages
        End Function
        Public Function getCrewPerformanceLevel(ByVal sStoreID As String) As Hashtable
            Dim htCrewPerformance As New Hashtable
            Dim sSQL As String
            sSQL = "SELECT tmSL.[CrewProductivityDP1], tmSL.[CrewProductivityDP2], tmSL.[CrewProductivityDP3], tmSL.[CrewProductivityDP4] "
            sSQL &= "FROM [dbo].[tmStoreLabor] AS tmSL WITH(NOLOCK) "
            sSQL &= "INNER JOIN [dbo].[tmStore] AS tmS WITH(NOLOCK) "
            sSQL &= "ON tmSL.[StoreID] = tmS.[StoreID] "
            sSQL &= "WHERE LEFT(tms.[UniversalNodeIdentifier], 31) = '" & sStoreID & "'"
            Try
                Dim bDataExists As Boolean = False
                Dim tabResults As DataTable = Nothing
                If Not Session.InvWSS.GetData(sSQL, tabResults) Then Throw New ApplicationException(Session.InvWSS.ErrorMessage)

                For Each dr As DataRow In tabResults.Rows
                    If Not IsDBNull(dr("CrewProductivityDP1")) Then
                        htCrewPerformance.Add("DP1", dr("CrewProductivityDP1"))
                    Else
                        htCrewPerformance.Add("DP1", -1)
                    End If
                    If Not IsDBNull(dr("CrewProductivityDP2")) Then
                        htCrewPerformance.Add("DP2", dr("CrewProductivityDP2"))
                    Else
                        htCrewPerformance.Add("DP2", -1)
                    End If
                    If Not IsDBNull(dr("CrewProductivityDP3")) Then
                        htCrewPerformance.Add("DP3", dr("CrewProductivityDP3"))
                    Else
                        htCrewPerformance.Add("DP3", -1)
                    End If
                    If Not IsDBNull(dr("CrewProductivityDP4")) Then
                        htCrewPerformance.Add("DP4", dr("CrewProductivityDP4"))
                    Else
                        htCrewPerformance.Add("DP4", -1)
                    End If
                    bDataExists = True
                Next dr
                If bDataExists = False Then
                    htCrewPerformance.Add("DP1", -1)
                    htCrewPerformance.Add("DP2", -1)
                    htCrewPerformance.Add("DP3", -1)
                    htCrewPerformance.Add("DP4", -1)
                End If
                If tabResults IsNot Nothing Then tabResults.Dispose()

            Catch ex As Exception
                Dim exs As String = ex.Message
            End Try

            Return htCrewPerformance
        End Function

        Public Function XferHoursToSBONet(ByVal scheduleID As Integer) As Boolean
            Dim sched = Me.Context.Cache.CachedSchedule(scheduleID)
            Dim bgdata = sched.OrderedBargraphs()
            For Each bg As Model.SchedEdit.Bargraph In bgdata
                TransferScheduledHoursToSBONet(CDate(bg.Business_Date))
            Next
        End Function
        Public Function TransferScheduledHoursToSBONet(ByVal BusinessDate As Date) As Boolean
            Try
                Dim store = Me.CachedStore()
                Dim sbSQL As New StringBuilder
                With sbSQL
                    .AppendLine(" DELETE [dbo].[tmDSSMgrHours] WHERE SBONet_StoreID = @StoreID AND BusinessDate = @BusinessDate")
                    .AppendLine(" INSERT INTO [dbo].[tmDSSMgrHours] ([BusinessDate], [SBONet_StoreID], [DSS_Employee_Number], [DSS_Schedule_ID], [DSS_ScheduledHours]) ")

                    .AppendLine(" SELECT MAX(BG.Business_Date) AS [BusinessDate], @StoreID AS [SBONet_StoreID], MAX(EMP.Employee_Number) AS [DSS_Employee_Number], MAX(SCH.Schedule_ID) AS [DSS_Schedule_ID], SUM(SH.Duration_Minutes)/60 AS [DSS_ScheduledHours] FROM {0}.[BarGraphs] BG ")
                    .AppendLine(" INNER JOIN {0}.[SCHEDULES] SCH ON BG.Schedule_ID = SCH.Schedule_ID ")
                    .AppendLine(" INNER JOIN {0}.[SHIFTS] SH ON BG.Bargraph_ID = SH.Bargraph_ID ")
                    .AppendLine(" INNER JOIN {0}.[EMPLOYEES] EMP ON SH.Employee_ID = EMP.Employee_ID ")
                    .AppendLine(" WHERE SCH.Store_ID = @DSS_Store_ID")
                    .AppendLine(" AND BG.Business_Date = @BusinessDate")
                    .AppendLine(" AND EMP.Manager <> 0 ")
                    .AppendLine(" GROUP BY EMP.Employee_ID")
                End With
                Using cmd As SqlCommand = New SqlCommand(String.Format(sbSQL.ToString, Session("DSS_Path")))
                    cmd.Parameters.Add("@StoreID", SqlDbType.Int).Value = store.GetSBOnetStoreID()
                    cmd.Parameters.Add("@BusinessDate", SqlDbType.Date).Value = BusinessDate.Date
                    cmd.Parameters.Add("@DSS_Store_ID", SqlDbType.VarChar, 31).Value = store.Store_ID
                    Session.InvWSS.Platform.SBODBExecQuerySQLCommand("SBOCore", cmd)
                End Using

            Catch ex As Exception

            End Try
        End Function
#End Region


#Region "Forecast tool"
        <WebMethod(EnableSession:=True)> _
        Public Function GetSalesComparisons(ByVal refDate As String, ByVal date1 As String, ByVal date2 As String, ByVal date3 As String) As IList(Of Model.Support.Dtos.SalesCompareRowDTO)

            Dim user = Me.Session.LoggedInUser()

            If (Not user.Admin AndAlso Not user.SystemAdmin) Then Throw New UnauthorizedAccessException("User is not authorized to view sales/forecast information")

            Dim baseDate = CDate(refDate)
            Dim dt1 = CDate(date1)
            Dim dt2 = CDate(date2)
            Dim dt3 = CDate(date3)

            Dim store = Me.CachedStore()

            Dim openHH = store.OpenLabourIndexed(baseDate.DayOfWeek)
            Dim closeHH = store.CloseLabourIndexed(baseDate.DayOfWeek)

            Dim repo = New Model.Support.SupportRepo()

            Return repo.GetSalesComparisons(store.Store_ID, openHH, closeHH, dt1, dt2, dt3)

        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function GetForecastsForWeek(ByVal startDate As String, ByVal forecastModel As String) As IList(Of Model.Support.Dtos.ForecastDayDTO)

            Dim user = Me.Session.LoggedInUser()

            If (Not user.Admin AndAlso Not user.SystemAdmin) Then Throw New UnauthorizedAccessException("User is not authorized to view sales/forecast information")

            Dim baseDate = CDate(startDate)

            Dim repo = New SupportRepo()

            Dim store = Me.CachedStore()

            Dim genModel As SupportRepo.ForecastGenerationModel = SupportRepo.ForecastGenerationModel.PreviousWeeks

            If (forecastModel.StartsWith("Year")) Then genModel = SupportRepo.ForecastGenerationModel.PreviousYearsSameDOW


            Return repo.GetForecastsForWeek(baseDate, store, genModel)

        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function GetSalesTrends(ByVal startDate As Date) As Model.Support.Dtos.SalesTrendsDTO

            Me.Session.LoggedInUser()

            Dim store = Me.CachedStore()

            Dim repo = New SupportRepo()

            Return repo.GetSalesTrendDTO(store.Store_ID, startDate)

        End Function


        Private Function ConstructWeeklyBusOpenIndexes(ByRef store As Model.SchedEdit.Store, ByVal startDate As Date) As Integer()
            Dim indices(0 To 6) As Integer

            For i As Integer = 0 To 6
                indices(i) = store.OpenBusinessIndexed(startDate.Date.AddDays(i).DayOfWeek)
            Next

            Return indices

        End Function

        Private Function ConstructWeeklyBusCloseIndexes(ByRef store As Model.SchedEdit.Store, ByVal startDate As Date) As Integer()
            Dim indices(0 To 6) As Integer

            For i As Integer = 0 To 6
                indices(i) = store.CloseBusinessIndexed(startDate.Date.AddDays(i).DayOfWeek)
            Next

            Return indices

        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function SaveForecasts(ByVal forecasts As IList(Of Model.Support.Dtos.ForecastDayDTO)) As Integer

            Dim user = Me.Session.LoggedInUser()

            If (Not user.Admin AndAlso Not user.SystemAdmin) Then Throw New UnauthorizedAccessException("User is not authorized to view sales/forecast information")

            Dim repo = New SupportRepo()

            Dim store = Me.CachedStore()

            Return repo.SaveForecasts(store.Store_ID, forecasts)

        End Function
#End Region

#Region "CSI methods"

        <WebMethod(EnableSession:=True)> _
        Public Function GetEmployeeCompositeCSI(ByVal employeeID As Integer, ByVal storeID As String) As IList(Of CSICompRating)
            Dim user = Me.Context.Session.LoggedInUser()

            Me.Session.CheckEmployeeAccess(employeeID, storeID)

            Dim csiManager = Me.Context.Cache.CachedStore(storeID).CsiManager

            Return csiManager.GetEmpCompositeRatingsDTO(employeeID)
        End Function


        <WebMethod(EnableSession:=True)> _
        Public Sub SaveEmployeeComposite(ByVal employeeID As Integer, ByVal storeID As String, ByVal markerID As Integer, ByVal rating As Integer)

            Dim user = Me.Context.Session.LoggedInUser()

            Me.Session.CheckEmployeeAccess(employeeID, storeID)

            Dim csiManager = Me.Context.Cache.CachedStore(storeID).CsiManager

            csiManager.UpdateEmployeeComposite(markerID, employeeID, rating)

        End Sub

        <WebMethod(EnableSession:=True)> _
        Public Function SaveEmployeePositionCSI(ByVal storeID As String, ByVal employeeID As Integer, ByVal positionID As Integer, ByVal rating As Integer) As Double

            Dim user = Me.Context.Session.LoggedInUser()

            Me.Session.CheckEmployeeAccess(employeeID, storeID)

            Dim csiManager = Me.Context.Cache.CachedStore(storeID).CsiManager

            csiManager.UpdateEmpPos(employeeID, positionID, rating)

            Return csiManager.CalcEmployeeCSI(employeeID, positionID)
        End Function

#End Region

#Region "Email handling"
        <WebMethod(EnableSession:=True)> _
        Public Function SendEmails(ByVal scheduleID As Integer, ByVal bargraphIDs As Integer(), ByVal employeeIDs As Integer(), ByVal customMessage As String) As IList(Of Model.Support.Dtos.EmailSendStatusDTO)

            Try
                ' Debug logging
                HttpContext.Current.Response.Write("<script>console.log('DataService.SendEmails: Starting with scheduleID=" & scheduleID & ", employeeCount=" & employeeIDs.Length & "');</script>")

                Dim schedule = Me.Context.Cache.CachedSchedule(scheduleID)
                Dim store = Me.Context.Cache.CachedStore(schedule.Store_ID)

                HttpContext.Current.Response.Write("<script>console.log('DataService.SendEmails: Retrieved schedule and store');</script>")

                Dim emailer = New Model.Support.ScheduleEmailer(store, schedule, Me.Session.LoggedInUser())

                HttpContext.Current.Response.Write("<script>console.log('DataService.SendEmails: Created emailer, calling SendEmails');</script>")
                emailer.SendEmails(bargraphIDs, employeeIDs, customMessage)

                Dim statuses = emailer.EmailSendStatuses
                HttpContext.Current.Response.Write("<script>console.log('DataService.SendEmails: Completed, returning " & statuses.Count & " statuses');</script>")

                Return statuses
            Catch ex As Exception
                HttpContext.Current.Response.Write("<script>console.error('DataService.SendEmails: Exception occurred: " & ex.Message.Replace("'", "\'") & "');</script>")
                Throw New ApplicationException(Me.GetErrorMessage(ex))
            End Try

        End Function

#End Region

#Region "Report and schedule selector functions"
        <WebMethod(EnableSession:=True)> _
        Public Function ChangeCurrentStore(ByVal storeID As String) As Boolean

            Me.Session.CheckStoreAccess(storeID)
            Try
                Dim user = Me.Context.Session.LoggedInUser()
                user.DefaultStore = storeID
                Me.Context.Cache.CachedStore(storeID)
                Me.Context.Cache.RefreshCachedStore(storeID)
                Return True
            Catch ex As Exception
                Throw New ApplicationException("Could not change the current store: " & ex.Message)
            End Try
            Return False
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function GetScheduleList(ByVal dateStart As String, ByVal dateEnd As String) As IList(Of Model.Support.Dtos.ScheduleListItemDTO)

            Dim startDate = CDate(dateStart)
            Dim endDate = CDate(dateEnd)
            Return New Model.Support.SupportRepo().GetScheduleList(startDate, endDate, Me.Context.Session.LoggedInUser().DefaultStore)

        End Function

        ''' <summary>
        ''' Retrieves a list of current and future schedules.  Will look 2 months into the future.
        ''' </summary>
        ''' <returns></returns>
        ''' <remarks></remarks>
        <WebMethod(EnableSession:=True)> _
        Public Function GetActiveScheduleList() As IList(Of Model.Support.Dtos.ScheduleListItemDTO)
            Dim repo = New Model.Support.SupportRepo()

            Dim startDate = repo.GetStartDateOfCurrentSchedule(Me.Context.Session.LoggedInUser().DefaultStore)

            If (startDate = Date.MinValue) Then
                startDate = Date.Now
            End If

            'BDean: 01/16/2020 - Search 2 months so that the list will show schedules for the same range the users are able to create them for (was previously +28 days)
            Dim endDate = startDate.AddMonths(2)

            Dim results = repo.GetScheduleList(startDate, endDate.AddDays(6), Me.Context.Session.LoggedInUser().DefaultStore, True, True, False)

            Return (From r In results Order By r.DateStart Ascending).ToList()

        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function GetLegacyScheduleList(ByVal daysBack As Integer) As IList(Of Model.Support.Dtos.ScheduleListItemDTO)
            Dim repo = New Model.Support.SupportRepo()

            Dim thresholdDate = repo.GetStartDateOfCurrentSchedule(Me.Context.Session.LoggedInUser().DefaultStore)

            If (thresholdDate = Date.MinValue) Then
                thresholdDate = Date.Now.AddDays(-1)
            Else
                thresholdDate = thresholdDate.AddDays(-1)
            End If
            Return New Model.Support.SupportRepo().GetScheduleList(thresholdDate.AddDays(daysBack * -1), thresholdDate, Me.Context.Session.LoggedInUser().DefaultStore, False, False, True)
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function GetForecastExistence(ByVal startDate As String) As BooleanResponseDTO

            Dim dte = CDate(startDate)
            Dim repo = New SupportRepo()

            Dim results = repo.GetWeeklyForecastExistencePerDay(dte, Me.Session.LoggedInUser.DefaultStore)
            Dim message = "All forecasts have been set."
            Dim boolResponse = True

            If (results.Count = 0) Then
                message = "No forecasts have been set for this date range."
                boolResponse = False
            ElseIf (results.Count < 7) Then
                message = "Forecasts missing for: "
                Dim passedFirstItem = False
                For i As Integer = 0 To 6
                    Dim dateToCompare = dte.AddDays(i)

                    If (results.Where(Function(dt) dt.Key.Equals(dateToCompare)).Count() = 0) Then
                        If (Not passedFirstItem) Then
                            passedFirstItem = True
                        Else
                            message &= ", "
                        End If
                        message &= dateToCompare.ToString("ddd MMM dd yyyy")
                    End If
                Next

                boolResponse = False

            End If

            Return New BooleanResponseDTO() With {.Message = message, .Result = boolResponse}
        End Function


#End Region

#Region "Template management"
        <WebMethod(EnableSession:=True)> _
        Public Function GetTemplates(ByVal storeID As String) As IList(Of Model.Support.Dtos.TemplateListItemDTO)

            Me.Session.CheckStoreAccess(storeID)
            Return New Model.Support.SupportRepo().GetTemplates(storeID)
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function LoadTemplate(ByVal bargraphID As Integer, ByVal templateID As Integer) As Boolean
            Me.Session.CheckBargraphAccess(bargraphID)
            Me.Session.CheckTemplateAccess(templateID)

            Dim bUseManagerSchedule As Boolean = Session.InvWSS.Platform.PersistentValue("DSS_Settings", "UseManagerSchedule")

            Dim repo = New ScheduleRepo()

            Dim result = repo.OverwriteBargraphWithTemplate(bargraphID, templateID, bUseManagerSchedule)

            Dim scheduleID = repo.GetScheduleIDByBargraph(bargraphID)

            Me.Context.Cache.RefreshBargraph(scheduleID, bargraphID)


            If bUseManagerSchedule Then
                Me.Context.Cache.getManagerShifts(scheduleID, bargraphID)
            End If






        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function OverwriteTemplate(ByVal bargraphID As Integer, ByVal templateID As Integer, ByVal copyEmployees As Boolean) As Boolean
            Me.Session.CheckBargraphAccess(bargraphID)
            Me.Session.CheckTemplateAccess(templateID)
            Dim bUseManagerSchedule As Boolean = Session.InvWSS.Platform.PersistentValue("DSS_Settings", "UseManagerSchedule")
            Return New ScheduleRepo().OverwriteTemplateWithBargraph(bargraphID, templateID, copyEmployees, bUseManagerSchedule)
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function SaveBargraphAsTemplate(ByVal bargraphID As Integer, ByVal templateName As String, ByVal copyEmployees As Boolean) As Boolean
            Me.Session.CheckBargraphAccess(bargraphID)
            Dim bUseManagerSchedule As Boolean = Session.InvWSS.Platform.PersistentValue("DSS_Settings", "UseManagerSchedule")
            Return New ScheduleRepo().SaveBargraphAsTemplate(bargraphID, templateName, copyEmployees, bUseManagerSchedule)
        End Function

        <WebMethod(EnableSession:=True)>
        Public Function CreateTemplatesFromLastWeek(ByVal bargraphID As Integer, ByVal copyEmployees As Boolean) As Boolean
            Me.Session.CheckBargraphAccess(bargraphID)
            Dim bUseManagerSchedule As Boolean = Session.InvWSS.Platform.PersistentValue("DSS_Settings", "UseManagerSchedule")
            Dim sbSQL As New StringBuilder
            Dim tabResults As New DataTable
            Dim bReturn As Boolean = False
            Dim sTemplateName As String
            Try
                With sbSQL
                    .AppendLine("Select BarGraph_ID, Business_Date FROM BARGRAPHS bg INNER JOIN SCHEDULES s On bg.Schedule_ID = s.Schedule_ID")
                    .AppendLine("WHERE s.Store_ID IN (SELECT Store_ID FROM SCHEDULES INNER JOIN BARGRAPHS ON SCHEDULES.Schedule_ID = BARGRAPHS.Schedule_ID WHERE BARGRAPHS.Bargraph_ID = " & bargraphID & ")")
                    .AppendLine("And s.Date_Start = (SELECT DATEADD(day,-7,Date_Start) FROM SCHEDULES INNER JOIN BARGRAPHS ON SCHEDULES.Schedule_ID = BARGRAPHS.Schedule_ID WHERE BARGRAPHS.Bargraph_ID = " & bargraphID & ")")
                End With

                Dim DB As New HVDatabase
                DB.Open()
                Dim Adapter As New SqlDataAdapter(sbSQL.ToString, DB.objConn)
                Adapter.Fill(tabResults)
                If tabResults.Rows.Count > 0 Then
                    For Each Row As DataRow In tabResults.Rows

                        sTemplateName = CDate(Row("Business_Date")).DayOfWeek.ToString & "_" & Format(CDate(Row("Business_Date")), "MMddyyyy")
                        Dim sDSQL As New StringBuilder
                        With sDSQL
                            .Append("select st.Template_ID from Scheduler_Templates st ")
                            .Append("Left outer join bargraphs bg on st.Bargraph_ID = bg.Bargraph_ID ")
                            .Append("WHERE st.Store_ID IN (SELECT Store_ID FROM SCHEDULES INNER JOIN BARGRAPHS ON SCHEDULES.Schedule_ID = BARGRAPHS.Schedule_ID WHERE BARGRAPHS.Bargraph_ID = " & bargraphID & ")")
                            .Append("AND bg.Bargraph_Name = '" & sTemplateName & "'")

                        End With
                        Dim tabTemplate As New DataTable
                        Dim DAdapter As New SqlDataAdapter(sDSQL.ToString, DB.objConn)
                        Dim DReturn As Boolean
                        DAdapter.Fill(tabTemplate)
                        For Each dRow As DataRow In tabTemplate.Rows
                            DReturn = DeleteTemplate(CInt(dRow("Template_ID")))
                        Next
                        bReturn = New ScheduleRepo().SaveBargraphAsTemplate(CInt(Row("bargraph_ID")), sTemplateName, copyEmployees, bUseManagerSchedule)
                    Next
                End If
                DB.Close()
            Catch ex As Exception
                bReturn = False
            End Try

            Return bReturn
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function DeleteTemplate(ByVal templateID As Integer) As Boolean
            Me.Session.CheckTemplateAccess(templateID)
            Return New ScheduleRepo().DeleteTemplate(templateID)
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function GetTemplateBargraph(ByVal templateID As Integer) As IList(Of BarGraphDataDTO)

            Me.Session.CheckTemplateAccess(templateID)
            Dim schedRepo = New ScheduleRepo()

            Dim store = Me.Context.Cache.CachedStore(schedRepo.GetStoreIDFromTemplate(templateID))

            Dim output = New List(Of BarGraphDataDTO)

            output.Add(schedRepo.GetTemplateBargraphDTO(templateID, store))

            Return output

        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function GetTemplateScheduleData(ByVal templateID As Integer) As ScheduleDataDTO
            Me.Session.CheckTemplateAccess(templateID)
            Dim schedRepo = New ScheduleRepo()

            ' need to explicitly retrieve the store id; may not correspond with user.defaultstore 
            ' if session times out and redirected back to this resource
            Dim storeID = schedRepo.GetStoreIDFromTemplate(templateID)
            Return schedRepo.GetTemplateScheduleDataDTO(templateID, Me.Context.Cache.CachedStore(storeID))

        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function SaveTemplateBargraph(ByVal bargraphID As Integer, ByVal shifts As ShiftDTO()) As Boolean
            Me.Session.CheckTemplateBargraphAccess(bargraphID)
            Return New ScheduleRepo().SaveBargraph(bargraphID, shifts)
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function GetTemplateShifts(ByVal bargraphID As Integer) As IList(Of ShiftDTO)
            Me.Session.CheckTemplateBargraphAccess(bargraphID)
            Dim repo = New ScheduleRepo()
            Dim bargraph = repo.GetBarGraph(bargraphID, True)

            ' hack alert: need to attach a schedule to this bargraph so that store settings (i.e., CSI) can be accessed.
            ' only need to set the storeid attribute.
            Dim sched = New Model.SchedEdit.Schedule() With {.Store_ID = Me.Session.LoggedInUser().DefaultStore}
            bargraph.Schedule = sched
            Return bargraph.GetShiftsDTO()
        End Function

#End Region

#Region "Finalization And Schedule Deletion"

        <WebMethod(EnableSession:=True)> _
        Public Function FinalizeSchedule(ByVal scheduleID As Integer) As Boolean
            Me.Session.CheckScheduleAccess(scheduleID)
            Dim results = New ScheduleRepo().FinalizeSchedule(scheduleID, Me.Session.LoggedInUser())
            If (results = True) Then Me.Context.Cache.RemoveCachedSchedule(scheduleID)
            If (results = True) Then XferHoursToSBONet(scheduleID)
            Return results
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function FinalizeBargraph(ByVal bargraphID As Integer) As Boolean
            Me.Session.CheckBargraphAccess(bargraphID)
            Dim results = New ScheduleRepo().FinalizeBargraph(bargraphID, Me.Session.LoggedInUser())
            Dim bargraph = Me.Context.Cache.CachedBargraph(bargraphID)
            Me.Context.Cache.RefreshBargraph(bargraph.Schedule_ID.GetValueOrDefault(), bargraphID)
            If (results = True) Then TransferScheduledHoursToSBONet(CDate(bargraph.Business_Date))
            Return results
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function ApproveSchedule(ByVal scheduleID As Integer) As Boolean
            Me.Session.CheckScheduleAccess(scheduleID)
            Dim results = New ScheduleRepo().ApproveSchedule(scheduleID, Me.Session.LoggedInUser())
            If (results = True) Then Me.Context.Cache.RemoveCachedSchedule(scheduleID)
            Return results
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function ApproveBargraph(ByVal bargraphID As Integer) As Boolean
            Me.Session.CheckBargraphAccess(bargraphID)
            Dim results = New ScheduleRepo().ApproveBargraph(bargraphID, Me.Session.LoggedInUser())
            Dim bargraph = Me.Context.Cache.CachedBargraph(bargraphID)
            Me.Context.Cache.RefreshBargraph(bargraph.Schedule_ID.GetValueOrDefault(), bargraphID)
            Return results
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function ReopenBargraph(ByVal bargraphID As Integer) As Boolean
            Dim repo = New ScheduleRepo()
            Me.Session.CheckBargraphAccess(bargraphID)
            Dim results = repo.ReOpenBargraph(bargraphID)
            Dim bargraph = Me.Context.Cache.CachedBargraph(bargraphID)
            Me.Context.Cache.RefreshBargraph(bargraph.Schedule_ID.GetValueOrDefault(), bargraphID)
            Return results
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function DeleteSchedule(ByVal scheduleID As Integer) As Boolean
            Me.Session.CheckScheduleAccess(scheduleID)
            Dim results = New ScheduleRepo().DeleteSchedule(scheduleID)
            Me.Context.Cache.RemoveCachedSchedule(scheduleID)
            Return results
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function ReopenSchedule(ByVal scheduleID As Integer) As Boolean
            Me.Session.CheckScheduleAccess(scheduleID)
            Dim sched = Me.Context.Cache.CachedSchedule(scheduleID)
            For Each bg In sched.OrderedBargraphs
                If (ReopenBargraph(bg.BarGraph_ID) = False) Then
                    Return False
                End If
            Next

            Me.Context.Cache.RemoveCachedSchedule(scheduleID)
            Return True
        End Function
#End Region

#Region "Copy store settings"
        <WebMethod(EnableSession:=True)> _
        Public Function CopyStoreSettings(ByVal sourceStoreID As String, ByVal destStoreID As String) As Integer
            Me.Session.CheckStoreAccess(sourceStoreID)
            Me.Session.CheckStoreAccess(destStoreID)

            If (Not Me.Session.LoggedInUser().SystemAdmin) Then
                Throw New UnauthorizedAccessException("You do Not have access to this service")
            End If

            If (sourceStoreID = destStoreID) Then Return 0
            Me.Context.Cache.RemoveCachedStore(destStoreID)
            Return New SupportRepo().CopyStoreSettings(sourceStoreID, destStoreID)

        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function CopyStoreGuideValues(ByVal sourceStoreID As String, ByVal destStoreID As String) As Integer
            Me.Session.CheckStoreAccess(sourceStoreID)
            Me.Session.CheckStoreAccess(destStoreID)

            If (Not Me.Session.LoggedInUser().SystemAdmin) Then
                Throw New UnauthorizedAccessException("You do Not have access to this service")
            End If

            If (sourceStoreID = destStoreID) Then Return 0

            Me.Context.Cache.RemoveCachedStore(destStoreID)
            Return New SupportRepo().CopyStoreGuideValues(sourceStoreID, destStoreID)

        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function CopyStoreOpenClose(ByVal sourceStoreID As String, ByVal destStoreID As String) As Integer
            Me.Session.CheckStoreAccess(sourceStoreID)
            Me.Session.CheckStoreAccess(destStoreID)

            If (Not Me.Session.LoggedInUser().SystemAdmin) Then
                Throw New UnauthorizedAccessException("You do Not have access to this service")
            End If

            If (sourceStoreID = destStoreID) Then Return 0

            Me.Context.Cache.RemoveCachedStore(destStoreID)
            Return New SupportRepo().CopyStoreOpenClose(sourceStoreID, destStoreID)

        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function CopyStoreHours(ByVal sourceStoreID As String, ByVal destStoreID As String) As Integer
            Me.Session.CheckStoreAccess(sourceStoreID)
            Me.Session.CheckStoreAccess(destStoreID)

            If (Not Me.Session.LoggedInUser().SystemAdmin) Then
                Throw New UnauthorizedAccessException("You do Not have access to this service")
            End If

            If (sourceStoreID = destStoreID) Then Return 0
            Me.Context.Cache.RemoveCachedStore(destStoreID)
            Return New SupportRepo().CopyStoreHours(sourceStoreID, destStoreID)

        End Function


#End Region

#Region "private helpers"

        Private ReadOnly Property CachedStore As Model.SchedEdit.Store
            Get
                Return Me.Context.Cache.CachedStore(Me.Session.LoggedInUser().DefaultStore)
            End Get
        End Property

        ''' <summary>
        ''' Throw an exception and determines how detailed the message should be.  The "DetailedErrorMessages" key/value in the appSettings in web.config
        ''' defines whether we include the stack trace or not along with the original exception.message
        ''' </summary>
        ''' <param name="ex"></param>
        ''' <remarks></remarks>
        Private Function GetErrorMessage(ByRef ex As Exception) As String

            If ShowDetailedErrors Then
                Return ex.Message & vbCrLf & ex.StackTrace
            Else
                Return ex.Message
            End If

        End Function

        ''' <summary>
        ''' The string found in the web.config in the key "DetailedErrorMessages"
        ''' </summary>
        ''' <remarks></remarks>
        Private Shared _detailedErrorMessageStr As String = Nothing

        ''' <summary>
        ''' The value derived from the web.config appsetting "DetailedErrorMessages"
        ''' </summary>
        ''' <remarks></remarks>
        Private Shared _detailedErrorMessage As Boolean = False

        ''' <summary>
        ''' Determines whether we want to display detailed messages (stack traces) or not
        ''' </summary>
        ''' <value></value>
        ''' <returns></returns>
        ''' <remarks></remarks>
        Private Shared ReadOnly Property ShowDetailedErrors As Boolean
            Get

                ' check if we've looked at the web.config yet ('nothing' tells us we haven't)
                If (_detailedErrorMessageStr Is Nothing) Then
                    _detailedErrorMessageStr = ConfigurationManager.AppSettings("DetailedErrorMessages")

                    ' perhaps the "DetailedErrorMessages" value does not exist in web.config
                    If (_detailedErrorMessageStr = Nothing) Then
                        _detailedErrorMessageStr = String.Empty
                    End If

                    ' possible values of true; otherwise show false
                    If (_detailedErrorMessageStr.Equals("True") Or _detailedErrorMessageStr.Equals("true") Or _detailedErrorMessageStr.Equals("1")) Then
                        _detailedErrorMessage = True
                    Else
                        _detailedErrorMessage = False
                    End If


                End If
                Return _detailedErrorMessage
            End Get
        End Property


#End Region

#Region "Position categories"

#Region "Store level"
        <WebMethod(EnableSession:=True)> _
        Public Function GetStoreCategories(ByVal storeID As String) As StorePosCatDataDTO

            Try
                ' exception thrown if user doesn't have access
                Me.Session.CheckStoreAccess(storeID)

                Dim repo = New SupportRepo()

                Return repo.GetPositionCategoryData(storeID)

            Catch ex As Exception
                Throw New ApplicationException(Me.GetErrorMessage(ex))
            End Try
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function RenameStoreCategory(ByVal categoryID As Integer, ByVal newName As String) As Boolean
            Dim repo = New SupportRepo()

            Dim storeID = repo.GetStoreIDByCategoryID(categoryID)

            ' will throw an exception if user doesn't have acess to store
            Me.Session.CheckStoreAccess(storeID)

            Try
                repo.RenameStoreCategory(categoryID, newName)
                Me.Context.Cache.RemoveCachedStore(storeID)
                Return True
            Catch ex As Exception
                Throw New ApplicationException(Me.GetErrorMessage(ex))
            End Try

        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function DeleteStoreCategory(ByVal categoryID As Integer) As StorePosCatDataDTO
            Dim repo = New SupportRepo()

            Dim storeID = repo.GetStoreIDByCategoryID(categoryID)

            Me.Session.CheckStoreAccess(storeID)

            Try

                repo.DeleteStoreCategory(categoryID)
                Me.Context.Cache.RemoveCachedStore(storeID)
                Return Me.GetStoreCategories(storeID)
            Catch ex As Exception
                Throw New ApplicationException(Me.GetErrorMessage(ex))
            End Try

        End Function

        ''' <summary>
        ''' Creates a new department in the database and returns the department id for the new instance
        ''' </summary>
        ''' <param name="storeID"></param>
        ''' <param name="categoryName"></param>
        ''' <returns></returns>
        ''' <remarks></remarks>
        <WebMethod(EnableSession:=True)> _
        Public Function InsertStoreCategory(ByVal storeID As String, ByVal categoryName As String) As Integer

            Dim repo = New SupportRepo()

            Me.Session.CheckStoreAccess(storeID)
            Try
                Dim dept = repo.InsertStoreCategory(storeID, categoryName)
                Me.Context.Cache.RemoveCachedStore(storeID)
                Return dept.PositionCategoryID
            Catch ex As Exception
                Throw New ApplicationException(Me.GetErrorMessage(ex))
            End Try
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function SaveStoreCategoryPositions(ByVal categoryID As Integer, ByVal positions As Integer()) As Boolean

            Dim repo = New SupportRepo()
            Dim storeID = repo.GetStoreIDByCategoryID(categoryID)
            Try
                repo.SaveStoreCategoryMemberships(categoryID, positions)
                Me.Context.Cache.RemoveCachedStore(storeID)
                Return True
            Catch ex As Exception
                Throw New ApplicationException(Me.GetErrorMessage(ex))
            End Try
        End Function

#End Region

#Region "Franchise Level"

        <WebMethod(EnableSession:=True)> _
        Public Function GetFranchises() As Model.Support.Dtos.StoreGroupsDTO

            Try
                Dim user = Me.Context.Session.LoggedInUser()

                Dim repo = New SupportRepo()

                Return repo.GetStoreGroups(user.UID)

            Catch ex As Exception
                Throw New ApplicationException(Me.GetErrorMessage(ex))
            End Try
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function GetStoresForFranchise(ByVal groupID As Integer) As GroupedStoreDTO()

            Try
                Me.Session.CheckStoreGroupAccess(groupID)

                Dim repo = New SupportRepo()

                Return repo.GetStoresByGroup(groupID)

            Catch ex As Exception
                Throw New ApplicationException(Me.GetErrorMessage(ex))
            End Try
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function CreateNewFranchiseCategory(ByVal groupID As Integer, ByVal categoryName As String) As Integer

            Try
                Me.Session.CheckStoreGroupAccess(groupID)

                Dim repo = New SupportRepo()

                If (repo.DoesGroupCategoryExist(groupID, categoryName)) Then
                    Throw New ApplicationException("Category Name '" + categoryName + "' already exists.")
                End If

                Return repo.CreateNewGroupCategory(groupID, categoryName)


            Catch ex As Exception
                Throw New ApplicationException(Me.GetErrorMessage(ex))
            End Try

        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function DeleteFranchiseCategory(ByVal groupID As Integer, ByVal groupCategoryID As Integer) As Boolean

            Try
                Me.Session.CheckStoreGroupAccess(groupID)

                Dim repo = New SupportRepo()

                repo.DeleteGroupCategory(groupCategoryID)

                Return True
            Catch ex As Exception
                Throw New ApplicationException(Me.GetErrorMessage(ex))
            End Try
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function DoBulkStoreCategoryMerge(ByVal groupID As Integer, ByVal categoryName As String, ByVal storeIDs() As String) As Boolean

            Me.Session.CheckStoreGroupAccess(groupID)

            Try

                Dim repo = New SupportRepo()

                repo.DoBulkStoreCategoryMerge(groupID, categoryName, storeIDs)

                For Each storeid In storeIDs
                    Me.Context.Cache.RemoveCachedStore(storeid)
                Next

                Return True

            Catch ex As Exception
                Throw New ApplicationException(Me.GetErrorMessage(ex))
            End Try
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function DoBulkStoreCategoryRename(ByVal groupID As Integer, ByVal categoryName As String, ByVal storeIDs() As String, ByVal oldName As String) As Boolean

            Me.Session.CheckStoreGroupAccess(groupID)

            Try
                Dim repo = New SupportRepo()

                repo.DoBulkStoreCategoryRename(groupID, categoryName, storeIDs, oldName)
                For Each storeid In storeIDs
                    Me.Context.Cache.RemoveCachedStore(storeid)
                Next
                Return True
            Catch ex As Exception
                Throw New ApplicationException(Me.GetErrorMessage(ex))
            End Try
        End Function

        <WebMethod(EnableSession:=True)> _
        Public Function DoBulkStoreCategoryDelete(ByVal groupID As Integer, ByVal categoryName As String, ByVal storeIDs() As String) As Boolean

            Me.Session.CheckStoreGroupAccess(groupID)

            Try
                Dim repo = New SupportRepo()
                repo.DoBulkStoreCategoryDelete(groupID, categoryName, storeIDs)
                For Each storeid In storeIDs
                    Me.Context.Cache.RemoveCachedStore(storeid)
                Next
                Return True
            Catch ex As Exception
                Throw New ApplicationException(Me.GetErrorMessage(ex))
            End Try
        End Function

#End Region

#End Region

#Region "Date information"

        <WebMethod(EnableSession:=True)>
        Public Function MaxReportingPeriodDate() As Date
            Dim dResult As Date

            dResult = GetMaxReportingPeriodDate()

            Return dResult

        End Function
#End Region

    End Class
End Namespace
