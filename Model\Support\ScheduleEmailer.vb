﻿Imports System.Collections.Generic
Imports SBONet.Model.SchedEdit
Imports SBONet.Model.Support.Dtos
Imports System.Net.Mail
Imports AmazonUtilities.Mail.SimpleEmailService
Imports WebServicesAddons.SendGridEmailService
Imports System.Data.SqlClient

'''

Namespace Model.Support
    ''' <summary>
    ''' 2019-09-19 jjc Sprint 71 SBOD-942/943 Added RecordSend() Function to record the emailseindig activities to new [tsScheduleEmailRecord] Table,  And call it from SendEmails()
    ''' 2025-07-14 Mari SBOO-864 Switching email service to SendGrid
	''' </summary>
    Public Class ScheduleEmailer

        Private _schedule As Model.SchedEdit.Schedule
        Private _store As Model.SchedEdit.Store
        Private _user As Data.Hypervision.User
        Private _statuses As IList(Of EmailSendStatusDTO)

        Public Sub New(ByRef store As Model.SchedEdit.Store, ByRef schedule As Model.SchedEdit.Schedule, ByRef user As Data.Hypervision.User)
            Me._store = store
            Me._schedule = schedule
            Me._user = user
            Me._statuses = New List(Of EmailSendStatusDTO)
        End Sub

        Public Sub SendEmails(ByVal bargraphIDs As Integer(), ByVal employeeIDs As Integer(), Optional ByVal customMessage As String = "")

            For Each employeeID In employeeIDs

                Dim emp = Me._store.EmployeeData.GetEmployee(employeeID)
                Dim emailSubject As String = String.Empty
                Dim emailContent As String = String.Empty

                If (emp Is Nothing) Then
                    AddStatus(employeeID, "Could not find employee", EmailStatus.EmployeeNotFound)
                    Continue For
                End If

                Try
                    emailSubject = String.Format("DUMAC Staff Scheduler: schedule information for {0}", Me._store.Store_Name)
                    emailContent = ConstructEmailContent(bargraphIDs, emp, customMessage)
                    WebServicesAddons.SendGridEmailService.SendMail(emp.Email, emailSubject, emailContent)
                    AddStatus(employeeID, "No problems reported", EmailStatus.NoProblems)
                Catch sfre As SmtpFailedRecipientException
                    AddStatus(employeeID, "Failed to send to recipient: " & sfre.Message, EmailStatus.FailedRecipentException)
                Catch se As SmtpException
                    AddStatus(employeeID, "SMTP server problem: " & se.Message, EmailStatus.RemoteServerException)
                Finally
                    RecordSend(emp, customMessage, emailSubject, emailContent)
                End Try
            Next


        End Sub

        Public ReadOnly Property EmailSendStatuses As IList(Of EmailSendStatusDTO)
            Get
                Return Me._statuses
            End Get
        End Property

        Private Sub AddStatus(ByVal employeeID As Integer, ByVal message As String, ByVal status As EmailStatus)
            Dim item = New EmailSendStatusDTO()

            Dim emp = Me._store.EmployeeData.GetEmployee(employeeID)
            Dim employeeName = String.Empty

            If (Not emp Is Nothing) Then
                employeeName = emp.EmployeeFullName
            End If
            With item
                .EmployeeID = employeeID
                .EmployeeName = employeeName
                .Message = message
                .ScheduleID = Me._schedule.Schedule_ID
                .SenderUserID = Me._user.UID
                .Status = status
                .StatusName = [Enum].GetName(GetType(EmailStatus), CInt(status))
            End With

            Me._statuses.Add(item)
        End Sub

        Public Function EmployeeEmailIsValid(ByVal employee As Model.SchedEdit.EMPLOYEE) As Boolean

            If String.IsNullOrEmpty(employee.Email) Then Return False

            Dim regex As New System.Text.RegularExpressions.Regex( _
                "^[a-zA-Z][\w\.-]*[a-zA-Z0-9]@[a-zA-Z0-9][\w\.-]*[a-zA-Z0-9]\.[a-zA-Z][a-zA-Z\.]*[a-zA-Z]$")

            Dim match As System.Text.RegularExpressions.Match = regex.Match(employee.Email)

            Return match.Success

        End Function

        Public Function ConstructEmailContent(ByVal bargraphIDs As Integer(), ByRef employee As Model.SchedEdit.EMPLOYEE, ByVal customMessage As String) As String

            Dim sb = New System.Text.StringBuilder()
            Dim emp = employee

            sb.AppendFormat("<h3>Store and Employee</h3><table border='1'><tr><th>Store:</th><td>{0}</td></tr><tr><th>Employee:</th><td>{1}</td></tr></table>", _
                            Me._store.Store_Name, employee.EmployeeFullName())

            If Not String.IsNullOrEmpty(customMessage) Then
                sb.AppendFormat("<hr/><h3>Message from your supervisor:</h3> {0}<hr/>", customMessage)
            End If

            sb.Append("<h3>Here are your scheduled shifts:</h3><table border='1' cellpadding='4'><tr><th>Shift Date</th><th>Position</th><th>Time</th></tr>")

            Dim bargraphs = Me._schedule.OrderedBargraphs()

            For i As Integer = 0 To bargraphs.Count() - 1 Step 1
                Dim bg = bargraphs(i)
                Dim shifts = bg.Shifts.Where(Function(sh) sh.Employee_ID.Equals(emp.Employee_ID)).OrderBy(Function(sh) sh.Start_Minute)    ' may have more than one shift in a given day

                ' bargraph was not selected by user -- don't show any information
                If (Not bargraphIDs.Contains(bg.BarGraph_ID)) Then
                    sb.AppendFormat("<tr><td>{0}</td><td>TBA</td><td>TBA</td></tr>", _
                                    bg.Business_Date.GetValueOrDefault().ToLongDateString())
                    Continue For
                End If

                ' no shifts for this day for this emaployee
                If (shifts.Count() = 0) Then
                    sb.AppendFormat("<tr><td>{0}</td><td style='text-align: center;'>{1}</td><td style='text-align: center;'>{2}</td></tr>", _
                                    bg.Business_Date.GetValueOrDefault().ToLongDateString(), _
                                    " --- ", " --- ")
                    Continue For
                End If

                ' show all shifts for that day
                Dim RPID As Integer
                RPID = GetReportingPeriodID(bg.Schedule.Store_ID, bg.Schedule.Date_Start)
                For j As Integer = 0 To shifts.Count() - 1
                    Dim sh = shifts(j)
                    sb.AppendFormat("<tr><td>{0}</td><td>{1}</td><td>{2}</td></tr>", _
                        bg.Business_Date.GetValueOrDefault().ToLongDateString(), _
                        Me._store.GetStorePosition(sh.Position_ID.GetValueOrDefault(), RPID).Position_Name, _
                        MinuteToTimeStr(sh.AbsStartTime) & " to " & MinuteToTimeStr(sh.AbsEndTime))
                Next

            Next

            Return sb.ToString()
        End Function
        Public Function RecordSend(ByRef employee As Model.SchedEdit.EMPLOYEE, ByVal customMessage As String, emailSubject As String, emailContent As String) As Boolean
            Dim InvWSS As InventoryWebSiteServices.InventoryServices = HttpContext.Current.Session("IWSS")
            Dim cmdSQL As New SqlCommand
            Dim sbSQL As New StringBuilder
            Dim status As EmailSendStatusDTO
            Dim statusName As String = String.Empty
            Dim statusMessage As String = String.Empty
            Dim emp As Integer = employee.Employee_ID

            status = _statuses.First(Function(x) x.EmployeeID.Equals(emp))

            If Not status Is Nothing
                statusName =  status.StatusName
                statusMessage = status.Message
            End If

            sbSQL.AppendLine("/* For Testing")
            sbSQL.AppendLine("DECLARE @Store_ID VARCHAR(31) = 'C50E2016C4044721BCDC34390378579'")
            sbSQL.AppendLine("DECLARE @Schedule_ID INT = 0")
            sbSQL.AppendLine("DECLARE @ScheduleName VARCHAR(50) = ''")
            sbSQL.AppendLine("DECLARE @ScheduleDate_Start DATETIME = CAST(GETDATE() AS DATE)")
            sbSQL.AppendLine("DECLARE @ScheduleDate_End DATETIME = CAST(GETDATE() AS DATE)")
            sbSQL.AppendLine("DECLARE @Employee_ID INT = 410754")
            sbSQL.AppendLine("DECLARE @Sender_User_ID INT = 8675309")
            sbSQL.AppendLine("DECLARE @StatusName VARCHAR(25) = 'OK'")
            sbSQL.AppendLine("DECLARE @StatusMessage VARCHAR(100) = 'Mmmkay'")
            sbSQL.AppendLine("DECLARE @EmailAddress VARCHAR(50) = '<EMAIL>'")
            sbSQL.AppendLine("DECLARE @EmailSubject VARCHAR(100) = 'Here''s ya schedule'")
            sbSQL.AppendLine("DECLARE @CustomMessage VARCHAR(MAX) = 'You''re so special'")
            sbSQL.AppendLine("DECLARE @EmailBody VARCHAR(MAX) = 'HTML Here'")
            sbSQL.AppendLine("--*/")
            sbSQL.AppendLine("")
            sbSQL.AppendLine("DECLARE @StoreID INT")
            sbSQL.AppendLine("")
            sbSQL.AppendLine("SELECT @StoreID = [StoreID]")
            sbSQL.AppendLine("FROM [dbo].[tmStore]")
            sbSQL.AppendLine("WHERE [DSS_Store_ID] = @Store_ID")
            sbSQL.AppendLine("")
            sbSQL.AppendLine("--/*")
            sbSQL.AppendLine("INSERT [dbo].[tsScheduleEmailRecord]")
            sbSQL.AppendLine("	([StoreID]")
            sbSQL.AppendLine("	,[Schedule_ID]")
            sbSQL.AppendLine("	,[ScheduleName]")
            sbSQL.AppendLine("	,[ScheduleDate_Start]")
            sbSQL.AppendLine("	,[ScheduleDate_End]")
            sbSQL.AppendLine("	,[Employee_ID]")
            sbSQL.AppendLine("	,[Sender_User_ID]")
            sbSQL.AppendLine("	,[StatusName]")
            sbSQL.AppendLine("	,[StatusMessage]")
            sbSQL.AppendLine("	,[EmailAddress]")
            sbSQL.AppendLine("	,[EmailSubject]")
            sbSQL.AppendLine("	,[CustomMessage]")
            sbSQL.AppendLine("	,[Body]")
            sbSQL.AppendLine("	)--*/")
            sbSQL.AppendLine("SELECT")
            sbSQL.AppendLine("	 @StoreID AS [StoreID]")
            sbSQL.AppendLine("	,@Schedule_ID AS [Schedule_ID]")
            sbSQL.AppendLine("	,@ScheduleName AS [ScheduleName]")
            sbSQL.AppendLine("	,@ScheduleDate_Start AS [ScheduleDate_Start]")
            sbSQL.AppendLine("	,@ScheduleDate_End AS [ScheduleDate_End]")
            sbSQL.AppendLine("	,@Employee_ID AS [Employee_ID]")
            sbSQL.AppendLine("	,@Sender_User_ID AS [Sender_User_ID]")
            sbSQL.AppendLine("	,@StatusName AS [StatusName]")
            sbSQL.AppendLine("	,@StatusMessage AS [StatusMessage]")
            sbSQL.AppendLine("	,@EmailAddress AS [EmailAddress]")
            sbSQL.AppendLine("	,@EmailSubject AS [EmailSubject]")
            sbSQL.AppendLine("	,@CustomMessage AS [CustomMessage]")
            sbSQL.AppendLine("	,@EmailBody AS [EmailBody]")
            sbSQL.AppendLine("")
            sbSQL.AppendLine("")

            Try
                With cmdSQL
                    .CommandText = sbSQL.ToString()
                    .CommandType = CommandType.Text

                    .Parameters.Add("@Store_ID", SqlDbType.VarChar, 31).Value = Me._store.Store_ID

                    .Parameters.Add("@Schedule_ID", SqlDbType.Int).Value = 0
                    .Parameters.Add("@ScheduleName", SqlDbType.VarChar, 50).Value = ""
                    .Parameters.Add("@ScheduleDate_Start", SqlDbType.DateTime).Value = DBNull.Value
                    .Parameters.Add("@ScheduleDate_End", SqlDbType.DateTime).Value = DBNull.Value

                    If Not Me._schedule Is Nothing
                        .Parameters("@Schedule_ID").Value = Me._schedule.Schedule_ID
                        .Parameters("@ScheduleName").Value = Me._schedule.Name
                        .Parameters("@ScheduleDate_Start").Value = Me._schedule.Date_Start
                        .Parameters("@ScheduleDate_End").Value = Me._schedule.Date_End
                    End If

                    .Parameters.Add("@Employee_ID", SqlDbType.Int).Value = employee.Employee_ID
                    .Parameters.Add("@Sender_User_ID", SqlDbType.Int).Value = Me._user.UID
                    .Parameters.Add("@statusName", SqlDbType.VarChar, 25).Value = statusName
                    .Parameters.Add("@statusMessage", SqlDbType.VarChar, 100).Value = statusMessage
                    .Parameters.Add("@EmailAddress", SqlDbType.VarChar, 50).Value = employee.Email
                    .Parameters.Add("@EmailSubject", SqlDbType.VarChar, 100).Value = emailSubject
                    .Parameters.Add("@CustomMessage", SqlDbType.VarChar, -1).Value = customMessage
                    .Parameters.Add("@EmailBody", SqlDbType.VarChar, -1).Value = emailContent

                    InvWSS.Platform.SBODBExecNonQuerySQLCommand("SBOCore", cmdSQL)
                End With
            Catch ex As Exception
                'SQL problem? carry on
            Finally
                cmdSQL.Dispose()
            End Try


            Return True
        End Function
    End Class

End Namespace
