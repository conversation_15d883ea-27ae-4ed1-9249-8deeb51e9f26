Imports System.IO
Imports System.Net.Mail
Imports System.Text
Imports System.Configuration
Imports System.Web.Script.Serialization
Imports System.Net

Namespace WebServicesAddons

    Public Module AmazonSES
        'Old SIGv2 Credentials
        'Private Const sUserName As String = "AKIAI7NLY4LMJRGFFIJQ"
        'Private Const sPassword As String = "AjNRrKualXwJ+6LL+5E0EhxORghjI9HdCmlFHR5Co0Re"
        'New (2021-04-26) SIGv4 Credentials
        Private Const sUserName As String = "AKIARMCEAZZYPOREQ3XV"
        Private Const sPassword As String = "BGSZ1YVE0rdYHMxAZv5w+o7J+Yu6lIuKadHT0Hru2q6r"
        Private Const sHost As String = "email-smtp.us-east-1.amazonaws.com"
        Private Const nPort As Integer = 25


        <Obsolete("Use AmazonUtilities.Mail.SimpleEmailSystem.SendMail instead")> Public Sub SendMail(ByVal vsRecipient As String, ByVal vsSubject As String, ByVal vsBody As String, Optional ByVal vbSendAsHTML As Boolean = True, Optional ByVal sSender As String = "<EMAIL>")
            Using smtpSESClient As New SmtpClient(sHost, nPort)

                Dim sEmailAddresses As String() = Split(vsRecipient, ";")

                Dim msgSESEmail As New MailMessage
                msgSESEmail.Body = vsBody
                msgSESEmail.IsBodyHtml = vbSendAsHTML
                msgSESEmail.Subject = vsSubject
                msgSESEmail.From = New MailAddress(sSender)

                For Each sEmail As String In sEmailAddresses

                    If Len(sEmail.Trim) > 0 Then
                        msgSESEmail.To.Add(sEmail.Trim)
                    End If

                Next

                smtpSESClient.Credentials = New System.Net.NetworkCredential(sUserName, sPassword)
                smtpSESClient.EnableSsl = True
                smtpSESClient.Port = nPort
                smtpSESClient.Send(msgSESEmail)

            End Using
        End Sub

        'Overloaded - Include attachments as String
        Public Sub SendMail(ByVal vsRecipient As String, ByVal vsSubject As String, ByVal vsBody As String, ByVal vsAttachmentString As String, Optional ByVal vbSendAsHTML As Boolean = True, Optional ByVal sSender As String = "<EMAIL>")

            If File.Exists(vsAttachmentString) Then
                Dim fsFile As FileStream = File.OpenRead(vsAttachmentString)
                SendMail(vsRecipient, vsSubject, vsBody, fsFile, vbSendAsHTML, sSender)
                fsFile.Dispose()
            Else
                SendMail(vsRecipient, vsSubject, vsBody, vbSendAsHTML, sSender)
            End If

        End Sub

        'Overloaded - Include attachments as FileStream
        Public Sub SendMail(ByVal vsRecipient As String, ByVal vsSubject As String, ByVal vsBody As String, ByVal vfsAttachmentStream As FileStream, Optional ByVal vbSendAsHTML As Boolean = True, Optional ByVal sSender As String = "<EMAIL>", Optional ByVal sAttachmentName As String = "")
            Using smtpSESClient As New SmtpClient(sHost, nPort)

                Dim sEmailAddresses As String() = Split(vsRecipient, ";")

                Dim msgSESEmail As New MailMessage
                msgSESEmail.Body = vsBody
                msgSESEmail.IsBodyHtml = vbSendAsHTML
                msgSESEmail.Subject = vsSubject
                msgSESEmail.From = New MailAddress(sSender)

                If Not IsNothing(vfsAttachmentStream) AndAlso vfsAttachmentStream.Length > 0 Then

                    Dim uriAttachmentLocation As New Uri(vfsAttachmentStream.Name)

                    If sAttachmentName = String.Empty Then
                        sAttachmentName = uriAttachmentLocation.Segments.Last.ToString
                    End If
                    Dim attFile As Attachment = New Attachment(vfsAttachmentStream, sAttachmentName)
                    msgSESEmail.Attachments.Add(attFile)

                End If

                For Each sEmail As String In sEmailAddresses

                    If Len(sEmail.Trim) > 0 Then
                        msgSESEmail.To.Add(sEmail.Trim)
                    End If

                Next

                smtpSESClient.Credentials = New System.Net.NetworkCredential(sUserName, sPassword)
                smtpSESClient.EnableSsl = True
                smtpSESClient.Port = nPort
                smtpSESClient.Send(msgSESEmail)

            End Using
        End Sub

    End Module

    Public Module SendGridEmailService

        Private ReadOnly Property ApiKey As String
            Get
                Return ConfigurationManager.AppSettings("SendGrid-ApiKey")
            End Get
        End Property

        Private ReadOnly Property FromEmail As String
            Get
                Return ConfigurationManager.AppSettings("SendGrid-FromEmail")
            End Get
        End Property

        Private ReadOnly Property FromName As String
            Get
                Return ConfigurationManager.AppSettings("SendGrid-FromName")
            End Get
        End Property

        ''' <summary>
        ''' Send email using SendGrid API
        ''' </summary>
        ''' <param name="vsRecipient">Recipient email address(es), separated by semicolon</param>
        ''' <param name="vsSubject">Email subject</param>
        ''' <param name="vsBody">Email body content</param>
        ''' <param name="vbSendAsHTML">Whether to send as HTML (default: True)</param>
        ''' <param name="sSender">Sender email address (optional, uses config default)</param>
        ''' <returns>True if successful, False otherwise</returns>
        Public Function SendMail(ByVal vsRecipient As String, ByVal vsSubject As String, ByVal vsBody As String, Optional ByVal vbSendAsHTML As Boolean = True, Optional ByVal sSender As String = "") As Boolean
            Try
                If String.IsNullOrEmpty(ApiKey) Then
                    Throw New InvalidOperationException("SendGrid API key not configured")
                End If

                If String.IsNullOrEmpty(vsRecipient) Then
                    Throw New ArgumentException("Recipient email address is required")
                End If

                ' Use configured sender if not provided
                If String.IsNullOrEmpty(sSender) Then
                    sSender = FromEmail
                End If

                ' Parse recipients
                Dim recipients As String() = vsRecipient.Split(";"c)
                Dim toList As New List(Of Object)

                For Each recipient As String In recipients
                    Dim trimmedRecipient As String = recipient.Trim()
                    If Not String.IsNullOrEmpty(trimmedRecipient) Then
                        toList.Add(New With {.email = trimmedRecipient})
                    End If
                Next

                If toList.Count = 0 Then
                    Throw New ArgumentException("No valid recipient email addresses found")
                End If

                ' Create SendGrid email object
                Dim emailData = New With {
                    .personalizations = New Object() {
                        New With {
                            .to = toList.ToArray()
                        }
                    },
                    .from = New With {
                        .email = sSender,
                        .name = FromName
                    },
                    .subject = vsSubject,
                    .content = New Object() {
                        New With {
                            .type = If(vbSendAsHTML, "text/html", "text/plain"),
                            .value = vsBody
                        }
                    }
                }

                ' Serialize to JSON
                Dim serializer As New JavaScriptSerializer()
                Dim jsonContent As String = serializer.Serialize(emailData)

                ' Send via SendGrid API
                Return SendEmailViaSendGrid(jsonContent)

            Catch ex As Exception
                ' Log error (you might want to implement proper logging)
                System.Diagnostics.Debug.WriteLine($"SendGrid email error: {ex.Message}")
                Return False
            End Try
        End Function

        ''' <summary>
        ''' Send email with file attachment using SendGrid API
        ''' </summary>
        ''' <param name="vsRecipient">Recipient email address(es)</param>
        ''' <param name="vsSubject">Email subject</param>
        ''' <param name="vsBody">Email body content</param>
        ''' <param name="vfsAttachmentStream">File stream for attachment</param>
        ''' <param name="sAttachmentName">Name for the attachment</param>
        ''' <param name="vbSendAsHTML">Whether to send as HTML</param>
        ''' <param name="sSender">Sender email address</param>
        ''' <returns>True if successful, False otherwise</returns>
        Public Function SendMail(ByVal vsRecipient As String, ByVal vsSubject As String, ByVal vsBody As String, ByVal vfsAttachmentStream As FileStream, Optional ByVal sAttachmentName As String = "", Optional ByVal vbSendAsHTML As Boolean = True, Optional ByVal sSender As String = "") As Boolean
            Try
                If String.IsNullOrEmpty(ApiKey) Then
                    Throw New InvalidOperationException("SendGrid API key not configured")
                End If

                If String.IsNullOrEmpty(vsRecipient) Then
                    Throw New ArgumentException("Recipient email address is required")
                End If

                ' Use configured sender if not provided
                If String.IsNullOrEmpty(sSender) Then
                    sSender = FromEmail
                End If

                ' Parse recipients
                Dim recipients As String() = vsRecipient.Split(";"c)
                Dim toList As New List(Of Object)

                For Each recipient As String In recipients
                    Dim trimmedRecipient As String = recipient.Trim()
                    If Not String.IsNullOrEmpty(trimmedRecipient) Then
                        toList.Add(New With {.email = trimmedRecipient})
                    End If
                Next

                If toList.Count = 0 Then
                    Throw New ArgumentException("No valid recipient email addresses found")
                End If

                ' Prepare attachment if provided
                Dim attachments As New List(Of Object)
                If vfsAttachmentStream IsNot Nothing AndAlso vfsAttachmentStream.Length > 0 Then
                    ' Read file content and encode as base64
                    Dim fileBytes(vfsAttachmentStream.Length - 1) As Byte
                    vfsAttachmentStream.Read(fileBytes, 0, fileBytes.Length)
                    Dim base64Content As String = Convert.ToBase64String(fileBytes)

                    ' Get filename from stream or use provided name
                    If String.IsNullOrEmpty(sAttachmentName) AndAlso Not String.IsNullOrEmpty(vfsAttachmentStream.Name) Then
                        Dim uri As New Uri(vfsAttachmentStream.Name)
                        sAttachmentName = uri.Segments.Last()
                    End If

                    If String.IsNullOrEmpty(sAttachmentName) Then
                        sAttachmentName = "attachment.pdf"
                    End If

                    attachments.Add(New With {
                        .content = base64Content,
                        .filename = sAttachmentName,
                        .type = "application/pdf",
                        .disposition = "attachment"
                    })
                End If

                ' Create SendGrid email object with attachment
                Dim emailData = New With {
                    .personalizations = New Object() {
                        New With {
                            .to = toList.ToArray()
                        }
                    },
                    .from = New With {
                        .email = sSender,
                        .name = FromName
                    },
                    .subject = vsSubject,
                    .content = New Object() {
                        New With {
                            .type = If(vbSendAsHTML, "text/html", "text/plain"),
                            .value = vsBody
                        }
                    },
                    .attachments = attachments.ToArray()
                }

                ' Serialize to JSON
                Dim serializer As New JavaScriptSerializer()
                Dim jsonContent As String = serializer.Serialize(emailData)

                ' Send via SendGrid API
                Return SendEmailViaSendGrid(jsonContent)

            Catch ex As Exception
                ' Log error (you might want to implement proper logging)
                System.Diagnostics.Debug.WriteLine($"SendGrid email error: {ex.Message}")
                Return False
            End Try
        End Function

        ''' <summary>
        ''' Send email with file attachment using file path
        ''' </summary>
        Public Function SendMail(ByVal vsRecipient As String, ByVal vsSubject As String, ByVal vsBody As String, ByVal vsAttachmentPath As String, Optional ByVal vbSendAsHTML As Boolean = True, Optional ByVal sSender As String = "") As Boolean
            If File.Exists(vsAttachmentPath) Then
                Using fsFile As FileStream = File.OpenRead(vsAttachmentPath)
                    Dim fileName As String = Path.GetFileName(vsAttachmentPath)
                    Return SendMail(vsRecipient, vsSubject, vsBody, fsFile, fileName, vbSendAsHTML, sSender)
                End Using
            Else
                Return SendMail(vsRecipient, vsSubject, vsBody, vbSendAsHTML, sSender)
            End If
        End Function

        ''' <summary>
        ''' Internal method to send email via SendGrid API
        ''' </summary>
        Private Function SendEmailViaSendGrid(jsonContent As String) As Boolean
            Try
                Using client As New WebClient()
                    ' Set headers
                    client.Headers.Add("Authorization", "Bearer " & ApiKey)
                    client.Headers.Add("User-Agent", "SBOnet/1.0")
                    client.Headers.Add("Content-Type", "application/json")

                    ' Convert JSON string to byte array
                    Dim data As Byte() = Encoding.UTF8.GetBytes(jsonContent)

                    ' Send POST request
                    Dim response As Byte() = client.UploadData("https://api.sendgrid.com/v3/mail/send", "POST", data)

                    ' If we get here without exception, the request was successful
                    Return True
                End Using
            Catch ex As Exception
                System.Diagnostics.Debug.WriteLine($"SendGrid API error: {ex.Message}")
                Return False
            End Try
        End Function

    End Module

End Namespace