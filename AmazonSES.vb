﻿Imports System.IO
Imports System.Net.Mail

Namespace WebServicesAddons

    Public Module AmazonSES
        'Old SIGv2 Credentials
        'Private Const sUserName As String = "AKIAI7NLY4LMJRGFFIJQ"
        'Private Const sPassword As String = "AjNRrKualXwJ+6LL+5E0EhxORghjI9HdCmlFHR5Co0Re"
        'New (2021-04-26) SIGv4 Credentials
        Private Const sUserName As String = "AKIARMCEAZZYPOREQ3XV"
        Private Const sPassword As String = "BGSZ1YVE0rdYHMxAZv5w+o7J+Yu6lIuKadHT0Hru2q6r"
        Private Const sHost As String = "email-smtp.us-east-1.amazonaws.com"
        Private Const nPort As Integer = 25


        <Obsolete("Use AmazonUtilities.Mail.SimpleEmailSystem.SendMail instead")> Public Sub SendMail(ByVal vsRecipient As String, ByVal vsSubject As String, ByVal vsBody As String, Optional ByVal vbSendAsHTML As Boolean = True, Optional ByVal sSender As String = "<EMAIL>")
            Using smtpSESClient As New SmtpClient(sHost, nPort)

                Dim sEmailAddresses As String() = Split(vsRecipient, ";")

                Dim msgSESEmail As New MailMessage
                msgSESEmail.Body = vsBody
                msgSESEmail.IsBodyHtml = vbSendAsHTML
                msgSESEmail.Subject = vsSubject
                msgSESEmail.From = New MailAddress(sSender)

                For Each sEmail As String In sEmailAddresses

                    If Len(sEmail.Trim) > 0 Then
                        msgSESEmail.To.Add(sEmail.Trim)
                    End If

                Next

                smtpSESClient.Credentials = New System.Net.NetworkCredential(sUserName, sPassword)
                smtpSESClient.EnableSsl = True
                smtpSESClient.Port = nPort
                smtpSESClient.Send(msgSESEmail)

            End Using
        End Sub

        'Overloaded - Include attachments as String
        Public Sub SendMail(ByVal vsRecipient As String, ByVal vsSubject As String, ByVal vsBody As String, ByVal vsAttachmentString As String, Optional ByVal vbSendAsHTML As Boolean = True, Optional ByVal sSender As String = "<EMAIL>")

            If File.Exists(vsAttachmentString) Then
                Dim fsFile As FileStream = File.OpenRead(vsAttachmentString)
                SendMail(vsRecipient, vsSubject, vsBody, fsFile, vbSendAsHTML, sSender)
                fsFile.Dispose()
            Else
                SendMail(vsRecipient, vsSubject, vsBody, vbSendAsHTML, sSender)
            End If

        End Sub

        'Overloaded - Include attachments as FileStream
        Public Sub SendMail(ByVal vsRecipient As String, ByVal vsSubject As String, ByVal vsBody As String, ByVal vfsAttachmentStream As FileStream, Optional ByVal vbSendAsHTML As Boolean = True, Optional ByVal sSender As String = "<EMAIL>", Optional ByVal sAttachmentName As String = "")
            Using smtpSESClient As New SmtpClient(sHost, nPort)

                Dim sEmailAddresses As String() = Split(vsRecipient, ";")

                Dim msgSESEmail As New MailMessage
                msgSESEmail.Body = vsBody
                msgSESEmail.IsBodyHtml = vbSendAsHTML
                msgSESEmail.Subject = vsSubject
                msgSESEmail.From = New MailAddress(sSender)

                If Not IsNothing(vfsAttachmentStream) AndAlso vfsAttachmentStream.Length > 0 Then

                    Dim uriAttachmentLocation As New Uri(vfsAttachmentStream.Name)

                    If sAttachmentName = String.Empty Then
                        sAttachmentName = uriAttachmentLocation.Segments.Last.ToString
                    End If
                    Dim attFile As Attachment = New Attachment(vfsAttachmentStream, sAttachmentName)
                    msgSESEmail.Attachments.Add(attFile)

                End If

                For Each sEmail As String In sEmailAddresses

                    If Len(sEmail.Trim) > 0 Then
                        msgSESEmail.To.Add(sEmail.Trim)
                    End If

                Next

                smtpSESClient.Credentials = New System.Net.NetworkCredential(sUserName, sPassword)
                smtpSESClient.EnableSsl = True
                smtpSESClient.Port = nPort
                smtpSESClient.Send(msgSESEmail)

            End Using
        End Sub

    End Module

End Namespace