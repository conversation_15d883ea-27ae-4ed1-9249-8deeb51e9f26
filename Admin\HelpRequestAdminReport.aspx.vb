﻿Option Explicit On

Imports iTextSharp.text
Imports iTextSharp.text.pdf
Imports Obout.Grid
Imports System.Data.SqlClient
Imports System.IO
Imports AmazonUtilities.Mail.SimpleEmailService
Imports WebServicesAddons.SendGridEmailService
Imports System.Configuration

'2021-05-06 jjc Sprint79w66 SBOD-2223 Changing email from using WebServicesAddons.AmazonSES.SendMail to AmazonUtilities.Mail.SimpleEmailService.SendMail
'2021-06-07 jjs Sprint79w67 SBOD-2254 Updating <NAME_EMAIL> to <EMAIL>
'2025-04-17 mari Updated email addresses to use configuration values with @ncrvoyix.com domain

Partial Class HelpRequestAdminReport
    Inherits System.Web.UI.Page
    Dim InvWSS As InventoryWebSiteServices.InventoryServices

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        InvWSS = Session("IWSS")

        If Not IsPostBack Then

            PopOrgList()
            SetDefaultOptions()

        End If

        'Set the sub title
        lblSubTitle.Text = tbStartDate.Text & " - " & tbEndDate.Text

    End Sub

    Sub PopOrgList()

        Dim sbSQL As New StringBuilder
        Dim tabOrgs As New DataTable

        With sbSQL

            .AppendLine("SELECT")
            .AppendLine("    tbOD.[Server]")
            .AppendLine("	,REPLACE(tbOD.[LocalDatabaseName], 'SBONet_', '') AS [OrgName]")
            .AppendLine("FROM")
            .AppendLine("	[dbo].[tbOrganizationDatabase] AS [tbOD] WITH(NOLOCK)")
            .AppendLine("	INNER JOIN [dbo].[tbOrganization] AS [tbO] WITH(NOLOCK)")
            .AppendLine("		ON tbOD.[OrganizationNumber] = tbO.[OrganizationNumber]")
            .AppendLine("WHERE")
            .AppendLine("	tbOD.[LocalDatabaseName] LIKE '%SBONet_%'")
            If True
                .AppendLine("	AND (tbO.[AllowAudit] = 1 OR tbOD.[LocalDatabaseName] = 'SBONet_LukeTest')")
            Else
                .AppendLine("	AND (tbO.[AllowAudit] = 1 OR tbOD.[LocalDatabaseName] = 'SBONet_LukeTest' OR tbOD.[LocalDatabaseName] = 'SBONet_Sandbox')")
            End If
            .AppendLine("ORDER BY")
            .AppendLine("   tbOD.[LocalDatabaseName]")

        End With

        RunCommand(sbSQL.ToString, BuildDBConnectString("SQL-CLU01", "SBORoot", "sboapplication", "SoftwareA43A"), True, tabOrgs)

        For Each row As DataRow In tabOrgs.Rows
            cblOrgs.Items.Add(New System.Web.UI.WebControls.ListItem(row("OrgName"), row("Server")))
        Next

    End Sub

    Sub SetDefaultOptions()

        'Date
        Dim curDate As Date = Now.ToShortDateString
        tbStartDate.Text = curDate
        tbEndDate.Text = curDate

        'Request Type
        cblTypes.Items.FindByValue("WS").Selected = True

        'Show
        rblShow.Items.FindByValue("unresolved").Selected = True

        'Populate the email editor with a default message
        SetDefaultEditorContent()

    End Sub

    Sub SetDefaultEditorContent()

        Dim msg As New StringBuilder

        With msg

            .AppendLine("<p>Hello,</p>")
            .AppendLine("<p>The below request has been resolved.</p>")
            .AppendLine("")
            .AppendLine("<p>Please let us know if you require further assistance.</p>")
            .AppendLine("")
            .AppendLine("<p>Thank You,<br />Web Services</p>")

        End With

        oEditor.EditPanel.Content = msg.ToString()
        tbCC.Text = ""

    End Sub

    Sub BindGrid()

        Dim sbSQL As New StringBuilder
        Dim tabResults As New DataTable

        With sbSQL

            .AppendLine("SELECT")
            .AppendLine("    REPLACE(DB_NAME(), 'SBONet_', '') AS [Database]")
            .AppendLine("   ,@@SERVERNAME AS [Server]")
            .AppendLine("	,tmHR.[HelpRequestID]")
            .AppendLine("	,tmHR.[Subject]")
            .AppendLine("	,tmHR.[Detail]")
            .AppendLine("	,REPLACE(tmHR.[PageAddedFrom], 'https://www.dumacwebservices.com/webinventoryproduction400/', '') AS [PageAddedFrom]")
            .AppendLine("	,[dbo].[pf_FetchAccountName](tmHR.[Requestor]) AS [Requestor]")
            .AppendLine("   ,tmHR.[RequestorPhone]")
            .AppendLine("	,tmHR.[RequestorPhoneIsCell]")
            .AppendLine("   ,'Corp ID: ' + ISNULL(CAST(tmSI.[PortalSiteNumber] AS VARCHAR(25)), ' ') + ' || ' + 'Store ID: ' + ISNULL(CAST(tmHR.[StoreID] AS VARCHAR(25)), ' ') + ' || ' + 'Store Num: ' + ISNULL(CAST(tmS.[StoreNum] AS VARCHAR(25)), ' ') + ' || ' + 'Store Desc: ' + ISNULL(tmS.[StoreDescription], ' ') AS [StoreInfo]")
            .AppendLine("	,tmHR.[ContactName]")
            .AppendLine("	,tmHR.[ContactPhone]")
            .AppendLine("	,tmHR.[ContactPhoneIsCell]")
            .AppendLine("	,tmHR.[EmailAddress]")
            .AppendLine("   ,FORMAT(tmHR.[Birthday], 'M/d/yyyy hh:mm tt') AS [Birthday]")
            .AppendLine("	,FORMAT(tmHR.[DateResolved], 'M/d/yyyy hh:mm tt') AS [DateResolved]")
            .AppendLine("   ,[dbo].[pf_FetchAccountName](tmHR.[Thumbprint]) AS [Thumbprint]")
            .AppendLine("   ,FORMAT(tmHR.[LastModified], 'M/d/yyyy hh:mm tt') AS [LastModified]")
            .AppendLine("FROM")
            .AppendLine("	[dbo].[tmHelpRequest] AS [tmHR] WITH(NOLOCK)")
            .AppendLine("	INNER JOIN [dbo].[tmStore] AS [tmS]")
            .AppendLine("	    ON tmHR.[StoreID] = tmS.[StoreID]")
            .AppendLine("	INNER JOIN [dbo].[tmStoreInvt] AS [tmSI]")
            .AppendLine("	    ON tmHR.[StoreID] = tmSI.[StoreID]")
            .AppendLine("WHERE")
            .AppendLine("	tmHR.[Subject] <> ''")
            .AppendLine("   AND tmHR.[IsInHouse] = 0")
            .AppendLine("	AND CAST(tmHR.[Birthday] AS DATE) BETWEEN '" & tbStartDate.Text & "' AND '" & tbEndDate.Text & "'")
            .AppendLine("   AND tmHR.[RequestType] IN (" & GetRequestTypes() & ")")

            If rblShow.SelectedValue = "resolved" Then
                .AppendLine("   AND tmHR.[DateResolved] IS NOT NULL")
            ElseIf rblShow.SelectedValue = "unresolved" Then
                .AppendLine("   AND tmHR.[DateResolved] IS NULL")
            End If

        End With

        'Run query for all selected organizations
        For Each li As System.Web.UI.WebControls.ListItem In cblOrgs.Items

            If li.Selected Then
                RunCommand(sbSQL.ToString, BuildDBConnectString(li.Value, "SBONet_" + li.Text, "sboapplication", "SoftwareA43A"), True, tabResults)
            End If

        Next

        If tabResults.Rows.Count > 0 Then

            With grdHelpRequests
                .DataSource = tabResults.DefaultView
                .GroupBy = "Database"
                .DataBind()
            End With

            grid_container.Attributes("class") = ""
            btnPDF.Visible = True

        Else

            grid_container.Attributes("class") = "hideme"
            btnPDF.Visible = False

        End If

    End Sub

    Function GetRequestTypes() As String

        Dim requests As String = ""

        For Each li As System.Web.UI.WebControls.ListItem In cblTypes.Items

            If li.Selected Then
                requests += IIf(requests = "", "'", ", '") & li.Value & "'"
            End If

        Next

        Return requests

    End Function

    Sub btnSubmit_Click(sender As Object, e As EventArgs)

        BindGrid()

    End Sub

    Sub UpdateSelectedRecords(sender As Object, e As EventArgs)

        Dim btn As Button = DirectCast(sender, Button)
        Dim bSendEmail As Boolean = btn.CommandArgument.ToString()

        'Store the selected records into an ArrayList
        Dim selectedRecords As ArrayList = grdHelpRequests.SelectedRecords

        'If the ArrayList contains data --> Do something
        If Not selectedRecords Is Nothing Then

            For Each record As Hashtable In selectedRecords

                Dim sbSQL As New StringBuilder

                With sbSQL

                    .AppendLine("UPDATE")
                    .AppendLine("	[dbo].[tmHelpRequest]")
                    .AppendLine("SET")
                    .AppendLine("	[DateResolved] = GETDATE()")
                    .AppendLine("WHERE")
                    .AppendLine("	[HelpRequestID] = " & record("HelpRequestID"))

                End With

                'Execute the SQL command
                RunCommand(sbSQL.ToString, BuildDBConnectString(record("Server"), "SBONet_" & record("Database"), "sboapplication", "SoftwareA43A"))

                'Email the "Requestor"
                If bSendEmail Then
                    BuildEmail(record)
                End If

            Next

        Else

            MsgBox("No records were selected.", MsgBoxStyle.Information, "Error")

        End If

        'Reset the editor content message (in case the user sent a custom message)
        SetDefaultEditorContent()

        'Populate the grid
        BindGrid()

    End Sub

    Sub BuildEmail(ByVal record As Hashtable)

        'Build the default recipient list
        Dim recipients As String = record("EmailAddress") & "; " & ConfigurationManager.AppSettings("EmailAddress.SBOWebWork")
        'Dim recipients As String = record("EmailAddress")
        'Dim recipients As String = "<EMAIL>"


        'Get the list of user entered "CC" recipients (if any exist)
        Dim ccList() As String = Split(tbCC.Text.Replace(",", ";"), ";")

        For Each cc As String In ccList
            recipients += "; " & Trim(cc)
        Next

        'Get the content from the editor (the user's email message)
        Dim editorContent As String = oEditor.EditPanel.Content
        Dim arrLines() As String = Split(editorContent, vbCrLf)

        Dim sbMessage As New StringBuilder

        With sbMessage

            'Loop through the editor's content, and add each line to the StringBuilder
            For Each line As String In arrLines
                .AppendLine(line.Replace("'", "''"))
            Next

            'Append the requestor's original message
            .AppendLine("<hr />")
            .AppendLine("")
            .AppendLine("<span style=""color: #aaa; font-style: italic;"">" & Replace(record("Detail"), "'", "''") & "</span>")

        End With

        SendEmail(recipients, "RE: " & record("Subject"), sbMessage.ToString)

    End Sub

    Sub SendEmail(ByVal emailTo As String, ByVal emailSubject As String, ByVal emailBody As String)

        'Updated to use SendGrid instead of Amazon SES
        'WebServicesAddons.AmazonSES.SendMail(emailTo, emailSubject, emailBody.ToString)
        If Not WebServicesAddons.SendGridEmailService.SendMail(emailTo, emailSubject, emailBody.ToString) Then
            'Do something?
            Response.Headers.Add("X-SendGrid-Exception", "Error sending mail")
        End If

        'Dim sbSQL As New StringBuilder

        'With sbSQL
        '    .AppendLine("INSERT INTO")
        '    .AppendLine("   [eManager_App4].[dbo].[tmEmail]")
        '    .AppendLine("(")
        '    .AppendLine("    [To]")
        '    .AppendLine("   ,[From]")
        '    .AppendLine("   ,[DateCreated]")
        '    .AppendLine("   ,[DateSent]")
        '    .AppendLine("   ,[DateReceived]")
        '    .AppendLine("   ,[IsRead]")
        '    .AppendLine("   ,[CC]")
        '    .AppendLine("   ,[BCC]")
        '    .AppendLine("   ,[IsHTML]")
        '    .AppendLine("   ,[Subject]")
        '    .AppendLine("   ,[IsDeleted]")
        '    .AppendLine("   ,[ReplyTo]")
        '    .AppendLine("   ,[IsForwarded]")
        '    .AppendLine("   ,[IsReplied]")
        '    .AppendLine("   ,[SentBy]")
        '    .AppendLine("   ,[MessageID]")
        '    .AppendLine("   ,[IsAttachment]")
        '    .AppendLine("   ,[UniqueID]")
        '    .AppendLine("   ,[Body]")
        '    .AppendLine("   ,[HTML]")
        '    .AppendLine(")")
        '    .AppendLine("VALUES")
        '    .AppendLine("(")
        '    .AppendLine("    '" & emailTo & "'")
        '    .AppendLine("   ,'<EMAIL>'")
        '    .AppendLine("   ,'" & Now() & "'")
        '    .AppendLine("   ,'" & Now() & "'")
        '    .AppendLine("   ,'" & Now() & "'")
        '    .AppendLine("   ,0")
        '    .AppendLine("   ,''")
        '    .AppendLine("   ,''")
        '    .AppendLine("   ,1")
        '    .AppendLine("   ,'" & emailSubject & "'")
        '    .AppendLine("   ,0")
        '    .AppendLine("   ,'<EMAIL>'")
        '    .AppendLine("   ,0")
        '    .AppendLine("   ,0")
        '    .AppendLine("   ,'<EMAIL>'")
        '    .AppendLine("   ,'" & InvWSS.Platform.GetGUID() & "<EMAIL>'")
        '    .AppendLine("   ,0")
        '    .AppendLine("   ,NewID()")
        '    .AppendLine("   ,''")
        '    .AppendLine("   ,'" & emailBody & "'")
        '    .AppendLine(")")

        'End With

        'RunCommand(sbSQL.ToString, "data source=SQL-CLU01;initial catalog=eManager_App4;Password=Reporter001;persist security info=False;user id=sa;workstation id=ASP-WSS-3;packet size=4096")

    End Sub

    Sub Grid_ColumnsCreated(sender As Object, e As EventArgs)

        Dim grid As Obout.Grid.Grid = DirectCast(sender, Obout.Grid.Grid)

        'Loop through each column...
        For Each col As Obout.Grid.Column In grid.Columns

            'If the column is in the checkbox list
            If Not cblColumns.Items.FindByValue(col.DataField) Is Nothing Then

                'Set the visibility based on the selected value
                col.Visible = cblColumns.Items.FindByValue(col.DataField).Selected

            End If

        Next

        'Show/Hide the "Date Resolved" column based on the current "Report Options"
        With grid.Columns.GetColumnByDataField("DateResolved")

            Select Case rblShow.SelectedValue

                Case "unresolved"
                    .Visible = False

                Case Else
                    .Visible = True

            End Select

        End With

    End Sub

    Sub Grid_RowDataBound(sender As Object, e As GridRowEventArgs)

        Dim grid As Obout.Grid.Grid = DirectCast(sender, Obout.Grid.Grid)
        Dim row As Obout.Grid.GridRow = e.Row

        If row.RowType = GridRowType.DataRow Then

            Dim bRequestorIsCell As Boolean = row.DataItem("RequestorPhoneIsCell")
            Dim bContactIsCell As Boolean = row.DataItem("ContactPhoneIsCell")

            'Loop through each column...
            For Each col As Obout.Grid.Column In grid.Columns

                With row.Cells(col.Index)

                    Select Case col.DataField

                        Case "Detail"
                            .Text = "<span title=""" & row.DataItem("PageAddedFrom") & """>" & .Text & "</span>"

                        Case "StoreInfo"
                            .Text = Replace(.Text, " || ", "<br />")

                        Case "RequestorPhone"

                            Dim nbr As String = .Text
                            Dim formattedNbr As String = "<span title=""" & .Text & """>" & FormatPhoneNumber(nbr) & "</span>"

                            If bRequestorIsCell Then
                                formattedNbr += "<img src=""" & ResolveUrl("~/Images/iPhone.png") & """ Height=""14"" Width=""14"" class=""phone_img"" title=""Cell Phone"" />"
                            End If

                            .Text = formattedNbr

                        Case "ContactPhone"

                            Dim nbr As String = .Text
                            Dim formattedNbr As String = "<span title=""" & .Text & """>" & FormatPhoneNumber(nbr) & "</span>"

                            If bContactIsCell Then
                                formattedNbr += "<img src=""" & ResolveUrl("~/Images/iPhone.png") & """ Height=""14"" Width=""14"" class=""phone_img"" title=""Cell Phone"" />"
                            End If

                            .Text = formattedNbr

                    End Select

                End With

            Next

        End If

    End Sub

    'This function will format the "Requestor" and "Contact" phone numbers - On Error: Return the original number
    Function FormatPhoneNumber(ByVal nbr As String) As String

        Dim formattedNbr As String = Trim(nbr)

        formattedNbr = nbr.Replace(" ", "").Replace("-", "").Replace(".", "").Replace("(", "").Replace(")", "")

        If Not IsNumeric(formattedNbr) Then Return nbr

        nbr = CLng(formattedNbr).ToString("(###) ###-####")

        Return nbr

    End Function

    Sub PDF_Export(sender As Object, e As System.EventArgs)

        'Re-bind the grid, because the data clears out on postback
        BindGrid()

        'The grid
        Dim grid As Obout.Grid.Grid = grdHelpRequests

        'Stream which will be used to render the data
        Dim fileStream As MemoryStream = New MemoryStream()

        'Create Document class object and set its size to letter and give space left, right, Top, Bottom Margin
        Dim doc As Document = New Document(iTextSharp.text.PageSize.A4.Rotate(), 10, 10, 42, 35)

        Try

            Dim wri As PdfWriter = PdfWriter.GetInstance(doc, fileStream)

            'Open Document to write
            doc.Open()

            'Set fonts
            Dim fontTitle As Font = FontFactory.GetFont("Arial", 14, New BaseColor(51, 51, 51))
            Dim fontSubTitle As Font = FontFactory.GetFont("Arial", 10, New BaseColor(51, 51, 51))
            Dim fontBlack As Font = FontFactory.GetFont("Arial", 7, New BaseColor(51, 51, 51))
            Dim fontWhite As Font = FontFactory.GetFont("Arial", 7, New BaseColor(255, 255, 255))

            'Create the PDF title
            Dim pdfTitle As Paragraph = New Paragraph("Help Request Report", fontTitle)
            pdfTitle.Alignment = Element.ALIGN_CENTER

            'Add the PDF title to the document
            doc.Add(pdfTitle)

            'Create the PDF sub title
            Dim pdfSubTitle As Paragraph = New Paragraph(tbStartDate.Text & " - " & tbEndDate.Text, fontSubTitle)
            pdfSubTitle.Alignment = Element.ALIGN_CENTER

            'Add the PDF title to the document
            doc.Add(pdfSubTitle)

            'Count the number of visible columns, and build an array of column widths
            Dim numVisibleCols As Integer = 0
            Dim arrColWidths(numVisibleCols) As Integer

            For Each col As Column In grid.Columns
                If col.Visible = True AndAlso col.HeaderText <> "" Then
                    ReDim Preserve arrColWidths(numVisibleCols)
                    arrColWidths(numVisibleCols) = col.Width
                    numVisibleCols += 1
                End If
            Next

            'Create instance of the pdf table, and set the number of columns in it
            Dim PdfTable As PdfPTable = New PdfPTable(numVisibleCols)

            'Set the PDF table width, column widths, and spacing
            With PdfTable
                .WidthPercentage = 98
                .SetWidths(arrColWidths)
                .SpacingBefore = 15.0F
            End With

            'Create instance of the pdf table cell
            Dim PdfPCell As PdfPCell = Nothing

            'Add headers to the pdf table
            For Each col As Column In grid.Columns

                If col.Visible = True AndAlso col.HeaderText <> "" Then
                    PdfPCell = New PdfPCell(New Phrase(New Chunk(col.HeaderText, fontBlack)))
                    With PdfPCell
                        .BackgroundColor = New iTextSharp.text.BaseColor(220, 227, 232)
                        .Padding = 8.0F
                    End With
                    PdfTable.AddCell(PdfPCell)
                End If

            Next

            Dim db As String = ""

            For Each row As Obout.Grid.GridRow In grid.Rows

                'Create a "Hashtable" of the current row values
                Dim hashRow As Hashtable = row.ToHashtable()

                'Group Header
                If hashRow("Database").ToString() <> db Then

                    db = hashRow("Database").ToString()

                    PdfPCell = New PdfPCell(New Phrase(New Chunk(db, fontWhite)))
                    With PdfPCell
                        .Colspan = numVisibleCols
                        .Padding = 4.0F
                        .BackgroundColor = New iTextSharp.text.BaseColor(76, 76, 76)
                    End With
                    PdfTable.AddCell(PdfPCell)

                End If

                'Data
                For Each col As Column In grid.Columns

                    If col.Visible = True AndAlso col.HeaderText <> "" Then

                        Select Case col.DataField

                            Case "StoreInfo"

                                Dim arrInfo() As String = Split(hashRow(col.DataField).ToString, " || ")

                                Dim sbInfo As New StringBuilder
                                With sbInfo

                                    .AppendLine(arrInfo(0))
                                    .AppendLine(arrInfo(1))
                                    .AppendLine(arrInfo(2))
                                    .AppendLine(arrInfo(3))

                                End With

                                PdfPCell = New PdfPCell(New Phrase(New Chunk(sbInfo.ToString(), fontBlack)))

                            Case Else

                                PdfPCell = New PdfPCell(New Phrase(New Chunk(hashRow(col.DataField).ToString, fontBlack)))

                        End Select

                        With PdfPCell
                            .Padding = 3.0F
                            .VerticalAlignment = Element.ALIGN_MIDDLE
                        End With

                        PdfTable.AddCell(PdfPCell)

                    End If

                Next

            Next

            doc.Add(PdfTable)

        Catch docEx As DocumentException

            'Handle PDF document exception 

        Catch ioEx As IOException

            'Handle IO exception 

        Catch ex As Exception

            'Handle general exception  

        Finally

            'Close document and writer
            doc.Close()

        End Try

        'Send the data and the appropriate headers to the browser
        With Response
            .Clear()
            .AddHeader("content-disposition", "attachment;filename=" & GetExportFileName() & ".pdf")
            .ContentType = "application/pdf"
            .BinaryWrite(fileStream.ToArray())
            .End()
        End With

    End Sub

    Function GetExportFileName() As String

        Dim sDate As DateTime = DateTime.Parse(tbStartDate.Text)
        Dim eDate As DateTime = DateTime.Parse(tbEndDate.Text)

        'Set the default filename
        Dim fName As String = "HelpRequests_" & sDate.ToString("yyyyMMdd")

        'If the "Start Date" and "End Date" is different --> Append the "End Date" to the filename
        If sDate <> eDate Then
            fName = fName & "_" & eDate.ToString("yyyyMMdd")
        End If

        'Return the filename
        Return fName.ToString

    End Function

    Sub RunCommand(ByVal sql As String, ByVal conn As String, Optional ByVal bGetTable As Boolean = False, Optional ByRef dt As DataTable = Nothing)

        Dim SQLConn As New SqlConnection
        Dim SQLCmd As New SqlCommand
        Dim SQLAdapter As New SqlDataAdapter


        SQLConn = New SqlConnection(conn)
        SQLConn.Open()

        If bGetTable Then
            SQLAdapter = New SqlDataAdapter(sql, SQLConn)
            SQLAdapter.Fill(dt)
        Else
            SQLCmd = New SqlCommand(sql, SQLConn)
            SQLCmd.ExecuteNonQuery()
        End If

        SQLConn.Close()



        If Not SQLCmd Is Nothing Then
            SQLCmd.Dispose()
        End If

        If Not SQLAdapter Is Nothing Then
            SQLAdapter.Dispose()
        End If

        If Not SQLConn Is Nothing Then
            SQLConn.Dispose()
        End If


    End Sub

    Function BuildDBConnectString(Optional ByVal SQLServer As String = "", Optional ByVal SQLDatabase As String = "", Optional ByVal SQLUserID As String = "", Optional ByVal SQLPassword As String = "", Optional ByVal PersistSecurityInfo As Boolean = False) As String

        Dim sConnect As String = ""

        If Len(SQLServer) Then
            sConnect = sConnect & "Data Source=" & SQLServer & ";"
        End If
        If Len(SQLDatabase) Then
            sConnect = sConnect & "Database=" & SQLDatabase & ";"
        End If
        If Len(SQLUserID) Then
            sConnect = sConnect & "User ID=" & SQLUserID & ";"
        End If
        If Len(SQLPassword) Then
            sConnect = sConnect & "Password=" & SQLPassword & ";"
        End If
        If PersistSecurityInfo Then
            sConnect = sConnect & "Persist Security Info=True;"
        End If

        Return sConnect

    End Function

End Class
