﻿<%@ Page MasterPageFile="site.master" Language="VB" Debug="true" EnableViewState="true" AutoEventWireup="false" Inherits="Scheduler_LaborMatrixOptions" CodeFile="Scheduler_LaborMatrixOptions.aspx.vb" %>

<%--
2017-11-22 jjc Sprint 42 // B-03194 // TK-04250 Add Pre-Cut Bacon

--%>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" Runat="Server">

    <style>

        /* Global
        ----------------------------------------------- */
        #container {
            display: inline-block;
            margin: 0 10px 10px 10px;
            border: 2px solid #000;
            background-color: #fff;
            font-family: Verdana;
            text-align: left;
        }            

            .title {
                padding: 10px;
                border-bottom: 1px solid #000;
                background-color: #e6e6e6;            
                font-size: 16px;
                text-align: center;
            }

            .content {
                padding: 20px;
                font-size: 12px;
            }

                fieldset {
                    position: relative;
                    margin-bottom: 10px;
                    padding: 20px;
                }                

                    legend {
                        font-weight: bold;
                    }

                    /* Store Selection
                    ----------------------------------------------- */
                    #store_selection {
                        display: inline-block;
                        margin-right: 16px;
                        vertical-align: top;
                    }

                        #store_selection label {
                            display: inline-block;
                            margin-right: 16px;
                            width: 110px;
                            text-align: right;
                        }

                        #store_selection select,
                        #store_selection option {
                            padding: 6px 1px 5px 1px;
                            width: 240px;
                            font-size: 12px;
                        }

                    /* Use IPT Labor Matrix
                    ----------------------------------------------- */
                    #labor_matrix {
                        display: inline-block;
                        padding-top: 32px;
                        width: 215px;
                        height: 55px;
                        vertical-align: top;
                    }

                        .ipt_group {
                            display: block;
                            margin: 0 auto;
                            width: 200px;
                        }

                        .ipt_group input[type="button"] {
                            margin-right: 8px;
                            height: 28px;
                            width: 28px;
                            border: 1px solid #bbb;
                            border-radius: 50%;
                            background-color: #efefef;
                            line-height: 28px;
                            text-align: center;
                            cursor: pointer;
                        }

                        .ipt_active {
                            border-color: #111 !important;
                            background-color: #333 !important;
                            color: #fff !important;
                        }

                    /* Container
                    ----------------------------------------------- */
                    #left_container {
                        display: inline-block;
                        vertical-align: top;
                    }

                        /* Matrix Version
                        ----------------------------------------------- */
                        #matrix_version {
                            display: inline-block;
                            margin-right: 16px;
                            height: 70px;
                            vertical-align: top;
                        }

                            #matrix_version label {
                                display: inline-block;
                                margin-right: 16px;
                                width: 110px;
                                text-align: right;
                            }

                            #matrix_version select,
                            #matrix_version option {
                                padding: 6px 1px 5px 1px;
                                width: 240px;
                                font-size: 12px;
                                height: 30px;
                            }

                        /* Restaurant Information
                        ----------------------------------------------- */
                        #restaurant_information {
                            display: block;
                            margin-right: 16px;
                            height: 430px;
                            vertical-align: top;
                        }

                            #restaurant_information label {
                                display: inline-block;
                                margin-right: 16px;
                                width: 110px;
                                text-align: right;
                            }

                            #restaurant_information select,
                            #restaurant_information option {
                                padding: 6px 1px 5px 1px;
                                width: 240px;
                                font-size: 12px;
                                height: 30px;
                            }

                    /* Container
                    ----------------------------------------------- */
                    #right_container {
                        display: inline-block;
                        vertical-align: top;
                    }

                        /* Productivity Enhancements
                        ----------------------------------------------- */
                        #productivity_enhancements { /*...*/ }
                    
                            #productivity_enhancements input {
                                display: inline-block;
                                margin-right: 16px;
                                height: 18px;
                                width: 18px;
                                vertical-align: middle;
                            }

                            #productivity_enhancements label {
                                font-size: 12px;
                            }

                        /* Exchange Rate
                        ----------------------------------------------- */
                        #exchange_rate { /*...*/ }

                            #exchange_rate input {
                                display: block;
                                margin: 0 auto;
                                height: 29px;
                                width: 80px;
                                text-align: center;
                            }

                    /* Crew Productivity
                    ----------------------------------------------- */

                    #crew_productivity {
                        height: 390px;
                        vertical-align: top;
                    }

                        #crew_productivity label {
                            display: inline-block;
                            margin-right: 16px;
                            width: 160px;
                            text-align: right;
                        }

                        #crew_productivity select,
                        #crew_productivity option {
                            padding: 6px 1px 5px 1px;
                            width: 240px;
                            font-size: 12px;
                            height: 30px;
                        }

                    /*#crew_productivity { }

                        .dp_group p {
                            display: inline-block;
                            margin-right: 16px;
                            width: 100px;
                            text-align: right;
                        }

                        .dp_group input[type="button"] {
                            margin-right: 8px;
                            padding-top: 3px;
                            height: 28px;
                            width: 28px;
                            border: 1px solid #bbb;
                            border-radius: 50%;
                            background-color: #efefef;
                            line-height: 28px;
                            text-align: center;
                            cursor: pointer;
                        }

                        .dp_active {
                            border-color: #111 !important;
                            background-color: #333 !important;
                            color: #fff !important;
                        }*/

                    

                #client_submit {
                    display: block;
                    margin: 20px auto 0 auto;
                }

        #site_overlay {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: #000;
            opacity: 0.3;
            z-index: 500;
        }

    </style>

    <form runat="server" id="Form1">

        <div id="container">

            <div class="title">Labor Matrix Options</div>
            
            <div class="content">

                <fieldset id="store_selection">

                    <legend>Store Selection</legend>

                    <p>
                        <label for="ddlStore">Store Name</label>
                        <asp:DropDownList ID="ddlStore" runat="server" AutoPostBack="true" OnSelectedIndexChanged="StoreSelectionChanged"></asp:DropDownList>
                    </p>

                </fieldset>

                <fieldset id="labor_matrix">

                    <legend>Use IPT Labor Matrix</legend>

                    <div class="ipt_group">

                        <asp:CheckBox ID="cbUseDSS" runat="server" readonly="true" Text="Use Staff Scheduler" /><br />
                        <asp:CheckBox ID="cbUseIPT" runat="server" readonly="true" Text="Use IPT Labor Matrix"/>
                        

                    </div>

                </fieldset>

                <br />

                <div id="left_container">

                    <fieldset id="matrix_version">

                        <legend>Matrix Version</legend>

                        <%-- Counter Type --%>
                        <p>
                            <label for="ddlMatrixVersion">Matrix Version</label>
                            <asp:DropDownList ID="ddlMatrixVersion" runat="server" AutoPostBack="true" OnSelectedIndexChanged="MatrixSelectionChanged">
                                <asp:ListItem Value="-1">Please Select...</asp:ListItem>
                            </asp:DropDownList>
                        </p>

                    </fieldset>
                
                    <fieldset id="restaurant_information">

                        <legend>Restaurant Information</legend>

                        <%-- Counter Type --%>
                        <p>
                            <label for="ddlCounterType">Counter Type</label>
                            <asp:DropDownList ID="ddlCounterType" runat="server">
                                <asp:ListItem Value="-1">Please Select...</asp:ListItem>
                            </asp:DropDownList>
                        </p>

                        <%-- PUW Type --%>
                        <p>
                            <label for="ddlPUWType">PUW Type</label>
                            <asp:DropDownList ID="ddlPUWType" runat="server">
                                <asp:ListItem Value="-1">Please Select...</asp:ListItem>
                            </asp:DropDownList>
                        </p>

                        <%-- Kitchen --%>
                        <p>
                            <label for="ddlKitchen">Kitchen</label>
                            <asp:DropDownList ID="ddlKitchen" runat="server">
                                <asp:ListItem Value="-1">Please Select...</asp:ListItem>
                            </asp:DropDownList>
                        </p>

                        <%-- Dining Room --%>
                        <p>
                            <label for="ddlDiningRoom">Dining Room</label>
                            <asp:DropDownList ID="ddlDiningRoom" runat="server">
                                <asp:ListItem Value="-1">Please Select...</asp:ListItem>
                            </asp:DropDownList>
                        </p>

                        <%-- Flooring --%>
                        <p>
                            <label for="ddlFlooring">Flooring</label>
                            <asp:DropDownList ID="ddlFlooring" runat="server">
                                <asp:ListItem Value="-1">Please Select...</asp:ListItem>
                            </asp:DropDownList>
                        </p>

                        <%-- Drinks --%>
                        <p>
                            <label for="ddlDrinks">Drinks</label>
                            <asp:DropDownList ID="ddlDrinks" runat="server">
                                <asp:ListItem Value="-1">Please Select...</asp:ListItem>
                            </asp:DropDownList>
                        </p>

                        <%-- Grill --%>
                        <p>
                            <label for="ddlGrill">Grill</label>
                            <asp:DropDownList ID="ddlGrill" runat="server">
                                <asp:ListItem Value="-1">Please Select...</asp:ListItem>
                            </asp:DropDownList>
                        </p>

                        <%-- Kiosk --%>
                        <div id="divKiosk" runat="server">
                            <p>
                                <label for="ddlKiosk">Kiosk</label>
                                <asp:DropDownList ID="ddlKiosk" runat="server">
                                    <asp:ListItem Value="-1">Please Select...</asp:ListItem>
                                </asp:DropDownList>
                            </p>
                        </div>

                        <%-- Kiosk --%>
                        <div id="divDigital" runat="server">
                            <p>
                                <label for="ddlDigital">Digital</label>
                                <asp:DropDownList ID="ddlDigital" runat="server">
                                    <asp:ListItem Value="-1">Please Select...</asp:ListItem>
                                </asp:DropDownList>
                            </p>
                        </div>

                        <%-- Digital Type --%>
                        <div id="divDigitalType" runat="server">
                            <p>
                                <label for="ddlDigitalType">Digital Type</label>
                                <asp:DropDownList ID="ddlDigitalType" runat="server">
                                    <asp:ListItem Value="-1">Please Select...</asp:ListItem>
                                </asp:DropDownList>
                            </p>
                        </div>

                        <%-- Order Technology --%>
                        <div id="divOrderTechnology" runat="server">
                            <p>
                                <label for="ddlOrderTechnology">Order Technology</label>
                                <asp:DropDownList ID="ddlOrderTechnology" runat="server">
                                    <asp:ListItem Value="-1">Please Select...</asp:ListItem>
                                </asp:DropDownList>
                            </p>
                        </div>

                        <%-- DriveThruMix --%>
                        <div id="divDriveThruMix" runat="server">
                            <p>
                                <label for="ddlDriveThruMix">Drive Thru Mix</label>
                                <asp:DropDownList ID="ddlDriveThruMix" runat="server">
                                    <asp:ListItem Value="-1">Please Select...</asp:ListItem>
                                </asp:DropDownList>
                            </p>
                        </div>
                    </fieldset>

                </div>  

                <div id="right_container">

                    <fieldset id="exchange_rate">

                        <legend>Exchange Rate</legend>

                        <p>
                            <asp:TextBox ID="tbExchangeRate" runat="server">1.00</asp:TextBox>
                        </p>

                    </fieldset>

                    <fieldset id="productivity_enhancements">

                        <legend>Productivity Enhancements</legend>

                        <asp:CheckBoxList ID="cblProductivityEnhancements" runat="server">
                            <asp:ListItem Value="1" class="cbPowerSoak">Powersoak</asp:ListItem> 
                            <asp:ListItem Value="1" class="cbCashScales">Cash Scales</asp:ListItem>
                            <asp:ListItem Value="1" class="cbGreaseExtraction">Grease Extraction</asp:ListItem>
                            <asp:ListItem Value="1" class="cbBillValidationSafe">Bill Validation Safe</asp:ListItem>
                            <asp:ListItem Value="1" class="cbBackOfficeForecastPrep">Back-Office Forecast/Prep</asp:ListItem>
                            <asp:ListItem Value="1" class="cbBackOfficeInventory">Back-Office Inventory</asp:ListItem>
                            <asp:ListItem Value="1" class="cbBackOfficeLaborScheduler hideme">Back-Office Labor Scheduler</asp:ListItem>
                            <asp:ListItem Value="1" class="cbWareWash">WareWash</asp:ListItem>
                            <asp:ListItem Value="1" class="cbElectricSaladSpinner">Electric Salad Spinner</asp:ListItem>
                            <%--Holding off on Bacon per Wendys--%>
                            <%--<asp:ListItem Value="1">Pre Cut Bacon</asp:ListItem>--%>
                            <asp:ListItem Value="1" class="cbPrechoppedlettuce">Pre-chopped lettuce</asp:ListItem>
                            <asp:ListItem Value="1" class="cbPrepLabelPrinter">Prep Label Printer</asp:ListItem>
                            <asp:ListItem Value="1" class="cbOperationsTablet">Operations Tablet</asp:ListItem>
                            <asp:ListItem Value="1" class="cbLettuceTest">Lettuce Test</asp:ListItem>
                            <asp:ListItem Value="1" class="cbAmbientBacon">Ambient Bacon</asp:ListItem>
                        </asp:CheckBoxList>

                    </fieldset>

                    <%--jjc
                    <fieldset id="strange_situations">

                        <legend>Override</legend>

                        <asp:CheckBoxList ID="cblStrangeSituations" runat="server">
                            <asp:ListItem Value="1">Emergency DT Only Operations</asp:ListItem> 
                        </asp:CheckBoxList>

                    </fieldset>--%>

                </div>  
                              
                <fieldset id="crew_productivity">

                    <legend>Crew Productivity</legend>

                    <%-- Crew Productivity --%>

                    <p>
                        <label for="ddlProductivityDP1">Day Part 1</label>
                        <asp:DropDownList ID="ddlProductivityDP1" runat="server">
                            <asp:ListItem Value="-1">Please Select...</asp:ListItem>
                        </asp:DropDownList>
                    </p>

                    <p>
                        <label for="ddlProductivityDP23">Day Part 2 & 3</label>
                        <asp:DropDownList ID="ddlProductivityDP23" runat="server">
                            <asp:ListItem Value="-1">Please Select...</asp:ListItem>
                        </asp:DropDownList>
                    </p>

                    <p>
                        <label for="ddlProductivityDP45">Day Part 4 & 5</label>
                        <asp:DropDownList ID="ddlProductivityDP45" runat="server">
                            <asp:ListItem Value="-1">Please Select...</asp:ListItem>
                        </asp:DropDownList>
                    </p>

                    <p>
                        <label for="ddlProductivityDP6">Day Part 6</label>
                        <asp:DropDownList ID="ddlProductivityDP6" runat="server">
                            <asp:ListItem Value="-1">Please Select...</asp:ListItem>
                        </asp:DropDownList>
                    </p>

                    <p>
                        <label for="ddlProductivityDP1Wknd">Weekend Day Part 1</label>
                        <asp:DropDownList ID="ddlProductivityDP1Wknd" runat="server">
                            <asp:ListItem Value="-1">Please Select...</asp:ListItem>
                        </asp:DropDownList>
                    </p>

                    <p>
                        <label for="ddlProductivityDP23Wknd">Weekend Day Part 2 & 3</label>
                        <asp:DropDownList ID="ddlProductivityDP23Wknd" runat="server">
                            <asp:ListItem Value="-1">Please Select...</asp:ListItem>
                        </asp:DropDownList>
                    </p>

                    <p>
                        <label for="ddlProductivityDP45Wknd">Weekend Day Part 4 & 5</label>
                        <asp:DropDownList ID="ddlProductivityDP45Wknd" runat="server">
                            <asp:ListItem Value="-1">Please Select...</asp:ListItem>
                        </asp:DropDownList>
                    </p>

                    <p>
                        <label for="ddlProductivityDP6Wknd">Weekend Day Part 6</label>
                        <asp:DropDownList ID="ddlProductivityDP6Wknd" runat="server">
                            <asp:ListItem Value="-1">Please Select...</asp:ListItem>
                        </asp:DropDownList>
                    </p>

<%--                    <div class="dp_group">

                        <p>Day Part 1</p>

                        <input type="button" value="A" />
                        <input type="button" value="B" />
                        <input type="button" value="C" />

                        <asp:TextBox ID="tbDP1" runat="server" CssClass="hideme"></asp:TextBox>

                    </div>

                    <div class="dp_group">

                        <p>Day Part 2 & 3</p>

                        <input type="button" value="A" />
                        <input type="button" value="B" />
                        <input type="button" value="C" />

                        <asp:TextBox ID="tbDP2_3" runat="server" CssClass="hideme"></asp:TextBox>

                    </div>

                    <div class="dp_group">

                        <p>Day Part 4 & 5</p>

                        <input type="button" value="A" />
                        <input type="button" value="B" />
                        <input type="button" value="C" />

                        <asp:TextBox ID="tbDP4_5" runat="server" CssClass="hideme"></asp:TextBox>

                    </div>

                    <div class="dp_group">

                        <p>Day Part 6</p>

                        <input type="button" value="A" />
                        <input type="button" value="B" />
                        <input type="button" value="C" />

                        <asp:TextBox ID="tbDP6" runat="server" CssClass="hideme"></asp:TextBox>

                    </div>--%>


                </fieldset>

                <input type="button" id="client_submit" value="Submit" />
                <asp:Button ID="btnSubmit" runat="server" OnClick="btnSubmit_Click" CssClass="hideme" /> 
                <br />
                      
                <asp:Label ID="lblLastUpdate" runat="server" align="center" Width="100%"/>
            </div>

        </div>  
        
        <div id="site_overlay" class="hideme"></div>      

    </form>

    <%-- jQuery --%>
    <script src="JS/jquery-1.10.2.js"></script>

    <script type="text/javascript">

        ;(function ($) {
            "use strict";

            var $submit = $("#client_submit"),
                asp = {
                    $ddlStore: $("#<%=ddlStore.ClientID%>"),
                    $ddlCounterType: $("#<%=ddlCounterType.ClientID%>"),
                    $ddlPUWType: $("#<%=ddlPUWType.ClientID%>"),
                    $ddlKitchen: $("#<%=ddlKitchen.ClientID%>"),
                    $ddlDiningRoom: $("#<%=ddlDiningRoom.ClientID%>"),
                    $ddlFlooring: $("#<%=ddlFlooring.ClientID%>"),
                    $ddlDrinks: $("#<%=ddlDrinks.ClientID%>"),
                    $ddlGrill: $("#<%=ddlGrill.ClientID%>"),
                    $ddlOrderTechnology: $("#<%=ddlOrderTechnology.ClientID%>"),
                    $ddlDriveThruMix: $("#<%=ddlDriveThruMix.ClientID%>"),
                    $tbExchangeRate: $("#<%=tbExchangeRate.ClientID%>"),
                    $btnSubmit: $("#<%=btnSubmit.ClientID%>"),
                    $cblProductivityEnhancements: $("#<%=cblProductivityEnhancements.ClientID%>")
                },
                methods = {
                    init: function () {

                        // Attach event handlers
                        methods.attachEvents();

                        // If "IA tier 1/2 'L-Shaped' counter" is selected --> Disable/Select options
                        if (asp.$ddlCounterType.val() === "1") { //IA tier 1/2 "L-Shaped" counter
                            methods.disableDropDownsLCounter();
                        }
                        // If "Global Next Gen" is selected --> Disable/Select options
                        if ( asp.$ddlKitchen.val() === "3" ) { //Global Next Gen
                            methods.disableDropDownsNextGen();
                        }

                        // If "Drive Thru Only" is selected --> Disable/Select options
                        if ( asp.$ddlKitchen.val() === "7" ) { //Drive Thru Only
                            methods.disableDropDownsDTOnly();
                        }

                        // If "None" is selected for OrderTechnoloyg --> Disable/Select options
                        if ( asp.$ddlOrderTechnology.val() === "0" ) { //None
                            methods.disableDropDownsOrderTechnology();
                        }

                        // Set the "Use IPT Labor Matrix" value based on the value in the hidden <asp:Textbox> field
                        methods.setIPT();

                        // Set the "Crew Productivity" rankings based on the values in the hidden <asp:Textbox> fields
                        methods.setProductivityRanks();

                    },
                    attachEvents: function () {

                        // When the "Store Name" ddl selection changes...
                        asp.$ddlStore.on("change", function () {

                            // Show the "Please Wait" div
                            methods.pleaseWait();

                        });

                        // When one of the "Use IPT Labor Matrix" inputs are clicked...
                        $("#labor_matrix").on("click", "input", function () {

                            var $el = $(this),
                                $parent = $el.parent(),
                                $group_inputs = $parent.find('input[type="button"]'),
                                $group_asp_input = $parent.find('input[type="text"]');

                            // Remove the "Active" class from all of the inputs in the clicked input's group
                            $group_inputs.removeClass("ipt_active");

                            // Add the "Active" class to the clicked input
                            $el.addClass("ipt_active");

                            // Update the hidden <asp:Textbox> with the number that corresponds to the clicked input's value
                            switch ($el.prop("value")) {

                                case "Y":
                                    $group_asp_input.val("True");
                                    break;

                                default:
                                    $group_asp_input.val("False");

                            }

                        });

                        // When the "Counter Type" ddl selection changes...
                        asp.$ddlCounterType.on("change", function () {

                            // If "IA tier 1/2 'L-Shaped' counter" is selected --> Disable/Select options
                            // Else --> Enable/Default options
                            $(this).val() === "1"
                                ? methods.disableDropDownsLCounter()
                                : methods.enableDropDownsLCounter();

                        });

                        // When the "PUW Type" ddl selection changes...
                        asp.$ddlPUWType.on("change", function () {

                            // If "Drive Thru Only" is selected --> Disable/Select options
                            // Else --> Enable/Default options
                            $(this).val() === "7"
                                ? methods.disableDropDownsDTOnly()
                                : methods.enableDropDownsDTOnly();

                        });
                        
                        // When the "Kitchen Type" ddl selection changes...
                        asp.$ddlKitchen.on("change", function () {

                            // If "Global Next Gen" is selected --> Disable/Select options
                            // Else --> Enable/Default options
                            $(this).val() === "3"
                                ? methods.disableDropDownsNextGen()
                                : methods.enableDropDownsNextGen();

                        });

                        // When the "Order Technology" ddl selection changes...
                        asp.$ddlOrderTechnology.on("change", function () {

                            // If "Drive Thru Only" is selected --> Disable/Select options
                            // Else --> Enable/Default options
                            $(this).val() === "0"
                                ? methods.disableDropDownsOrderTechnology()
                                : methods.enableDropDownsOrderTechnology();

                        });

                        // When one of the "Crew Productivity" inputs are clicked...
                        $("#crew_productivity").on("click", "input", function () {

                            var $el = $(this),
                                $parent = $el.parent(),
                                $group_inputs = $parent.find('input[type="button"]'),
                                $group_asp_input = $parent.find('input[type="text"]');

                            // Remove the "Active" class from all of the inputs in the clicked input's group
                            $group_inputs.removeClass("dp_active");

                            // Add the "Active" class to the clicked input
                            $el.addClass("dp_active");

                            // Update the hidden <asp:Textbox> with the number that corresponds to the clicked input's value
                            switch ($el.prop("value")) {

                                case "A":
                                    $group_asp_input.val("1");
                                    break;

                                case "B":
                                    $group_asp_input.val("2");
                                    break;

                                default:
                                    $group_asp_input.val("3");

                            }

                        });

                        // When the "Submit" button is clicked...
                        $submit.on("click", function () {

                            var obj_validation = methods.validateUserInput();

                            // If all required fields are filled in --> Submit the form
                            // Else --> Alert the user
                            if (obj_validation.bool) {

                                // Show the "Please Wait" div
                                methods.pleaseWait();

                                // Enable all "Restaurant Information" dropdown lists ( required or the values cannot be read from the code behind )
                                methods.enableDropDownsLCounter();
                                methods.enableDropDownsNextGen();
                                methods.enableDropDownsOrderTechnology();

                                // Submit the form
                                asp.$btnSubmit.click();

                            }
                            else {

                                alert("Please complete the following section(s) and try again:" + "\n" + obj_validation.msg);

                            }

                        });

                    },
                    disableDropDownsLCounter: function() {

                        asp.$ddlPUWType
                            .val("1")
                            .prop("disabled", "disabled");

                        asp.$ddlKitchen
                            .val("0")
                            .prop("disabled", "disabled");

                        asp.$ddlDiningRoom
                            .val("0")
                            .prop("disabled", "disabled");

                        asp.$ddlFlooring
                            .val("0")
                            .prop("disabled", "disabled");

                        asp.$ddlDrinks
                            .val("0")
                            .prop("disabled", "disabled");

                    },
                    enableDropDownsLCounter: function() {

                        asp.$ddlPUWType.removeProp("disabled");
                        asp.$ddlKitchen.removeProp("disabled");
                        asp.$ddlDiningRoom.removeProp("disabled");
                        asp.$ddlFlooring.removeProp("disabled");
                        asp.$ddlDrinks.removeProp("disabled");

                    },
                    disableDropDownsNextGen: function() {

                        if (asp.$ddlPUWType.val() === "7") { //Drive Thru Only
                            asp.$ddlDiningRoom
                                .val("1") //Do Not Maintain a Dining Room
                                .prop("disabled", "disabled")

                            asp.$ddlKitchen
                                //.val("0")
                                .prop("disabled", "disabled")
                        } else {
                            asp.$ddlPUWType
                                .val("1") //1 PUW
                                .prop("disabled", "disabled")
                        }

                        asp.$ddlCounterType
                            .val("0") //Standard Counter
                            .prop("disabled", "disabled");

                        //asp.$ddlPUWType
                        //    .val("1")
                        //    .prop("disabled", "disabled");

                        asp.$ddlDiningRoom
                            .val("0") //Maintain a Dining Room
                            .prop("disabled", "disabled");

                        asp.$ddlFlooring
                            .val("0") //All-Tile Dining Room Floor
                            .prop("disabled", "disabled");

                        asp.$ddlDrinks
                            .val("0") //Self-Serve Drinks
                            .prop("disabled", "disabled");

                        asp.$ddlGrill
                            .val("0") //Double-Sided Grill
                            .prop("disabled", "disabled");

                        $("#ctl00_MainContent_cblProductivityEnhancements_0")
                            .add($("#ctl00_MainContent_cblProductivityEnhancements_2"))
                            .add($("#ctl00_MainContent_cblProductivityEnhancements_7"))
                            .add($("#ctl00_MainContent_cblProductivityEnhancements_10"))
                            .add($("#ctl00_MainContent_cblProductivityEnhancements_11"))
                            .prop("disabled", true)
                            .prop("checked", true);

                    },
                    enableDropDownsNextGen: function() {

                        asp.$ddlCounterType.removeProp("disabled");
                        asp.$ddlPUWType.removeProp("disabled");
                        asp.$ddlDiningRoom.removeProp("disabled");
                        asp.$ddlFlooring.removeProp("disabled");
                        asp.$ddlDrinks.removeProp("disabled");
                        asp.$ddlGrill.removeProp("disabled");

                        $("#ctl00_MainContent_cblProductivityEnhancements_0")
                            .add($("#ctl00_MainContent_cblProductivityEnhancements_2"))
                            .add($("#ctl00_MainContent_cblProductivityEnhancements_7"))
                            .add($("#ctl00_MainContent_cblProductivityEnhancements_10"))
                            .add($("#ctl00_MainContent_cblProductivityEnhancements_11"))
                            .prop("disabled", false);

                    },
                    disableDropDownsDTOnly: function() {

                        asp.$ddlCounterType
                            .val("0") //Standard Counter
                            .prop("disabled", "disabled");

                        asp.$ddlKitchen
                            .val("3") //Global Next Gen
                            .prop("disabled", "disabled");

                        asp.$ddlDiningRoom
                            .val("1") //Do Not Maintain a Dining Room
                            .prop("disabled", "disabled");

                        asp.$ddlFlooring
                            .val("0") //All-Tile Dining Room Floor
                            .prop("disabled", "disabled");

                        asp.$ddlDrinks
                            .val("0") //Self-Serve Drinks
                            .prop("disabled", "disabled");

                        asp.$ddlGrill
                            .val("0") //Double-Sided Grill
                            .prop("disabled", "disabled");

                    },
                    enableDropDownsDTOnly: function() {

                        asp.$ddlCounterType.removeProp("disabled");
                        asp.$ddlKitchen.removeProp("disabled");
                        asp.$ddlDiningRoom.removeProp("disabled");
                        asp.$ddlFlooring.removeProp("disabled");
                        asp.$ddlDrinks.removeProp("disabled");
                        asp.$ddlGrill.removeProp("disabled");

                    },
                    disableDropDownsOrderTechnology: function() {

                        asp.$ddlDriveThruMix
                            .val(70) //70%
                            .prop("disabled", "disabled");

                    },
                    enableDropDownsOrderTechnology: function() {

                        asp.$ddlDriveThruMix.removeProp("disabled");

                    },
                    setIPT: function() {

                        $(".ipt_group").each(function () {

                            var $el = $(this),
                                $group_inputs = $el.find('input[type="button"]'),
                                $asp_text_box = $el.find('input[type="text"]'),
                                hidden_index = ($asp_text_box.val() === "False" ? 1 : 0);     // False is set to a 1 here ( as opposed to the typical 0 ) because this value is used as the index of the input button, and the "False" button is actually index 1
                                                        
                            $group_inputs.eq(hidden_index).addClass("ipt_active");                            

                        });                        

                    },
                    setProductivityRanks: function () {

                        // Loop through each "Day Part" group
                        $(".dp_group").each(function () {

                            var $el = $(this),
                                $group_inputs = $el.find('input[type="button"]'),
                                $asp_text_box = $el.find('input[type="text"]'),
                                hidden_index = $asp_text_box.val() || -1;

                            if (hidden_index !== -1) {
                                $group_inputs.eq(hidden_index - 1).addClass("dp_active");
                            }

                        });

                    },
                    validateUserInput: function () {

                        var validation = {
                                bool: false,
                                msg: ""
                            },
                            exchange_rate = asp.$tbExchangeRate.val(),
                            restaurant_information_bool = true,
                            exchange_rate_bool = true,
                            crew_productivity_bool = true;

                        // Use IPT Labor Matrix
                        // No validation needed for this. The only time the form should NOT submit, is if the rest of the page was not filled in, which would fail the validation check anyway

                        // Restaurant Information:
                        // If any of the dropdown lists are showing the "Please Select..." option --> FAIL
                        if (asp.$ddlCounterType.val() === "-1" || asp.$ddlPUWType.val() === "-1" || asp.$ddlKitchen.val() === "-1" || asp.$ddlDiningRoom.val() === "-1" ||
                            asp.$ddlFlooring.val() === "-1" || asp.$ddlDrinks.val() === "-1" || asp.$ddlGrill.val() === "-1") {
                            restaurant_information_bool = false;
                            validation.msg += "\n" + "Restaurant Information";
                        }

                        // Exchange Rate:
                        // If the "Exchange Rate" is not a number OR is blank OR is less than or equal to zero --> FAIL
                        if ($.isNumeric(exchange_rate) === false || exchange_rate === "" || exchange_rate <= 0) {

                            if (exchange_rate.length === 0) {
                                validation.msg += "\n" + "Exchange Rate";
                            }
                            else if ($.isNumeric(exchange_rate) === false) {                                
                                validation.msg += "\n" + "Exchange Rate ( Must be a number )";
                            }
                            else {                                
                                validation.msg += "\n" + "Exchange Rate ( Must be greater than zero )";
                            }

                            exchange_rate_bool = false;                            

                        }

                        // Crew Productivity:
                        // Loop through each group of "Crew Productivity" input buttons ( A, B, C )
                        $(".dp_group").each(function () {

                            var $el = $(this),
                                $group_inputs = $el.find('input[type="button"]'),
                                group_passed = false;

                            // Loop through each input button in the current group
                            $group_inputs.each(function () {

                                // If the current group has NOT passed validation --> Attempt to validate
                                if (group_passed === false) {

                                    // If the current input was selected --> PASS
                                    if ($(this).hasClass("dp_active")) {
                                        group_passed = true;
                                    }

                                }                                

                            });

                            // If the current group has NOT passed validation after all inputs were tested --> FAIL
                            if (group_passed === false) {

                                crew_productivity_bool = false;
                                
                                if (validation.msg.indexOf("Crew Productivity") === -1) {
                                    validation.msg += "\n" + "Crew Productivity";
                                }

                            }                          

                        });

                        // If all sections passed --> PASS
                        if (restaurant_information_bool && exchange_rate_bool && crew_productivity_bool) {
                            validation.bool = true;
                        }

                        return validation;

                    },
                    pleaseWait: function () {
                        
                        $("#site_overlay").removeClass("hideme");

                        $("#waitdiv").css({
                            "margin": "-15px 0 0 -75px",
                            "visibility": "visible",
                            "z-index": "501"
                        });

                    }
                };

            // Initialize
            methods.init();

        })(jQuery);

    </script>

</asp:Content>