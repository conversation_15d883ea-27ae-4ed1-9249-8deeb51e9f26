<%@ Page MasterPageFile="site.master" Language="VB" Debug="true" EnableViewState="true" AutoEventWireup="false" Inherits="Admin_VendorItemEdit" CodeFile="Admin_VendorItemEdit.aspx.VB" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="ajaxToolkit" %>
<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" Runat="Server">

    <style>


        /* CSS Reset
        -------------------------------------------------- */
        * {
            margin: 0;
            padding: 0;
            border: 0;
            outline: 0;
            box-sizing: border-box;
        }

        .cal_theme1 * {
            box-sizing:content-box;
        }


        /* HTML5
        -------------------------------------------------- */
        header {
            display: block;
        }


        /* Clearfix
        -------------------------------------------------- */
        .group:before,
        .group:after {
	        display: table;
	        content: "";
        }

        .group:after {
	        clear: both;
        }

        .group {
	        zoom: 1;
        }


        /* Global/Misc
        -------------------------------------------------- */
        .button_container {
            clear: both;
            padding-top: 20px;
            text-align: center;
        }

        .hide_me { display: none !important; }

        .required {
            font-style: italic;
            color: #f00;
        }

        /* Override the CSS applied to the "Please Wait" div */
        #waitdiv {
            height: auto !important;
            width: auto !important;
            z-index: 300 !important;
        }


        /* Form Elements
        -------------------------------------------------- */
        fieldset {
            position: relative;
            margin-top: 14px;
            padding: 5px 10px 10px 10px;
            width: 100%;
            border: 1px solid #000;
        }

        fieldset.date_range {
            padding-top: 8px;
            padding-bottom: 13px;
        }

            legend {
                padding: 0 3px;
                font-size: 12px;
                font-weight: bold;
            }

            fieldset input {
                height: 12px;
                width: 12px;
                vertical-align: middle;
            }

            fieldset label {
                padding-left: 6px;
                font-size: 12px;
            }

            fieldset select {
                width: 100%;
            }

            img.selAll,
            img.selNone {
                position: absolute;
                top: 2px;
                right: 8px;
                cursor: pointer;
            }

            img.selNone {
                right: 20px;
            }


        /* Site Overlay
        -------------------------------------------------- */
        #site_overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba( 0, 0, 0, 0.3 );
            z-index: 100;
        }


        /* Container Divs
        -------------------------------------------------- */
        #report_container,
        #options_container {
            display: inline-block;
            margin: 0 auto;
            padding: 20px;
            width: auto;
            border: 2px solid #000;
            background-color: #fff;
            text-align: left;
            color: #000;
        }

        #report_container {
            margin-bottom: 16px;
        }

        #options_container {
            position: absolute;
            top: 150px;
            left: 50%;
            margin-left: -240px;
            width: 480px;
            z-index: 200;
        }

            #report_container h1,
            #options_container h1 {
                margin: -20px -20px 0 -20px;
                padding: 0 10px;
                height: 48px;
                border-bottom: 1px solid #000;
                background-color: #e6e6e6;
                font-size: 16px;
                font-weight: normal;
                line-height: 48px;
                text-align: center;
            }

            #report_container h1 {
                min-height: 48px;
                height: auto;
            }

            .sub_header {
                display: block;
                margin-top: -26px;
                padding: 16px 0px;
                font-size: 12px;
                line-height: 16px;
            }

        #grid_container {
            padding-top: 20px;
        }

            .gv th,
            .gv td {
                padding: 3px 8px 3px 2px;
                border: 1px solid #000;
                font-size: 12px;
                white-space: nowrap;
            }

            .gv th {
                padding-top: 6px;
                padding-bottom: 6px;
                background-color: #ccc;
                text-align: left;
            }

            .grid_label {
                display: block;
                text-align: center;
            }

        #fsDate {
            width:50%;
        }
    </style>


	<form runat="server" id="Form1"> 
    <ajaxToolkit:ToolkitScriptManager ID="ToolkitScriptManager1" runat="server" />	
	<table class="maintable">
		<tr><td class="maintabletitle" colspan="2">Vendor Item Parameters</td></tr>
		<tr><td class="maintablebody"><asp:label runat="server" ID="lblError" CssClass="errmsg" Text="" /></td></tr>
		<tr>
			<td class="maintablebody">
				<table>
					<tr>
						<td class="reportoptionlabelnarrow">Vendor Item ID:</td>			
						<td class="reportoptioncell"><asp:textbox runat="server" id="VendorItemID" readonly="true" class="inputcontrolswide"/><asp:Label style="padding-left: 5px;" id="NewItemLabel" runat="server" visible="false" class="inputcontrolswide"/></td>
					</tr>
					<tr>
						<td class="reportoptionlabelnarrow">Vendor:</td>					
						<td class="reportoptioncell"><asp:dropdownlist runat="server" id="VendorID" OnSelectedIndexChanged="vendorchange" AutoPostBack="True" class="inputcontrolswide"/></td>
					</tr>
					<tr>
						<td class="reportoptionlabelnarrow">Distribution Center:</td>		
						<td class="reportoptioncell"><asp:dropdownlist runat="server" id="DistributionCenterID" class="inputcontrolswide"/></td>
					</tr>					
					<tr>
						<td class="reportoptionlabelnarrow">Vendor Item Number:</td>		
						<td class="reportoptioncell">
							<asp:textbox runat="server" id="VendorItemNumber" class="inputcontrolswide"/>
							<asp:RequiredFieldValidator id="valVendorItemNumber" ControlToValidate="VendorItemNumber" ErrorMessage="Please enter a Vendor Item Number." Display="None" runat="server" />
						</td>						
					</tr>
					<tr>					    
						<td class="reportoptionlabelnarrow">Ingredient:</td>				
						<td class="reportoptioncell"><asp:dropdownlist runat="server" ClientIDMode="Static" id="IngredientID" class="inputcontrolswide" /></td>
					</tr>
					<tr>
						<td class="reportoptionlabelnarrow">Order Unit Multiplier:</td>	
						<td class="reportoptioncell">
							<asp:textbox runat="server" id="OrderUnitMultiplier" ClientIDMode="Static" class="inputcontrolswide"/>
							<asp:RequiredFieldValidator id="valOrderUnitMultiplier1" ControlToValidate="OrderUnitMultiplier" ErrorMessage="Please enter an Order Unit Multiplier." Display="None" runat="server" />
                            <%--2025-04-23 jjc Sprint 28 SBOO-624 Removing Integer Requirement--%>
							<%--<asp:CompareValidator id="valOrderUnitMultiplier2" runat="server" ControlToValidate="OrderUnitMultiplier" ErrorMessage="Order Unit Multiplier must be entered as an integer." Operator="DataTypeCheck" Type="Integer" Display="None" />--%>
						</td>
					</tr>
					<tr>
						<td class="reportoptionlabelnarrow">Unit Measure:</td>			
						<td class="reportoptioncell">
							<asp:dropdownlist runat="server" id="UnitMeasureID" class="inputcontrolswide"/>
							<asp:RequiredFieldValidator id="valUnitMeasureID" runat="server" ControlToValidate="UnitMeasureID" ErrorMessage="Please select a Unit Measure" InitialValue="0" Display="None" EnableClientScript="True" />
						</td>
					</tr>
					<tr>
						<td class="reportoptionlabelnarrow">Vendor Item Description:</td>	
						<td class="reportoptioncell">
							<asp:textbox runat="server" id="VendorItemDescription" class="inputcontrolswide"/>
							<asp:RequiredFieldValidator id="valVendorItemDescription" ControlToValidate="VendorItemDescription" ErrorMessage="Please enter a Vendor Item Description." Display="None" runat="server" />
						</td>
					</tr>
					<tr>
						<td class="reportoptionlabelnarrow">Franchise:</td>			
						<td class="reportoptioncell"><asp:dropdownlist runat="server" id="FranchiseID" class="inputcontrolswide" OnSelectedIndexChanged="ddlFranchise_SelectedIndexChanged" AutoPostBack="true"/></td>
					</tr>
					<tr>
						<td class="reportoptionlabelnarrow">Purchase Account:</td>		
						<td class="reportoptioncell">
							<asp:dropdownlist runat="server" id="PurchaseAccountID" class="inputcontrolswide"/>
							<asp:RequiredFieldValidator id="valPurchaseAccountID" runat="server" ControlToValidate="PurchaseAccountID" ErrorMessage="Please select a Purchase Account" InitialValue="0" Display="None" EnableClientScript="True" />
						</td>
					</tr>
					<tr>
						<td class="reportoptionlabelnarrow">Do Not Inventory:</td>	
						<td class="reportoptioncell"><asp:checkbox runat="server" id="DoNotInventory"/></td>
					</tr>
					<tr>
						<td class="reportoptionlabelnarrow">DNU Ing. Price:</td>	
						<td class="reportoptioncell"><asp:checkbox runat="server" id="DNUIngPrice"/></td>
					</tr>
					<tr>
						<td class="reportoptionlabelnarrow">OG Import Override:</td>	
						<td class="reportoptioncell"><asp:checkbox runat="server" id="OGOverride"/></td>
					</tr>
					<tr>
						<td class="reportoptionlabelnarrow">Is Active:</td>	
						<td class="reportoptioncell"><asp:checkbox runat="server" id="CanOrder"/></td>
					</tr>
					<tr>
						<td class="reportoptionlabelnarrow">Show On S.O.G.:</td>	
						<td class="reportoptioncell"><asp:checkbox runat="server" id="ShowOnSOG"/></td>
					</tr>
					<tr>
						<td class="reportoptionlabelnarrow">Is Split Case:</td>	
						<td class="reportoptioncell"><asp:checkbox runat="server" ClientIDMode="Static" id="chkSplitCase"/></td>
					</tr>
					<tr>
						<td class="reportoptionlabelnarrow">Is Catch Weight Item:</td>	
						<td class="reportoptioncell"><asp:checkbox runat="server" ClientIDMode="Static" id="IsCatchWeight"/></td>
					</tr>
					<tr>
						<td class="reportoptionlabelnarrow">Standard Case Weight:</td>	
						<td class="reportoptioncell"><asp:TextBox ID="txtStandardCaseWeight" ClientIDMode="Static" runat="server" class="inputcontrolswide" ></asp:TextBox></td>
					</tr>
					<tr>
						<td class="reportoptionlabelnarrow">Vendor RawItemID:</td>	
						<td class="reportoptioncell"><asp:TextBox ID="txtVendorRawItemID" runat="server" class="inputcontrolswide" ></asp:TextBox></td>
					</tr>
					<tr>
						<td class="reportoptionlabelnarrow">Is Surcharge Item:</td>	
						<td class="reportoptioncell"><asp:checkbox runat="server" id="IsSurcharge"/></td>
					</tr>
					<tr>
						<td class="reportoptionlabelnarrow">Presentation Order:</td>	
						<td class="reportoptioncell"><asp:textbox runat="server" id="PresentationOrder" class="inputcontrolswide"/></td>
					</tr>
					<tr>
						<td class="reportoptionlabelnarrow">Thumbprint:</td>	
						<td class="reportoptioncell"><asp:textbox runat="server" id="txtThumbprint" class="inputcontrolswide" ReadOnly="true"/></td>
					</tr>
					<tr>
						<td class="reportoptionlabelnarrow">Last Modified By:</td>	
						<td class="reportoptioncell"><asp:textbox runat="server" id="txtLastModifiedBy" class="inputcontrolswide" ReadOnly="true"/></td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td class="tdbuttons">
				<asp:Button id="btnSave" runat="server" Text="Save" ClientIDMode="Static" ToolTip="Save current changes" class="buttons" onclick="btnSave_click" style="width: 80px;" />
				<asp:Button id="btnDel" runat="server" Text="Delete" ToolTip="Delete current item" class="buttons" onclick="btnDelete_click" style="width: 80px;" />
				<asp:Button id="btnCan" runat="server" Text="Cancel" ToolTip="Discard current changes" class="buttons" onclick="btnCancel_click" style="width: 80px;" />
				<asp:ValidationSummary id="ValidationSummary" runat="server" ShowMessageBox="True" ShowSummary="False"></asp:ValidationSummary>
			</td>
		</tr>
		<tr><td style="text-align: center;"><%= emsg %></td></tr>
	</table>
                <%-- Report Options --%>
    <div id="options_container" class="group hide_me"> 
                    <header>
                        <h1>Fixable Change Detected</h1>
                    </header>
        <asp:UpdatePanel runat="server" ID="upOptions">
            <Triggers>
                <asp:PostBackTrigger ControlID="btnDoAdvSave" />
            </Triggers>
            <ContentTemplate>
        
                    <%-- Content --%>
                    <p>
                        A change has been detected which can be fixed historically automatically. <br />
                        <br />If you wish to fix historically, please select a date below. <br />
                        <br />If you would rather not fix it historically, please click "save normally".
                    </p>

                    <%-- District --%>
                    <fieldset id="fsDate" runat="server">

                        <legend>Start Date</legend>

                        <div id="district_container">
                            <asp:TextBox ID="txtStartDate" ClientIDMode="Static" runat="server" CssClass="tbGradient" style="width: 150px;" />		                    
                            <asp:Image runat="server" id="imgStartDate" ImageUrl="images/Calendar.png" ImageAlign="AbsMiddle" />  
                            <ajaxToolkit:CalendarExtender id="calStartDate" runat="server" TargetControlID="txtStartDate" Format="MM/dd/yyyy" PopupButtonID="imgStartDate" CssClass="cal_theme1" />
                        </div>

                    </fieldset>
                    <asp:Label ID="lblDistrict" runat="server" CssClass="required" Visible="false" />

                    <div class="button_container">
                        <asp:Button id="btnSaveNoFix" runat="server" Text="Save Normally" ClientIDMode="Static" ToolTip="Save current changes" class="buttons" onclick="btnSave_click" />
                        <asp:Button ID="btnDoAdvSave" ClientIDMode="Static" runat="server" CssClass="buttons" Text="Save And Fix Historically" OnClick="btnDoAdvSave_Click" />
                        <input type="button" id="hide_opts" class="buttons" value="Cancel" />
                    </div>

            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
    

    <%-- Site Overlay --%>
    <div id="site_overlay" class="hide_me"></div>

	</form> 
    
    <script src="JS/jquery-1.9.1.js"></script>
    <script type="text/javascript">
        var Snapshot = (function ($) {
            "use strict";

            var $OUM = $("#OrderUnitMultiplier"),
                $ING = $("#IngredientID"),
                $CATCH = $("#IsCatchWeight")[0];

            function Snapshot() {
                this.OUM = Number($OUM.val());
                this.INGID = Number($ING.val());
                this.CATCH = $CATCH.checked;
            };

            Snapshot.prototype.equals = function (that) {
                for (var prop in that) {
                    if (typeof this[prop] === "function") continue;
                    if (this[prop] === undefined || that[prop] === undefined) return false;
                    if (this[prop] !== that[prop]) return false;
                }
                return true;
            };

            Snapshot.prototype.hasChanged = function () {
                var that = new Snapshot();

                return !(this.equals(that)) && !this.CatchWeight(that);
            };

            Snapshot.prototype.CatchWeight = function (that) {
                that = that || new Snapshot();
                return this.CATCH || that.CATCH;
            }
            return Snapshot;
        })(jQuery);


    </script>
    <script type="text/javascript">
        ; (function ($) {
            "use strict";

            var $options = $("#options_container"),
                $site_overlay = $("#site_overlay"),
                $grid_container = $("#grid_container"),
                $wait = $("#waitdiv"),
                $snapshot = new Snapshot(),
                methods = {
                    init: function () {

                        // Bind events
                        methods.bindEvents();


                        // Adjust the position of the "Please Wait" div ( overwrite values set on the Site.Master )
                        $wait.css({
                            "margin-left": ($wait.outerWidth() / 2) * -1,
                            "margin-top": ($wait.outerHeight() / 2) * -1
                        });

                        // If the hidden <asp:TextBox> indicates that the "Report Options" popup should be displayed --> Clear the textbox and show the popup
                        

                    },
                    bindEvents: function () {

                        // When the "Report Options" or "Close" buttons are clicked...
                        $("#btnSave, #hide_opts").off("click", methods.toggleOptions);
                        $("#btnSave, #hide_opts").on("click", methods.toggleOptions);

                        $("#btnDoAdvSave").off("click");
                        $("#btnDoAdvSave").on("click", function (ev) {
                            var txtDate = $("#txtStartDate").val();
                            if(typeof txtDate !== typeof "string")
                            {
                                console.log(txtDate + " ")
                                return false;
                            }

                            if (txtDate.trim() === "") {
                                alert("Please specify a date.");
                                return false;
                            }
                            
                            var SelectedDate = new Date(txtDate);

                            var limit = (new Date()).getDateOnly().subtract(new Sys.Extended.UI.TimeSpan(90, 0, 0, 0));
                            
                            if (SelectedDate < limit) {
                                //Do not use locale date string
                                //IE adds left-to-right order marks
                                alert("The date selected (" + SelectedDate.format("MM/dd/yyyy") + ") is older than 90 days. Please specify a date within the last 90 days.");
                                return false;
                            }

                            if (SelectedDate.toString() === "Invalid Date") {
                                alert("Invalid date specified.");
                                return false;
                            }

                            if (SelectedDate > new Date()) {
                                alert("Please do not specify a date in the future.");
                                return false;
                            }
                            
                            $("#txtStartDate").val(SelectedDate.format("MM/dd/yyyy"))
                            
                            return confirm("Relink until " + SelectedDate.format("MM/dd/yyyy") + "?");
                        });

                        // When a "+" or "-" icon is clicked ( Select All/None )...
                        $('#options_container .selAll,#options_container .selNone').on('click', function () {

                            var checkStatus = $(this).prop('class') === 'selAll',
                                $parent = $(this).parent();

                            $parent.find(':checkbox').each(function () {
                                this.checked = checkStatus;
                            });

                        });

                    },
                    toggleOptions: function (event) {
                        var $visible = !($options.hasClass("hide_me"));

                        if ($visible) {
                            $options.addClass("hide_me");
                            $site_overlay.addClass("hide_me");
                        }
                        else
                        {
                            if ($snapshot.hasChanged() || (event.shiftKey && !$snapshot.CatchWeight())) {

                                $options.toggleClass("hide_me");
                                $site_overlay.toggleClass("hide_me");

                                event.preventDefault();
                                return false;
                            }
                            else if (event.shiftKey && $snapshot.CatchWeight()) {
                                alert("You cannot save and fix items that change the catch weight flag. Please save normally.");
                                return false;
                            }
                                
                        }
                    }
                };
            // Initialize the script
            methods.init();
            var prm = Sys.WebForms.PageRequestManager.getInstance();
            prm.add_endRequest(methods.init);

        })(jQuery);
    </script>



</asp:Content> 