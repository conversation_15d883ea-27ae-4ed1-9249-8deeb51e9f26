﻿Option Explicit On

Imports System.IO
Imports OfficeOpenXml
Imports System.Data
Imports System.Data.OleDb
Imports System.Net
Imports System.Net.Mail


'2021-06-07 jjc Sprint79w67 SBOD-2254 Updating <NAME_EMAIL> to <EMAIL>



Public Class HistoricalSalesImport
    Inherits System.Web.UI.Page
    Dim InvWSS As InventoryWebSiteServices.InventoryServices

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        'If the user is not logged in --> Redirect to the login page
        If Len(Session("UserID")) = 0 Then
            Response.Redirect("login.aspx?timeout=1")
        End If

        InvWSS = Session("IWSS")

        If Not IsPostBack Then

            PopRegions()
            PopDistricts()
            PopStores()

            tbStartDate.Text = DateTime.Today
            tbEndDate.Text = DateTime.Today

        End If

    End Sub

#Region "Populate Report Options"

    Sub PopRegions()

        ddlRegion.Items.Clear()

        Dim tabRegions As DataTable
        Dim li As System.Web.UI.WebControls.ListItem

        tabRegions = InvWSS.GetPermittedRegions()

        'If only one row is returned --> Hide the <asp:DropDownList>
        'Else --> Add an "All Regions" option
        Select Case tabRegions.Rows.Count

            Case 1

                fsRegion.Visible = False

            Case Else

                li = New System.Web.UI.WebControls.ListItem("All Regions")
                li.Value = -1
                ddlRegion.Items.Add(li)

        End Select

        For Each row As DataRow In tabRegions.Rows

            li = New System.Web.UI.WebControls.ListItem(row("Region"))
            li.Value = row("RegionNum")
            ddlRegion.Items.Add(li)

        Next

        tabRegions.Dispose()
        li = Nothing

    End Sub

    Sub PopDistricts()

        ddlDistrict.Items.Clear()

        Dim tabDistricts As New DataTable
        Dim li As System.Web.UI.WebControls.ListItem

        If Not ddlRegion.SelectedItem Is Nothing Then

            tabDistricts = InvWSS.GetPermittedDistricts(ddlRegion.SelectedItem.Value)

            'If no rows are returned --> Display a message to the user
            'ElseIf one row is returned --> Hide the <asp:DropDownList>
            'Else --> Add an "All Districts" option
            Select Case tabDistricts.Rows.Count

                Case 0

                    lblDistrict.Text = "No Districts found in the selected Region."
                    lblDistrict.Visible = True

                Case 1

                    fsDistrict.Visible = False

                Case Else

                    li = New System.Web.UI.WebControls.ListItem("All Districts")
                    li.Value = -1
                    ddlDistrict.Items.Add(li)

            End Select

            For Each row As DataRow In tabDistricts.Rows

                li = New System.Web.UI.WebControls.ListItem(row("District"))
                li.Value = row("DistrictNum")
                ddlDistrict.Items.Add(li)

            Next

        End If

        tabDistricts.Dispose()
        li = Nothing

    End Sub

    Sub PopStores()

        fsStore.Visible = True
        cblStore.Items.Clear()

        Dim sbStores As New StringBuilder
        Dim tabStores As New DataTable
        Dim li As System.Web.UI.WebControls.ListItem

        With sbStores

            .AppendLine("SELECT")
            .AppendLine("    [StoreID]")
            .AppendLine("	,[StoreNum]")
            .AppendLine("	,[StoreDescription]")
            .AppendLine("FROM")
            .AppendLine("	[dbo].[tmStore] WITH(NOLOCK)")
            .AppendLine("WHERE")
            .AppendLine("	[StoreID] IN")
            .AppendLine("	(")
            .AppendLine("	    SELECT")
            .AppendLine("			tmS.[StoreID]")
            .AppendLine("		FROM")
            .AppendLine("	        [dbo].[WEBRPT_vjBinPermissions] AS [vjBP] WITH(NOLOCK)")
            .AppendLine("		    INNER JOIN [dbo].[tmStore] AS [tmS] WITH(NOLOCK)")
            .AppendLine("			    ON vjBP.[UniversalStoreIdentifier] = tmS.[UniversalStoreIdentifier]")
            .AppendLine("		    INNER JOIN [dbo].[tmNode] AS [tmN] WITH(NOLOCK)")
            .AppendLine("			    ON tmS.[UniversalNodeIdentifier] = tmN.[UniversalNodeIdentifier]")
            .AppendLine("		WHERE")
            .AppendLine("			vjBP.[BinLevel] = 1")
            .AppendLine("			AND tmN.[ActiveNode] <> 0")
            .AppendLine("			AND vjBP.[UniversalAccountIdentifier] = '" & Session("AccountID") & "'")

            'If a "Region" is selected --> Append the WHERE clause
            If ddlRegion.SelectedValue <> "-1" Then
                .AppendLine("			AND vjBP.[UniversalRegionIdentifier] = ( SELECT [UniversalRegionIdentifier] FROM [dbo].[tmRegion] WITH(NOLOCK) WHERE [RegionNum] = " & ddlRegion.SelectedValue & " )")
            End If

            'If a "District" is selected --> Append the WHERE clause
            If ddlDistrict.SelectedValue <> "-1" AndAlso ddlDistrict.SelectedValue <> "" Then
                .AppendLine("			AND vjBP.[UniversalDistrictIdentifier] = ( SELECT [UniversalDistrictIdentifier] FROM [dbo].[tmDistrict] WITH(NOLOCK) WHERE [DistrictNum] = " & ddlDistrict.SelectedValue & " )")
            End If

            .AppendLine("   )")
            .AppendLine("ORDER BY")
            .AppendLine("	[StoreNum]")

        End With

        InvWSS.GetData(sbStores.ToString, tabStores)

        If Not IsDBNull(tabStores) AndAlso Not tabStores Is Nothing Then

            'If no rows are returned --> Display a message to the user
            'Else --> Populate the store list
            If tabStores.Rows.Count = 0 Then

                lblStore.Text = "You do not have access to any stores in the selected Region/District."
                lblStore.Visible = True

            Else

                For Each row As DataRow In tabStores.Rows

                    li = New System.Web.UI.WebControls.ListItem(row("StoreDescription"))
                    li.Value = row("StoreID")
                    cblStore.Items.Add(li)

                Next

                tabStores.Dispose()
                li = Nothing

            End If

        Else

            lblStore.Text = "Error - Store list returned Null/Nothing"
            lblStore.Visible = True

        End If

    End Sub

#End Region

#Region "Event Handlers"

    Public Sub ddlRegion_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs)

        lblDistrict.Visible = False
        lblStore.Visible = False

        PopDistricts()
        PopStores()

    End Sub

    Public Sub ddlDistrict_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs)

        lblStore.Visible = False

        PopStores()

    End Sub

    Public Sub btnDownloadWorkbook_Click(ByVal sender As Object, ByVal e As EventArgs)

        'Reset label(s) and hidden <asp:TextBox> ( These resets don't work if the validation PASSES and the "Open/Save" dialog appears - Possibly due to lack of PostBack? )
        lblDateRange.Text = ""
        lblDateRange.Visible = False
        lblStore.Text = ""
        lblStore.Visible = False
        tbShowOpts.Text = ""

        If ValidateDownloadOptions() Then
            CreateBlankWorkbook()
        Else
            tbShowOpts.Text = "download-options"
        End If

    End Sub

    Sub btnUploadFile_Click(ByVal sender As Object, ByVal e As System.EventArgs)

        'Reset label(s) and hidden <asp:TextBox>
        lblUpload.Visible = False
        lblGrid.Text = "Please select one of the above options to continue."
        lblGrid.CssClass = "required label"
        tbShowOpts.Text = ""

        'If the user is uploading a ".xlsx" file --> Continue
        'Else --> Display a message
        If FileUpload1.HasFile AndAlso Path.GetExtension(FileUpload1.FileName) = ".xlsx" Then

            Dim oExcelPackagePlus As New ExcelPackage(FileUpload1.PostedFile.InputStream)

            Dim ws As ExcelWorksheet = oExcelPackagePlus.Workbook.Worksheets(1)

            'Import the workbook into a DataTable for processing...
            Dim dt As DataTable = CreateHistoricalSalesDataTable(ws)

            'If the DataTable contains data --> Insert the "Historical Sales" data into the [dbo].[ttsHistoricalSalesImport] table
            If dt.Rows.Count > 0 Then
                InsertHistoricalSales(dt)
            End If

        Else

            With lblUpload
                .Text = "Select a .xlsx workbook to continue."
                .Visible = True
            End With

            tbShowOpts.Text = "upload-options"

        End If

    End Sub

#End Region

#Region "Download Blank Workbook"

    Private Function ValidateDownloadOptions() As Boolean

        Dim bValidationPassed As Boolean = False
        Dim failMessage As String = ""

        'Store
        With lblStore

            If cblStore.SelectedIndex > -1 Then

                'Date Range
                With lblDateRange

                    If tbStartDate.Text = "__/__/____" Then
                        .Text = "Enter a Start Date."

                    ElseIf tbEndDate.Text = "__/__/____" Then
                        .Text = "Enter an End Date."

                    ElseIf CDate(tbStartDate.Text) > DateTime.Today Then
                        .Text = "Start Date can't be in the future."

                    ElseIf CDate(tbEndDate.Text) > DateTime.Today Then
                        .Text = "End Date can't be in the future."

                    ElseIf CDate(tbStartDate.Text) > CDate(tbEndDate.Text) Then
                        .Text = "End Date must be greater than the Start Date."

                    Else
                        bValidationPassed = True

                    End If

                    If bValidationPassed = False Then
                        .Visible = True
                    End If

                End With

            Else

                .Text = "Select a store to continue."
                .Visible = True

            End If

        End With

        Return bValidationPassed

    End Function

    Private Enum eWorksheetColumns

        storeNum = 1
        storeDescription = 2
        businessDate = 3
        netSales = 4
        customerCount = 5

    End Enum

    Private Sub CreateBlankWorkbook()

        'Query the database, and return the "Historical Sales" for the selected Stores/Dates
        Dim dt As DataTable = GetHistoricalSales()

        'If data was returned --> Continue
        If Not IsNothing(dt) AndAlso dt.Rows.Count > 0 Then

            Me.EnableViewState = False

            Dim ExcelMemoryStream As New MemoryStream()
            Dim oExcelPackagePlus As New ExcelPackage(ExcelMemoryStream)

            Dim ws As ExcelWorksheet = oExcelPackagePlus.Workbook.Worksheets.Add("Historical Sales")

            With ws

                'Populate the worksheet
                .Cells("A1").LoadFromDataTable(dt, True)

                'Style worksheet cells
                With .Cells(ws.Dimension.Address)

                    .AutoFitColumns()
                    .Style.HorizontalAlignment = Style.ExcelHorizontalAlignment.Left

                End With

                'Style the header row
                .Cells("A1:E1").Style.Font.Bold = True

            End With

            'Save the workbook
            oExcelPackagePlus.Save()

            With Response
                .Clear()
                .ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                .AddHeader("content-disposition", "attachment;filename=SBOnet_HistoricalSalesImport.xlsx")
                .BinaryWrite(ExcelMemoryStream.ToArray())
            End With

            Response.Flush() ' Send client what we have
            Response.SuppressContent = True ' Don't let ASP.Net send more info after we did
            Context.ApplicationInstance.CompleteRequest() ' Skip straight to end_request, do not pass go, do not collect $200

        End If

    End Sub

    Private Function GetHistoricalSales() As DataTable

        Dim sbSelectedStores As New StringBuilder
        Dim sbSQL As New StringBuilder
        Dim tabResults As DataTable = Nothing

        Try

            With sbSelectedStores

                'Loop through the Store list...
                For Each li As System.Web.UI.WebControls.ListItem In cblStore.Items

                    'If a store is selected --> Append it
                    If li.Selected Then
                        .AppendLine("	,( " & li.Value & " )")
                    End If

                Next

            End With

            With sbSQL

                .AppendLine("DECLARE @StartDate AS DATE = '" & tbStartDate.Text & "';")
                .AppendLine("DECLARE @EndDate AS DATE = '" & tbEndDate.Text & "';")
                .AppendLine("")
                .AppendLine("DECLARE @Stores AS TABLE")
                .AppendLine("(")
                .AppendLine("	[StoreID] INT")
                .AppendLine(");")
                .AppendLine("")
                .AppendLine("INSERT INTO @Stores ( [StoreID] )")
                .AppendLine("VALUES")
                .AppendLine(New Regex(",").Replace(sbSelectedStores.ToString(), " ", 1) & ";")
                .AppendLine("")
                .AppendLine("--SELECT * FROM @Stores;")
                .AppendLine("")
                .AppendLine("WITH cteBusinessDate AS")
                .AppendLine("(")
                .AppendLine("	SELECT")
                .AppendLine("		@StartDate AS [BusinessDate]")
                .AppendLine("")
                .AppendLine("	UNION ALL")
                .AppendLine("")
                .AppendLine("	SELECT")
                .AppendLine("		DATEADD( dd, 1, [BusinessDate] )")
                .AppendLine("	FROM")
                .AppendLine("		cteBusinessDate")
                .AppendLine("	WHERE")
                .AppendLine("		[BusinessDate] < @EndDate")
                .AppendLine(")")
                .AppendLine("")
                .AppendLine("--SELECT * FROM cteBusinessDate;")
                .AppendLine("")
                .AppendLine("SELECT")
                .AppendLine("	 tmS.[StoreNum]")
                .AppendLine("	,tmS.[StoreDescription]")
                .AppendLine("	,FORMAT( cte.[BusinessDate], 'MM/dd/yyyy' ) AS [BusinessDate]")
                .AppendLine("	,SUM( ttsL.[NET_SALES] ) AS [NetSales]")
                .AppendLine("	,SUM( ttsL.[CUST_COUNT] ) AS [CustomerCount]")
                .AppendLine("FROM")
                .AppendLine("	cteBusinessDate AS [cte]")
                .AppendLine("	CROSS JOIN @Stores AS [stores]")
                .AppendLine("	INNER JOIN [dbo].[tmStore] AS [tmS] WITH(NOLOCK)")
                .AppendLine("		ON stores.[StoreID] = tmS.[StoreID]")
                .AppendLine("	LEFT OUTER JOIN [dbo].[ttsLabor] AS [ttsL] WITH(NOLOCK)")
                .AppendLine("		ON stores.[StoreID] = ttsL.[STOREID]")
                .AppendLine("			AND ttsL.[BUSINESSDATE] = cte.[BusinessDate]")
                .AppendLine("GROUP BY")
                .AppendLine("	 tmS.[StoreNum]")
                .AppendLine("	,tmS.[StoreDescription]")
                .AppendLine("	,cte.[BusinessDate]")
                .AppendLine("ORDER BY")
                .AppendLine("	 tmS.[StoreNum]")
                .AppendLine("	,cte.[BusinessDate]")
                .AppendLine("OPTION")
                .AppendLine("	( MAXRECURSION 1825 );")

            End With

            InvWSS.GetData(sbSQL.ToString(), tabResults)

        Catch

            'Ignore errors...

        End Try

        Return tabResults

    End Function

#End Region

#Region "Upload Completed Workbook"

    Private Function CreateHistoricalSalesDataTable(ByRef ws As ExcelWorksheet) As DataTable

        'DATA VALIDATION ( PHASE 1 ):
        '   This function will attempt to move the data supplied by the user into a DataTable
        '   Because the columns in the DataTable have a specific type, this will throw an error if the user has blank fields, enters text in a numeric field, etc.

        Dim wsStartRow As Integer = 2
        Dim wsEndRow As Integer = ws.Dimension.End.Row

        'Verify that the end row contains data ( If the user copy/pasted/deleted data, it's possible that formatting could exist in cells beyond the data.
        'If this happens, an error will be thrown when trying to insert the data into the DataTable ( the data types would be incorrect because the cells would be blank )

        '----- [ BEGIN ] -----
        Dim bEndRowContainsData As Boolean = False

        Do Until bEndRowContainsData = True

            If ws.Cells(wsEndRow, eWorksheetColumns.storeNum).Value = Nothing AndAlso ws.Cells(wsEndRow, eWorksheetColumns.businessDate).Value = Nothing Then
                wsEndRow -= 1
            Else
                bEndRowContainsData = True
            End If
        Loop
        '----- [ END ] -----

        Dim wsStartCol As Integer = eWorksheetColumns.storeNum
        Dim wsEndCol As Integer = eWorksheetColumns.customerCount

        Dim wsRowNum As Integer = wsStartRow

        Dim dt As New System.Data.DataTable

        'Create data columns
        With dt.Columns

            .Add("StoreNum", Type.GetType("System.Int32"))
            .Add("StoreDescription", Type.GetType("System.String"))
            .Add("BusinessDate", Type.GetType("System.DateTime"))
            .Add("NetSales", Type.GetType("System.Double"))
            .Add("CustomerCount", Type.GetType("System.Int32"))

        End With

        Try

            'Add rows to data table
            For wsRowNum = wsStartRow To wsEndRow

                Dim dtRow As DataRow = dt.NewRow()

                'Loop through each of the columns on the Excel worksheet...
                For wsColNum As Integer = wsStartCol To wsEndCol

                    Dim cellText = ws.Cells(wsRowNum, wsColNum).Text.Replace("$", "").Replace(",", "")

                    If cellText = "" AndAlso (wsColNum = eWorksheetColumns.netSales Or wsColNum = eWorksheetColumns.customerCount) Then
                        cellText = 0
                    End If

                    dtRow.Item(wsColNum - 1) = cellText

                Next

                dt.Rows.Add(dtRow)

            Next

        Catch ex As Exception

            'Clear all rows from the DataTable ( to prevent any valid data prior to the exception from being processed )
            dt.Clear()

            lblGrid.Text = "An error ocurred while processing row #" & wsRowNum & " of the workbook.<br /><br />Please ensure that all cells contain valid data, and try again."
            lblException.Text = ex.ToString()

        End Try

        Return dt

    End Function

    Private Sub InsertHistoricalSales(ByRef dt As DataTable)

        'DATA VALIDATION ( PHASE 2 ):
        '   This sub will attempt to insert the data entered by the user ( now stored in a DataTable) into the [dbo].[ttsHistoricalSalesImport] table
        '   Because the table has PRIMARY KEY constraints, the INSERT will fail on duplicate rows ( the @@ROWCOUNT will return the number of rows that were inserted )

        Try

            Dim sbSQL As New StringBuilder
            Dim tabResults As New DataTable

            With sbSQL

                .AppendLine("DECLARE @PollDate DATE = '" & DateTime.Today.ToShortDateString() & "';")
                .AppendLine("")
                .AppendLine("DECLARE @WorksheetData TABLE")
                .AppendLine("(")
                .AppendLine("	 [StoreNum] INT")
                .AppendLine("	,[StoreDescription] NVARCHAR(50)")
                .AppendLine("	,[BusinessDate] DATE")
                .AppendLine("	,[NetSales] MONEY")
                .AppendLine("	,[CustomerCount] INT")
                .AppendLine(");")
                .AppendLine("")

                For Each row As DataRow In dt.Rows
                    .AppendLine("INSERT INTO @WorksheetData ( [StoreNum], [StoreDescription], [BusinessDate], [NetSales], [CustomerCount] ) VALUES ( " & row.Item("StoreNum") & ", '" & row.Item("StoreDescription") & "', '" & row.Item("BusinessDate") & "', " & row.Item("NetSales") & ", " & row.Item("CustomerCount") & " );")
                Next

                .AppendLine("")
                .AppendLine("--SELECT * FROM @WorksheetData;")
                .AppendLine("")
                .AppendLine("BEGIN TRY")
                .AppendLine("	INSERT INTO [dbo].[ttsHistoricalSalesImport]")
                .AppendLine("	SELECT")
                .AppendLine("		 @PollDate AS [POLL_DATE]")
                .AppendLine("		,wsD.[BusinessDate] AS [BUSINESSDATE]")
                .AppendLine("		,tmS.[StoreID] AS [STOREID]")
                .AppendLine("		,'2013' AS [CODE]")
                .AppendLine("		,'NetSales' AS [DESCRIPTOR]")
                .AppendLine("		,wsD.[CustomerCount] AS [COUNT]")
                .AppendLine("		,wsD.[NetSales] AS [AMOUNT]")
                .AppendLine("		,NULL AS [RESET_CNT]")
                .AppendLine("		,1 AS [Updated]")
                .AppendLine("		,'Historical Sales' AS [ImportFile]")
                .AppendLine("	FROM")
                .AppendLine("		@WorksheetData AS [wsD]")
                .AppendLine("		INNER JOIN [dbo].[tmStore] AS [tmS] WITH(NOLOCK)")
                .AppendLine("			ON wsD.[StoreNum] = tmS.[StoreNum];")
                .AppendLine("")
                .AppendLine("	SELECT @@ROWCOUNT AS [RowsInserted];")
                .AppendLine("END TRY")
                .AppendLine("BEGIN CATCH")
                .AppendLine("	SELECT @@ROWCOUNT AS [RowsInserted], ERROR_MESSAGE() AS [ErrorMsg];")
                .AppendLine("END CATCH")

            End With

            InvWSS.GetData(sbSQL.ToString(), tabResults)

            If tabResults.Rows.Count > 0 Then

                'If no rows were inserted --> Display an error message
                'Else --> Send an email, and inform the user that the upload completed successfully
                If CInt(tabResults.Rows(0).Item("RowsInserted")) = 0 Then

                    lblGrid.Text = "An error ocurred while processing the workbook.<br /><br />Please ensure that there are no duplicate rows, and that this data has not been uploaded previously."
                    lblException.Text = tabResults.Rows(0).Item("ErrorMsg")

                Else

                    'If there are no query string parameters to suppress the email --> Send it
                    If Len(Request.QueryString("NoEmail")) = False Or Request.QueryString("NoEmail") <> 1 Then

                        Dim recipients As String = "<EMAIL>"
                        Dim msg As String = "Historical sales data was imported by " & GetUserFullName() & " ( " & Session("OrganizationName") & " ), And Is In the [dbo].[ttsHistoricalSalesImport] table."
                        Dim subject As String = "SBOnet - Historical Sales Import ( " & Session("OrganizationName") & " )"

                        SendMessage(recipients, msg, subject, False)

                    End If

                    lblGrid.CssClass = "required label success"
                    lblGrid.Text = "The workbook was processed successfully!<br /><br />An email has been sent To the SBOnet Team, And your sales data will be imported To SBOnet upon review."
                    lblException.Text = tabResults.Rows(0).Item("RowsInserted") & " rows inserted."

                End If

            End If

        Catch ex As Exception

            lblGrid.Text = "An Error ocurred While processing the workbook.<br /><br />Please ensure that there are no duplicate rows, And that this data has Not been uploaded previously."
            lblException.Text = ex.ToString()

        End Try

    End Sub

    Sub SendMessage(ByVal recipient As String, ByVal msg As String, ByVal subject As String, Optional ByVal bHTML As Boolean = True)

        Dim objMsg As New MailMessage("<EMAIL>", recipient) With {
            .From = New MailAddress("<EMAIL>", "SBOnet - Historical Sales Import"),
            .Body = msg,
            .Subject = subject,
            .IsBodyHtml = bHTML
        }

        Dim smtpClient As New SmtpClient("***********") With {
            .UseDefaultCredentials = False,
            .Credentials = New NetworkCredential("Sboalerts", "Alerts006!")
        }

        'smtpClient.Send(objMsg)
        SendMail(recipient, subject, msg)
    End Sub

    Private Function GetUserFullName() As String

        Dim fullName As String = "Unknown User"

        Try

            Dim sbSQL As New StringBuilder
            Dim tabResults As New DataTable

            With sbSQL

                .AppendLine("Select")
                .AppendLine("	[FirstName] + ' ' + [LastName] AS [FullName]")
                .AppendLine("FROM")
                .AppendLine("	[dbo].[tmAccount] WITH(NOLOCK)")
                .AppendLine("WHERE")
                .AppendLine("	[UniversalAccountIdentifier] = '" & Session("AccountID") & "';")

            End With

            InvWSS.GetData(sbSQL.ToString(), tabResults)

            If tabResults.Rows.Count > 0 Then
                fullName = tabResults.Rows(0).Item("FullName")
            End If

        Catch

            'Ignore errors...

        End Try

        Return fullName

    End Function

#End Region

End Class
