﻿Imports System.Data
Imports System.Data.SqlClient
Imports SBOTypeLibrary
Imports SBOWebSiteServices
Imports InventoryWebSiteServices
Imports System.Collections.Generic
Imports System.IO
Imports OfficeOpenXml

''' <summary>
''' 
''' </summary>
''' <remarks>
''' SUMMARY is a hackjob. It will force several report options to work correctly (Though won't change report options)
''' </remarks>
Partial Class SpeedOfServiceDetail
    Inherits System.Web.UI.Page

#Region "Fields"
    Private dictSOSAvgColors As Dictionary(Of Integer, Func(Of Double, String))
    Private dictRepPeriods As Dictionary(Of Integer, String)
    Private dictSOSTransVsCars As SortedDictionary(Of Integer, Tuple(Of Func(Of Double, Boolean), String))
    Private dictDayParts As Dictionary(Of Integer, Tuple(Of String, String))
    Private InvWSS As InventoryWebSiteServices.InventoryServices
    Private StartDate As Date
    Private EndDate As Date
#End Region

#Region "Types"
    Private Enum SOSReportCols
        Title
        DP1
        DP2
        DP3
        DP4
        DP5
        DP6
        Total
    End Enum
    Private Enum SOSExceptionCols
        Store
        PartialDays
        MissingDays
        TotalExceptions
    End Enum
    Private Enum SOSSummaryCols
        RowName
        Best
        Goals
    End Enum
#End Region

#Region "Templates"
    Private Const sTemplateExceptionsGrid As String = "{0:D0} ({1:P1})"
    Private Const sTemplateBest As String = "<p><b>Avg. Time:</b> {0}</p><p><b>Store:</b> {1}</p><p><b>Day:</b> {2}</p><p><b>MoD:</b> {4}</p>"
    Private Const sTemplateGoals As String = "<p><b>SOS Goal:</b>{0}</p><p><b>Days Meeting Goal:</b> {1} ({2:P1})</p>"
    Private Const sTemplateLegend As String = "<p style='{3}'><span class='indicator' style='background-color:{0}'>&nbsp;</span> {2} {1} </p>"
#End Region

#Region "Props"
    Protected ReadOnly Property CanGroupByDistrict As Boolean
        Get
            If ListCheckBoxList(cblDistricts).Count <= 1 Then Return False
            If IsSingleStoreMode Then Return False
            Return chkGroupDistrict.Checked
        End Get
    End Property
    Protected ReadOnly Property CanGroupByRegion As Boolean
        Get
            If ListCheckBoxList(cblDistricts).Count <= 1 Then Return False
            If ListCheckBoxList(cblRegions).Count <= 1 Then Return False
            If IsSingleStoreMode Then Return False
            Return chkGroupRegion.Checked
        End Get
    End Property
    Protected ReadOnly Property CanGroupByStore As Boolean
        Get
            If IsSummaryMode Then Return False
            Return True
        End Get
    End Property
    Protected ReadOnly Property CanGroupByTimeSpan As Boolean
        Get
            'Summary always groups on timespan
            If IsSummaryMode Then Return True
            'section by time span, with a week timespan, grouped by week
            If ddlDateType.SelectedValue = 1 AndAlso ddlGroupByDate.SelectedValue = 1 Then Return False
            If radioStore.Checked Then Return False
            Return True
        End Get
    End Property
    Protected ReadOnly Property IsSummaryMode As Boolean
        Get
            Return ddlGroupByDate.SelectedValue = 3
        End Get
    End Property
    Protected ReadOnly Property IsSingleStoreMode As Boolean
        Get
            Return ListCheckBoxList(cblStores).Count = 1
        End Get
    End Property
#End Region

    Sub Page_Load(ByVal sender As Object, ByVal e As EventArgs) Handles Me.Load
        Response.AddHeader("X-UA-Compatible", "IE=Edge") 'Needed for IE old
        InvWSS = Session("IWSS")

        If String.IsNullOrEmpty(ViewState("DecimalFormatting")) Then
            ViewState("DecimalFormatting") = InvWSS.Platform.PersistentValue("InventoryConfig", "DecimalFormattingSOSReport")
        End If

        FetchDayPartHeaders()
        pvSOSAvgColors()
        pvSOSTransVsCars()

        dictRepPeriods = New Dictionary(Of Integer, String)

        If Not IsPostBack Then
            tbEndDate.Text = Date.Today.Date.ToShortDateString()
            tbStartDate.Text = Date.Today.AddDays(-3).Date.ToShortDateString()
            PopRegions()
            PopYears()
            PopPeriods()
            PopWeeks()
        End If
    End Sub

#Region "Pop Funcs"
    Sub PopRegions()
        cblRegions.Items.Clear()

        Dim tabRegions As DataTable
        Dim li As System.Web.UI.WebControls.ListItem
        fsRegions.Visible = True

        tabRegions = InvWSS.GetPermittedRegions()

        For Each row As DataRow In tabRegions.Rows
            li = New ListItem(row("Region"), row("RegionNum"))
            cblRegions.Items.Add(li)

            If tabRegions.Rows.Count = 1 Then
                li.Selected = True
                fsRegions.Visible = False
                PopDistricts(New List(Of String))

                chkGroupRegion.Checked = False
                chkGroupRegion.Visible = False
            End If
        Next
        tabRegions.Dispose()
    End Sub
    Sub PopDistricts(ByRef Selected As List(Of String))
        cblDistricts.Items.Clear()

        lblDistrict.Text = ""
        lblDistrict.Visible = False
        fsDistricts.Visible = True

        Dim bShowRegionInDistrict As Boolean = False

        Dim iNumDistricts As Integer = 0

        Dim liRegionsSelected As List(Of String) = ListCheckBoxList(cblRegions)

        bShowRegionInDistrict = liRegionsSelected.Count > 1

        Dim tabDistricts As DataTable
        Dim li As System.Web.UI.WebControls.ListItem
        If liRegionsSelected.Count > 0 Then
            For Each Region As ListItem In cblRegions.Items
                If Region.Selected Then
                    tabDistricts = InvWSS.GetPermittedDistricts(Region.Value)
                    If Not String.IsNullOrEmpty(InvWSS.ErrorMessage) Then Throw New ApplicationException(InvWSS.ErrorMessage)
                    iNumDistricts += tabDistricts.Rows.Count

                    If tabDistricts.Rows.Count > 0 Then

                        li = New ListItem(Region.Text) With {.Enabled = False}
                        cblDistricts.Items.Add(li)

                        For Each row As DataRow In tabDistricts.Rows
                            li = New ListItem(row("District").ToString, row("DistrictNum").ToString) With {.Selected = Selected.Contains(li.Value)}
                            cblDistricts.Items.Add(li)
                        Next
                    End If

                    If Not tabDistricts Is Nothing Then
                        tabDistricts.Dispose()
                    End If
                End If

            Next

            Select Case iNumDistricts
                Case 0
                    lblDistrict.Text = "No Districts found in the selected Region(s)."
                    lblDistrict.Visible = True
                Case 1
                    fsDistricts.Visible = False
                    chkGroupDistrict.Visible = False
                    chkGroupDistrict.Checked = False
                    cblDistricts.Items(1).Selected = True
                    PopStores(ListCheckBoxList(cblStores))
                Case Else
                    PopStores(ListCheckBoxList(cblStores))
            End Select
        Else
            fsDistricts.Visible = False
            fsStores.Visible = False
        End If
    End Sub
    Sub PopStores(ByRef Selected As List(Of String))
        fsStores.Visible = False
        lblStore.Text = ""
        lblStore.Visible = False

        cblStores.Items.Clear()

        Dim sbStores As New StringBuilder
        Dim tabStores As New DataTable
        Dim li As ListItem

        Dim sDistricts As String = CommaDelimitedCheckBoxList(cblDistricts)
        Dim sRegions As String = CommaDelimitedCheckBoxList(cblRegions)

        If Not (String.IsNullOrEmpty(sDistricts) Or String.IsNullOrEmpty(sRegions)) Then
            fsStores.Visible = True

            With sbStores

                .AppendLine("SELECT")
                .AppendLine("   STO.[StoreID]")
                .AppendLine("  ,[StoreDescription]")
                .AppendLine("  ,INF.DTTimerReady")
                .AppendLine("FROM")
                .AppendLine("  [dbo].[tmStore] STO WITH(NOLOCK)")
                .AppendLine("  INNER JOIN [tmStoreInvt] [INF] WITH(NOLOCK)")
                .AppendLine("       ON STO.StoreID = INF.StoreID")
                .AppendLine("WHERE")
                .AppendLine("  DTTimerTypeID <> 4 AND")
                .AppendLine("  STO.[StoreID] IN")
                .AppendLine("	(")
                .AppendLine("	    SELECT")
                .AppendLine("	        tmS.[StoreID]")
                .AppendLine("	    FROM")
                .AppendLine("	        [dbo].[WEBRPT_vjBinPermissions] AS [vjBP] WITH(NOLOCK)")
                .AppendLine("		    INNER JOIN [dbo].[tmStore] AS [tmS] WITH(NOLOCK)")
                .AppendLine("			    ON vjBP.[UniversalStoreIdentifier] = tmS.[UniversalStoreIdentifier]")
                .AppendLine("		    INNER JOIN [dbo].[tmNode] AS [tmN] WITH(NOLOCK)")
                .AppendLine("			    ON tmS.[UniversalNodeIdentifier] = tmN.[UniversalNodeIdentifier]")
                .AppendLine("      WHERE")
                .AppendLine("          vjBP.[BinLevel] = 1")
                .AppendLine("	        AND tmN.[ActiveNode] <> 0 ")
                .AppendLine("	        AND vjBP.[UniversalAccountIdentifier] = '" & Session("AccountID") & "'")

                If Not String.IsNullOrEmpty(sRegions) Then
                    .AppendLine("	AND vjBP.[UniversalRegionIdentifier] IN ( SELECT [UniversalRegionIdentifier] FROM [dbo].[tmRegion] WITH(NOLOCK) WHERE [RegionNum] IN (" & sRegions & ") )")
                End If

                If Not String.IsNullOrEmpty(sDistricts) Then
                    .AppendLine("	AND vjBP.[UniversalDistrictIdentifier] IN ( SELECT [UniversalDistrictIdentifier] FROM [dbo].[tmDistrict] WITH(NOLOCK) WHERE [DistrictNum] IN (" & sDistricts & ") )")
                End If

                .AppendLine("	)")
                .AppendLine("ORDER BY")
                .AppendLine("   [StoreNum]")

            End With

            InvWSS.GetData(sbStores.ToString, tabStores)
            If Not String.IsNullOrEmpty(InvWSS.ErrorMessage) Then Throw New ApplicationException(InvWSS.ErrorMessage)
        End If


        If Not tabStores Is Nothing Then
            If tabStores.Rows.Count = 0 Then

                lblStore.Text = "You do not have access to any stores in the selected Region/District."
                lblStore.Visible = True

            Else 'Else --> Populate the store list

                For Each row As DataRow In tabStores.Rows
                    If chkHideNotReady.Checked AndAlso Not row("DTTimerReady") Then Continue For
                    li = New System.Web.UI.WebControls.ListItem(String.Format(If(row("DTTimerReady"), "{0}", "<span class='NotDTTimerReady'>{0}*</span>"), row("StoreDescription")))
                    li.Value = row("StoreID")
                    cblStores.Items.Add(li)
                    li.Selected = Selected.Contains(li.Value)

                Next

                tabStores.Dispose()
                li = Nothing

            End If

        Else
            If String.IsNullOrEmpty(sDistricts) Or String.IsNullOrEmpty(sRegions) Then
                lblStore.Text = "No districts selected" 'This is mostly a guess.
            Else
                lblStore.Text = "An unknown error occured fetching stores."
            End If

            lblStore.Visible = True
        End If
    End Sub
    Sub PopYears()

        ddlYear.Items.Clear()

        Dim sSQL As New StringBuilder
        Dim tTable As New DataTable
        Dim litem As ListItem

        sSQL.AppendLine("SELECT")
        sSQL.AppendLine("	 tmRP.[ReportingYear]")
        sSQL.AppendLine("	,MIN(tmRP.[ReportingStartDate]) AS [ReportingStartDate]")
        sSQL.AppendLine("	,MAX(tmRP.[ReportingEndDate]) AS [ReportingEndDate]")
        sSQL.AppendLine("FROM [dbo].[tmReportingPeriod] AS [tmRP] WITH(NOLOCK)")
        sSQL.AppendLine("WHERE ReportingStartDate < GETDATE()")
        sSQL.AppendLine("GROUP BY tmRP.[ReportingYear]")
        sSQL.AppendLine("ORDER BY ReportingYear DESC")

        InvWSS.GetData(sSQL.ToString, tTable)
        If Not String.IsNullOrEmpty(InvWSS.ErrorMessage) Then Throw New ApplicationException(InvWSS.ErrorMessage)

        For Each row As DataRow In tTable.Rows
            litem = New ListItem(row("ReportingYear").ToString, row("ReportingYear").ToString) With
                    {.Selected = (row.Item("ReportingStartDate") <= Now.Date.AddDays(-7) And row.Item("ReportingEndDate") >= Now.Date.AddDays(-7))}
            ddlYear.Items.Add(litem)
        Next

    End Sub
    Sub PopPeriods()
        ddlPeriod.Items.Clear()
        Dim strSQL As String
        Dim tTable As New DataTable
        Dim PeriodItem As ListItem
        strSQL = "SELECT * FROM pf_GetReportingPeriodsForYear(" & ddlYear.SelectedItem.Text & ") where ReportingStartDate < GETDATE()"

        InvWSS.GetData(strSQL, tTable)
        If Not String.IsNullOrEmpty(InvWSS.ErrorMessage) Then Throw New ApplicationException(InvWSS.ErrorMessage)

        For Each row As DataRow In tTable.Rows
            PeriodItem = New ListItem(CDate(row("ReportingStartDate")).ToShortDateString & " - " & CDate(row("ReportingEndDate")).ToShortDateString, row("ReportingPeriod")) With
                {.Selected = row("ReportingStartDate") <= Now.Date.AddDays(-7) And row("ReportingEndDate") >= Now.Date.AddDays(-7)}
            ddlPeriod.Items.Add(PeriodItem)
        Next

    End Sub
    Sub PopWeeks()

        ddlWeek.Items.Clear()

        Dim sSQL As New StringBuilder  'Dim sSQL As String
        Dim tTable As New DataTable
        Dim litem As ListItem

        sSQL.AppendLine("SELECT")
        sSQL.AppendLine("	 tmRP.[ReportingWeekNum]")
        sSQL.AppendLine("	,tmRP.[ReportingPeriodID]")
        sSQL.AppendLine("	,tmRP.[ReportingStartDate]")
        sSQL.AppendLine("	,tmRP.[ReportingEndDate]")
        sSQL.AppendLine("FROM [dbo].[tmReportingPeriod] AS [tmRP] WITH(NOLOCK)")
        sSQL.AppendLine("WHERE tmRP.[ReportingYear] = '" & ddlYear.SelectedValue & "' AND tmRP.ReportingPeriod = " & ddlPeriod.SelectedValue & " ")
        sSQL.AppendLine("AND tmRP.[ReportingStartDate] <= GETDATE()")

        InvWSS.GetData(sSQL.ToString, tTable)
        If Not String.IsNullOrEmpty(InvWSS.ErrorMessage) Then Throw New ApplicationException(InvWSS.ErrorMessage)

        For Each row As DataRow In tTable.Rows
            litem = New ListItem(CDate(row("ReportingStartDate")).ToShortDateString & " - " & CDate(row("ReportingEndDate")).ToShortDateString, row("ReportingStartDate")) With
                {.Selected = row("ReportingStartDate") <= Now.Date.AddDays(-7) And row("ReportingEndDate") >= Now.Date.AddDays(-7)}
            ddlWeek.Items.Add(litem)
        Next
    End Sub
#End Region

#Region "Persistent Values"
    Private Sub pvSOSAvgColors()
        'Funcs aren't serializable
        dictSOSAvgColors = New Dictionary(Of Integer, Func(Of Double, String))()

        If ViewState("SOSAvg6DP") Is Nothing Then
            ViewState("SOSAvg6DP") = InvWSS.Platform.PersistentValue("InventoryConfig", "SOSAvg6DP")
        End If

        Dim sTempString As String = ViewState("SOSAvg6DP")
        Dim sTolSplit = Split(sTempString, ";")
        Dim sRowSplit(sTolSplit.Length - 1) As Array

        For i As Integer = 0 To sTolSplit.Length - 1
            sRowSplit(i) = Split(sTolSplit(i), ",")
        Next

        Dim iRows As Integer = sRowSplit.Length
        For i As Integer = 0 To 6

            Dim dictValues As New SortedDictionary(Of Double, String)(Comparer(Of Double).Default)
            For j As Integer = 0 To iRows - 1
                'Multiply by -1 to reverse the sort order, so the "Highest" number is now the "lowest" number for the purpose of sorting.
                'This works around the fact that we don't have a reverse comparer in .Net, and I don't want to add a new class for such a thing.
                dictValues.Add(sRowSplit(j)(i * 2) * -1, sRowSplit(j)(i * 2 + 1))
            Next j

            Dim Uptown As Func(Of Double, String) = Function(Value As Double) As String
                                                        If Value <= 0 Then Return "#004eff" 'Please don't break physics
                                                        For Each Key As Double In dictValues.Keys
                                                            If Value >= (Key * -1) Then Return dictValues(Key)
                                                        Next
                                                        Return "#004eff"
                                                    End Function
            dictSOSAvgColors.Add(i, Uptown)

            'Here we create the legend, it's some raw HTML generics because that's what is easiest considering the dynamic nature.
            If dictDayParts.ContainsKey(i + 1) OrElse i = 6 Then
                'If the daypart actually exists (or this is the total part of the legend)

                Dim divLegendBlock As New HtmlGenericControl("DIV")
                divLegendBlock.Attributes.Add("class", "legend-block")

                For Each Key As Integer In dictDayParts.Keys
                    grdSOSReport.Columns(SOSReportCols.DP1 + Key - 1).Visible = True
                Next

                Dim sbBlockContents As New StringBuilder

                If i = 6 Then
                    sbBlockContents.AppendLine("<p style='text-align:center;'><b>Total</b></p>")
                    divLegendBlock.Attributes.Add("class", "legend-block last")
                Else
                    sbBlockContents.AppendLine("<p style='text-align:center;'><b>" & dictDayParts(i + 1).Item1 & "</b></p>")
                End If

                For Each Key As Double In dictValues.Keys
                    sbBlockContents.AppendLine(String.Format(sTemplateLegend, dictValues(Key).ToString, (-1 * Key).ToString, If(Key = 0, ">&nbsp;", ">="), "text-align:left;"))
                Next Key

                divLegendBlock.InnerHtml = sbBlockContents.ToString

                divAverageLegend.Controls.Add(divLegendBlock)
            End If
        Next i

        divAverageLegend.Controls.Add(New LiteralControl(String.Format(sTemplateLegend, "#004eff", "Missing Data", String.Empty, String.Empty)))
    End Sub
    Private Sub pvSOSTransVsCars()

        dictSOSTransVsCars = New SortedDictionary(Of Integer, Tuple(Of Func(Of Double, Boolean), String))()
        If ViewState("dictSOSTransVsCars") Is Nothing Then
            ViewState("dictSOSTransVsCars") = InvWSS.Platform.PersistentValue("InventoryConfig", "SOSTransVsCars")
        End If

        Dim i As Integer = 0
        Dim j As Integer = 0
        Dim aryArgs(3, 2) As String

        Dim sTransTol() As String = Split(ViewState("dictSOSTransVsCars"), ",")
        If sTransTol.GetUpperBound(0) > 0 Then
            'Populates aryArgs from with the information in sTransTol from the PV
            For Each item As String In sTransTol
                aryArgs(i, j) = item.ToString
                j += 1
                If j = 3 Then
                    i += 1
                    j = 0
                End If
            Next

            'Creates the legend filling in the values held in the PV.
            Dim divLegendBlock As New HtmlGenericControl("DIV")
            divLegendBlock.Attributes.Add("class", "legend-block last")
            divLegendBlock.Style.Add("width", "auto")

            Dim sbBlockContents As New StringBuilder
            For iCounter As Integer = 0 To 3
                'sets sAmount to the tolerance value

                Dim sSymbol = aryArgs(iCounter, 0).ToString
                Dim sAmount = aryArgs(iCounter, 1).ToString
                Dim sHTMLColor = aryArgs(iCounter, 2).ToString

                'LEGENDARY
                sbBlockContents.AppendLine(String.Format(sTemplateLegend, sHTMLColor, sAmount, sSymbol, "text-align:left;display:inline;"))

                'Lets do some magic
                Dim tup As New Tuple(Of Func(Of Double, Boolean), String)(Function(Value As Double) As Boolean
                                                                              Return False
                                                                          End Function, "#004eff")
                Select Case sSymbol
                    Case "<="
                        tup = New Tuple(Of Func(Of Double, Boolean), String)(Function(Value As Double) As Boolean
                                                                                 Return Value <= sAmount
                                                                             End Function, sHTMLColor)
                    Case "="
                        tup = New Tuple(Of Func(Of Double, Boolean), String)(Function(Value As Double) As Boolean
                                                                                 Return Value = sAmount
                                                                             End Function, sHTMLColor)
                    Case ">="
                        tup = New Tuple(Of Func(Of Double, Boolean), String)(Function(Value As Double) As Boolean
                                                                                 Return Value >= sAmount
                                                                             End Function, sHTMLColor)
                    Case ">"
                        tup = New Tuple(Of Func(Of Double, Boolean), String)(Function(Value As Double) As Boolean
                                                                                 Return Value > sAmount
                                                                             End Function, sHTMLColor)
                    Case "<"
                        tup = New Tuple(Of Func(Of Double, Boolean), String)(Function(Value As Double) As Boolean
                                                                                 Return Value < sAmount
                                                                             End Function, sHTMLColor)
                    Case "<>", "!="
                        tup = New Tuple(Of Func(Of Double, Boolean), String)(Function(Value As Double) As Boolean
                                                                                 Return Value <> sAmount
                                                                             End Function, sHTMLColor)
                    Case "+/-", "+-"
                        tup = New Tuple(Of Func(Of Double, Boolean), String)(Function(Value As Double) As Boolean
                                                                                 Return Math.Abs(Value) <= sAmount
                                                                             End Function, sHTMLColor)
                    Case Else
                        Throw New ApplicationException("Unrecognized Symbol: '" & sSymbol & "'")
                End Select
                dictSOSTransVsCars.Add(iCounter, tup)
            Next
            sbBlockContents.AppendLine(String.Format(sTemplateLegend, "#004eff", "Missing Data", String.Empty, "text-align:left;display:inline;"))

            divLegendBlock.InnerHtml = sbBlockContents.ToString

            divTransLegend.Controls.Add(divLegendBlock)
        End If
    End Sub
#End Region

#Region "Report Options"
    Private Sub ValidateReportOptions()
        lblSubHeader.Text = String.Empty
        lblSubHeader.Visible = True
        lblStore.Visible = False
        lblRegion.Visible = False
        lblDistrict.Visible = False

        If ListCheckBoxList(cblRegions).Count = 0 Then
            lblRegion.Text = "No Regions Selected"
            lblRegion.Visible = True
            Throw New ApplicationException("No Regions Selected")
        End If

        If ListCheckBoxList(cblDistricts).Count = 0 Then
            lblDistrict.Text = "No districts Selected"
            lblDistrict.Visible = True
            Throw New ApplicationException("No districts Selected")
        End If

        If ListCheckBoxList(cblStores).Count = 0 Then
            lblStore.Text = "No Stores Selected"
            lblStore.Visible = True
            Throw New ApplicationException("No Stores Selected")
        End If

        divTransLegend.Visible = chkCompareTransactions.Checked
        divAverageLegend.Visible = Not chkCompareTransactions.Checked
        lblSubHeader.Text &= "<b>Date Range:</b>"
        Select Case ddlDateType.SelectedValue
            Case 1
                lblSubHeader.Text &= ddlWeek.SelectedItem.Text & "<br/>"
                Exit Select
            Case 2
                lblSubHeader.Text &= ddlPeriod.SelectedItem.Text & "<br/>"
                Exit Select
            Case 3
                lblSubHeader.Text &= CDate(tbStartDate.Text).ToShortDateString & " - " & CDate(tbEndDate.Text).ToShortDateString & "<br/>"
                Exit Select
        End Select
    End Sub
    Protected Sub cblDistricts_SelectedIndexChanged(sender As Object, e As EventArgs)
        PopStores(ListCheckBoxList(cblStores))
    End Sub
    Protected Sub cblRegions_SelectedIndexChanged(sender As Object, e As EventArgs)
        PopDistricts(ListCheckBoxList(cblDistricts))
    End Sub
    Protected Sub ddlPeriod_SelectedIndexChanged(sender As Object, e As EventArgs)
        If ddlWeek.Visible Then
            PopWeeks()
        End If
    End Sub
    Protected Sub ddlYear_SelectedIndexChanged(sender As Object, e As EventArgs)
        If ddlPeriod.Visible Then PopPeriods()
        If ddlWeek.Visible Then PopWeeks()
    End Sub
    Protected Sub chkHideNotReady_CheckedChanged(sender As Object, e As EventArgs)
        PopStores(ListCheckBoxList(cblStores))
    End Sub
    Protected Sub ddlDateType_SelectedIndexChanged(sender As Object, e As EventArgs)
        Select Case ddlDateType.SelectedValue
            Case 1 'Week
                RptOptionYr.Visible = True
                RptOptionPd.Visible = True
                RptOptionWk.Visible = True
                RptOptionEndDate.Visible = False
                RptOptionStartDate.Visible = False
                PopWeeks()
            Case 2 'Period
                RptOptionYr.Visible = True
                RptOptionPd.Visible = True
                RptOptionWk.Visible = False
                RptOptionEndDate.Visible = False
                RptOptionStartDate.Visible = False

            Case 3
                RptOptionYr.Visible = False
                RptOptionPd.Visible = False
                RptOptionWk.Visible = False
                RptOptionEndDate.Visible = True
                RptOptionStartDate.Visible = True
        End Select
    End Sub
    Protected Sub btnRunReport_Click(sender As Object, e As EventArgs)
        If BindPrimaryGrid() Then
            BindSummaryGrid()
            showsummary.Visible = True
            btnExportSummary.Visible = True
        Else
            grdStoreSummary.DataSource = Nothing
            grdStoreSummary.DataBind()
            showsummary.Visible = False
            btnExportSummary.Visible = False
        End If
    End Sub
#End Region

#Region "Utility"
    ''' <summary>
    ''' Returns a comma delimited string of selected values from a given checkboxlist
    ''' </summary>
    ''' <param name="CBL">Check Box List</param>
    ''' <returns>comma delimited string of selected values</returns>
    ''' <remarks></remarks>
    Function CommaDelimitedCheckBoxList(ByRef CBL As CheckBoxList, Optional ByVal UseText As Boolean = False) As String
        CommaDelimitedCheckBoxList = ""
        For Each LI As ListItem In CBL.Items
            If LI.Selected Then
                CommaDelimitedCheckBoxList &= IIf(String.IsNullOrEmpty(CommaDelimitedCheckBoxList), "", ",") & IIf(UseText, LI.Text, LI.Value)
            End If

        Next LI
    End Function

    ''' <summary>
    ''' Returns a list of strings of selected values in a given checkboxlist
    ''' </summary>
    ''' <param name="CBL">Check Box List</param>
    ''' <returns>List(Of String) containing selected values</returns>
    ''' <remarks></remarks>
    Function ListCheckBoxList(ByRef CBL As CheckBoxList) As List(Of String)
        ListCheckBoxList = New List(Of String)()
        For Each LI As ListItem In CBL.Items
            If LI.Selected Then
                ListCheckBoxList.Add(LI.Value)
            End If
        Next LI
    End Function
#End Region

#Region "Grid"
    Private Sub BindSummaryGrid()

        '''''''''''''''''''''''''''''''
        '   Speed of Service Report   '
        '''''''''''''''''''''''''''''''
        Try

            Dim sbSQL As New StringBuilder
            Dim tabResults As New DataTable
            Dim sCTEs As String 'This will later be given a value, right before --Main Query in the query below. It's used to save the CTEs for the second summary grid.


            Dim tmp() As String

            'Doing this ugly voodoo saves us from having to fetch the start/end date from the reporting period tables
            Select Case ddlDateType.SelectedValue
                Case 1
                    tmp = ddlWeek.SelectedItem.Text.Split(" ")
                Case 2
                    tmp = ddlPeriod.SelectedItem.Text.Split(" ")
                Case 3
                    tmp = {tbStartDate.Text, "-", tbEndDate.Text}

            End Select
            StartDate = CDate(tmp(0).Trim())
            EndDate = CDate(tmp(2).Trim())

            With sbSQL

                .AppendLine("DECLARE @StoreDateList AS StoreDateList")
                .AppendLine("DECLARE @StartDate DATE")
                .AppendLine("DECLARE @EndDate DATE")
                .AppendLine("DECLARE @Stores TABLE ([StoreID] INT)")
                .AppendLine("Declare @UniversalAccountIdentifier varchar(32)")


                .AppendLine("Set @UniversalAccountIdentifier = '" & InvWSS.Platform.Application.AccountIdentifier & "'")


                .AppendLine(String.Format("SET @StartDate = '{0}'", StartDate.ToShortDateString))
                .AppendLine(String.Format("SET @EndDate = '{0}'", EndDate.ToShortDateString))

                .AppendLine("DECLARE @PermittedStores TABLE")
                .AppendLine("	([StoreID] INT")
                .AppendLine("	,[StoreNum] INT")
                .AppendLine("	,[StoreDescription] VARCHAR(50)")
                .AppendLine("	,[UniversalStoreIdentifier]  VARCHAR(32)")
                .AppendLine("	,[ManagerName] VARCHAR(50)")
                .AppendLine("	,[Permitted] BIT")
                .AppendLine("	,[ViewReports] BIT")
                .AppendLine("	,[AlterData] BIT")
                .AppendLine("	)")

                .AppendLine("INSERT @PermittedStores")
                .AppendLine("	([StoreID]")
                .AppendLine("	,[StoreNum]")
                .AppendLine("	,[StoreDescription]")
                .AppendLine("	,[UniversalStoreIdentifier]")
                .AppendLine("	,[ManagerName]")
                .AppendLine("	,[Permitted]")
                .AppendLine("	,[ViewReports]")
                .AppendLine("	,[AlterData]")
                .AppendLine("	)")
                .AppendLine("EXECUTE [dbo].[WEBRPT_ps_di_GetPermittedStores] '', '', '', @UniversalAccountIdentifier;")

                .AppendLine("INSERT @Stores([StoreID])")
                .AppendLine("SELECT [StoreID]")
                .AppendLine("FROM @PermittedStores")
                .AppendLine("WHERE [StoreID] IN (" & CommaDelimitedCheckBoxList(cblStores) & ");")

                .AppendLine("WITH DateRange_CTE AS")
                .AppendLine("	(SELECT @StartDate AS [BusinessDate]")
                .AppendLine("	UNION ALL")
                .AppendLine("	SELECT DATEADD(dd, 1, [BusinessDate])")
                .AppendLine("	FROM DateRange_CTE")
                .AppendLine("	WHERE [BusinessDate] < @EndDate")
                .AppendLine("	)")
                .AppendLine("INSERT @StoreDateList")
                .AppendLine("	([StoreID]")
                .AppendLine("	,[BusinessDate]")
                .AppendLine("	)")
                .AppendLine("SELECT")
                .AppendLine("	 [StoreID]")
                .AppendLine("	,[BusinessDate]")
                .AppendLine("FROM DateRange_CTE")
                .AppendLine("	CROSS JOIN @Stores")
                .AppendLine("ORDER BY [BusinessDate];")


                .AppendLine("WITH TimerData AS")
                .AppendLine("( -- Basic timer data")
                .AppendLine("	SELECT")
                .AppendLine("		 StoreID")
                .AppendLine("		,BusinessDate")
                .AppendLine("		,Tag")
                .AppendLine("		,Value")
                .AppendLine("	FROM")
                .AppendLine("		[pf_GetDTTimerData](@StoreDateList)")
                .AppendLine("),")

                .AppendLine("DayPartsRecuse AS (")
                .AppendLine("        SELECT 1 AS [Sequence]")
                .AppendLine("        UNION ALL")
                .AppendLine("        SELECT [Sequence] + 1 FROM DayPartsRecuse WHERE [Sequence] < 7")
                .AppendLine("),")
                .AppendLine("DayPartsExist AS (")
                .AppendLine("	SELECT")
                .AppendLine("		[PVT].[1]")
                .AppendLine("	   ,[PVT].[2]")
                .AppendLine("	   ,[PVT].[3]")
                .AppendLine("	   ,[PVT].[4]")
                .AppendLine("	   ,[PVT].[5]")
                .AppendLine("	   ,[PVT].[6]")
                .AppendLine("	   ,[PVT].[7]")
                .AppendLine("	FROM")
                .AppendLine("		(")
                .AppendLine("			SELECT DISTINCT")
                .AppendLine("				ISNULL( [TDP].[Sequence], 0 ) AS [Exists]")
                .AppendLine("			   ,DayPartsRecuse.[Sequence]")
                .AppendLine("			FROM [dbo].[ttmDayPart] [TDP]")
                .AppendLine("                INNER JOIN [dbo].[ttmTimeRange] [TTR] ON [TTR].[DayPartID] = [TDP].[DayPartID]")
                .AppendLine("                INNER JOIN [dbo].[pf_GetDTTimerTag]() [PGDTT] ON [TDP].[Tag] = [PGDTT].[DayPartTag] AND [PGDTT].[TimerTag] LIKE 'SOSDP_T'")
                .AppendLine("				 RIGHT JOIN DayPartsRecuse")
                .AppendLine("					 ON DayPartsRecuse.[Sequence] = REPLACE(REPLACE([PGDTT].[TimerTag],'SOSDP',''),'T','')")
                .AppendLine("")
                .AppendLine("		)[DAT]")
                .AppendLine("	PIVOT")
                .AppendLine("		(")
                .AppendLine("			MAX([Exists])")
                .AppendLine("			FOR [Sequence] IN")
                .AppendLine("			  ( [1], [2], [3], [4], [5], [6], [7] )")
                .AppendLine("		)[PVT]")
                .AppendLine("),")
                .AppendLine("TimerDataPivoted AS")
                .AppendLine("( -- Pivot timer data")
                .AppendLine("	SELECT ")
                .AppendLine("		 CASE WHEN [DPE].[1] <> 0 THEN PV.SOSDP1T ELSE 0 END [SOSDP1T]")
                .AppendLine("		,CASE WHEN [DPE].[2] <> 0 THEN PV.SOSDP2T ELSE 0 END [SOSDP2T]")
                .AppendLine("		,CASE WHEN [DPE].[3] <> 0 THEN PV.SOSDP3T ELSE 0 END [SOSDP3T]")
                .AppendLine("		,CASE WHEN [DPE].[4] <> 0 THEN PV.SOSDP4T ELSE 0 END [SOSDP4T]")
                .AppendLine("		,CASE WHEN [DPE].[5] <> 0 THEN PV.SOSDP5T ELSE 0 END [SOSDP5T]")
                .AppendLine("		,CASE WHEN [DPE].[6] <> 0 THEN PV.SOSDP6T ELSE 0 END [SOSDP6T]")
                .AppendLine("		,PV.SOSDP8T")
                .AppendLine("		,PV.StoreID")
                .AppendLine("		,PV.BusinessDate")
                .AppendLine("	FROM")
                .AppendLine("		(")
                .AppendLine("			(")
                .AppendLine("				SELECT")
                .AppendLine("					 StoreID")
                .AppendLine("					,BusinessDate")
                .AppendLine("					,Tag")
                .AppendLine("					,Value")
                .AppendLine("				FROM")
                .AppendLine("					TimerData")
                .AppendLine("			)")
                .AppendLine("		) AS DATA")
                .AppendLine("	PIVOT")
                .AppendLine("	(")
                .AppendLine("		SUM(DATA.Value)")
                .AppendLine("		FOR DATA.Tag IN ([SOSDP1T]")
                .AppendLine("						,[SOSDP2T]")
                .AppendLine("						,[SOSDP3T]")
                .AppendLine("						,[SOSDP4T]")
                .AppendLine("						,[SOSDP5T]")
                .AppendLine("						,[SOSDP6T]")
                .AppendLine("						,[SOSDP8T])")
                .AppendLine("	) PV")
                .AppendLine("")
                .AppendLine("	CROSS JOIN [DayPartsExist] [DPE]")
                .AppendLine("),")

                .AppendLine("STAFData AS")
                .AppendLine("( -- MGR Data (Not used when grouping)")
                .AppendLine("	SELECT")
                .AppendLine("		 Tag")
                .AppendLine("		,TextValue")
                .AppendLine("		,BusinessDate")
                .AppendLine("		,STOREID")
                .AppendLine("		,ROW_NUMBER() OVER(PARTITION BY BusinessDate,Tag,StoreID ORDER BY BusinessDate DESC) [Ordinal]")
                .AppendLine("	FROM vjStoreAndForwardDetail STAFD WITH(NOLOCK)")
                .AppendLine("	WHERE")
                .AppendLine("		TAG LIKE 'MGR__'")
                .AppendLine("),")
                .AppendLine("MGRDataPivoted AS")
                .AppendLine("( -- Pivoted, to join to main query")
                .AppendLine("	SELECT")
                .AppendLine("		 PV.MGR01")
                .AppendLine("		,PV.MGR02")
                .AppendLine("		,PV.MGR03")
                .AppendLine("		,PV.MGR04")
                .AppendLine("		,PV.MGR05")
                .AppendLine("		,PV.MGR06")
                .AppendLine("		,PV.STOREID")
                .AppendLine("		,PV.BusinessDate")
                .AppendLine("	FROM ")
                .AppendLine("		(")
                .AppendLine("			SELECT")
                .AppendLine("				 Tag")
                .AppendLine("				,TextValue")
                .AppendLine("				,BusinessDate")
                .AppendLine("				,STOREID")
                .AppendLine("			FROM STAFData")
                .AppendLine("			WHERE Ordinal = 1")
                .AppendLine("		) DAT")
                .AppendLine("		PIVOT")
                .AppendLine("		(")
                .AppendLine("			MAX(TextValue)")
                .AppendLine("			FOR Tag IN ([MGR01],[MGR02],[MGR03],[MGR04],[MGR05],[MGR06])")
                .AppendLine("		) PV")
                .AppendLine("),")
                .AppendLine("SOSGoals AS (")
                .AppendLine("SELECT ")
                .AppendLine("	 StoreID")
                .AppendLine("	,SOSGoal1")
                .AppendLine("	,SOSGoal2")
                .AppendLine("	,SOSGoal3")
                .AppendLine("	,SOSGoal4")
                .AppendLine("	,SOSGoal5")
                .AppendLine("	,SOSGoal6")
                .AppendLine("	,DENSE_RANK() OVER(PARTITION BY StoreID ORDER BY EffectiveDate DESC) [ORDINAL]")
                .AppendLine("FROM ")
                .AppendLine("	tmSOSGoals WITH(NOLOCK)")
                .AppendLine("),")
                .AppendLine("SOSGoalsData AS (")
                .AppendLine("	SELECT ")
                .AppendLine("		-- SDL.StoreID,")
                .AppendLine("		 SUM(CASE WHEN ROUND(TDP.SOSDP1T,0) <= GOAL.SOSGoal1 THEN 1 ELSE 0 END) [SOSDP1-ATGOAL]")
                .AppendLine("		,AVG(GOAL.SOSGoal1) [SOSDP1-GOAL]")
                .AppendLine("")
                .AppendLine("		,SUM(CASE WHEN ROUND(TDP.SOSDP2T,0) <= GOAL.SOSGoal2 THEN 1 ELSE 0 END) [SOSDP2-ATGOAL]")
                .AppendLine("		,AVG(GOAL.SOSGoal2) [SOSDP2-GOAL]")
                .AppendLine("	")
                .AppendLine("		,SUM(CASE WHEN ROUND(TDP.SOSDP3T,0) <= GOAL.SOSGoal3 THEN 1 ELSE 0 END) [SOSDP3-ATGOAL]")
                .AppendLine("		,AVG(GOAL.SOSGoal3) [SOSDP3-GOAL]")
                .AppendLine("	")
                .AppendLine("		,SUM(CASE WHEN ROUND(TDP.SOSDP4T,0) <= GOAL.SOSGoal4 THEN 1 ELSE 0 END) [SOSDP4-ATGOAL]")
                .AppendLine("		,AVG(GOAL.SOSGoal4) [SOSDP4-GOAL]")
                .AppendLine("	")
                .AppendLine("		,SUM(CASE WHEN ROUND(TDP.SOSDP5T,0) <= GOAL.SOSGoal5 THEN 1 ELSE 0 END) [SOSDP5-ATGOAL]")
                .AppendLine("		,AVG(GOAL.SOSGoal5) [SOSDP5-GOAL]")
                .AppendLine("")
                .AppendLine("		,SUM(CASE WHEN ROUND(TDP.SOSDP6T,0) <= GOAL.SOSGoal6 THEN 1 ELSE 0 END) [SOSDP6-ATGOAL]")
                .AppendLine("		,AVG(GOAL.SOSGoal6) [SOSDP6-GOAL]")
                .AppendLine("	")
                .AppendLine("	FROM")
                .AppendLine("		@StoreDateList SDL ")
                .AppendLine("		LEFT JOIN TimerDataPivoted TDP")
                .AppendLine("			ON SDL.StoreID = TDP.StoreID")
                .AppendLine("			AND SDL.BusinessDate = TDP.BusinessDate")
                .AppendLine("		LEFT JOIN MGRDataPivoted MDP")
                .AppendLine("			ON SDL.StoreID = MDP.STOREID")
                .AppendLine("			AND SDL.BusinessDate = MDP.BusinessDate")
                .AppendLine("		LEFT JOIN tmStore STO WITH(NOLOCK)")
                .AppendLine("			ON STO.StoreID = SDL.StoreID")
                .AppendLine("		LEFT JOIN SOSGoals GOAL WITH(NOLOCK)")
                .AppendLine("			ON SDL.StoreID = GOAL.StoreID")
                .AppendLine("			AND GOAL.ORDINAL=1")
                .AppendLine("	WHERE")
                .AppendLine("	   SDL.BusinessDate < CAST(GETDATE() AS DATE)")
                .AppendLine(")")

                'Save all the above CTE stuff
                sCTEs = .ToString

                .AppendLine("--Main Query")
                .AppendLine("SELECT TOP 1")
                .AppendLine("	--DP1")
                .AppendLine("	FIRST_VALUE(TDP.SOSDP1T) OVER (ORDER BY CASE WHEN TDP.SOSDP1T = 0 OR TDP.SOSDP1T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP1T ASC) as [SOSDP1T-BEST-TIME],")
                .AppendLine("	FIRST_VALUE(STO.StoreDescription) OVER (ORDER BY CASE WHEN TDP.SOSDP1T = 0 OR TDP.SOSDP1T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP1T ASC) as [SOSDP1T-BEST-STO],")
                .AppendLine("	FIRST_VALUE(TDP.BusinessDate) OVER (ORDER BY CASE WHEN TDP.SOSDP1T = 0 OR TDP.SOSDP1T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP1T ASC) as [SOSDP1T-BEST-DATE],")
                .AppendLine("	ISNULL(FIRST_VALUE(MDP.MGR01) OVER (ORDER BY CASE WHEN TDP.SOSDP1T = 0 OR TDP.SOSDP1T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP1T ASC),'Not Specified') as [SOSDP1T-BEST-MGR],")
                .AppendLine("	")
                .AppendLine("	--DP2")
                .AppendLine("	FIRST_VALUE(TDP.SOSDP2T) OVER (ORDER BY CASE WHEN TDP.SOSDP2T = 0 OR TDP.SOSDP2T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP2T ASC) as [SOSDP2T-BEST-TIME],")
                .AppendLine("	FIRST_VALUE(STO.StoreDescription) OVER (ORDER BY CASE WHEN TDP.SOSDP2T = 0 OR TDP.SOSDP2T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP2T ASC) as [SOSDP2T-BEST-STO],")
                .AppendLine("	FIRST_VALUE(TDP.BusinessDate) OVER (ORDER BY CASE WHEN TDP.SOSDP2T = 0 OR TDP.SOSDP2T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP2T ASC) as [SOSDP2T-BEST-DATE],")
                .AppendLine("	ISNULL(FIRST_VALUE(MDP.MGR01) OVER (ORDER BY CASE WHEN TDP.SOSDP2T = 0 OR TDP.SOSDP2T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP2T ASC),'Not Specified') as [SOSDP2T-BEST-MGR],")
                .AppendLine("")
                .AppendLine("	--DP3")
                .AppendLine("	FIRST_VALUE(TDP.SOSDP3T) OVER (ORDER BY CASE WHEN TDP.SOSDP3T = 0 OR TDP.SOSDP3T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP3T ASC) as [SOSDP3T-BEST-TIME],")
                .AppendLine("	FIRST_VALUE(STO.StoreDescription) OVER (ORDER BY CASE WHEN TDP.SOSDP3T = 0 OR TDP.SOSDP3T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP3T ASC) as [SOSDP3T-BEST-STO],")
                .AppendLine("	FIRST_VALUE(TDP.BusinessDate) OVER (ORDER BY CASE WHEN TDP.SOSDP3T = 0 OR TDP.SOSDP3T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP3T ASC) as [SOSDP3T-BEST-DATE],")
                .AppendLine("	ISNULL(FIRST_VALUE(MDP.MGR01) OVER (ORDER BY CASE WHEN TDP.SOSDP3T = 0 OR TDP.SOSDP3T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP3T ASC),'Not Specified') as [SOSDP3T-BEST-MGR],")
                .AppendLine("")
                .AppendLine("	--DP4")
                .AppendLine("	FIRST_VALUE(TDP.SOSDP4T) OVER (ORDER BY CASE WHEN TDP.SOSDP4T = 0 OR TDP.SOSDP4T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP4T ASC) as [SOSDP4T-BEST-TIME],")
                .AppendLine("	FIRST_VALUE(STO.StoreDescription) OVER (ORDER BY CASE WHEN TDP.SOSDP4T = 0 OR TDP.SOSDP4T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP4T ASC) as [SOSDP4T-BEST-STO],")
                .AppendLine("	FIRST_VALUE(TDP.BusinessDate) OVER (ORDER BY CASE WHEN TDP.SOSDP4T = 0 OR TDP.SOSDP4T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP4T ASC) as [SOSDP4T-BEST-DATE],")
                .AppendLine("	ISNULL(FIRST_VALUE(MDP.MGR01) OVER (ORDER BY CASE WHEN TDP.SOSDP4T = 0 OR TDP.SOSDP4T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP4T ASC),'Not Specified') as [SOSDP4T-BEST-MGR],")
                .AppendLine("")
                .AppendLine("	--DP5")
                .AppendLine("	FIRST_VALUE(TDP.SOSDP5T) OVER (ORDER BY CASE WHEN TDP.SOSDP5T = 0 OR TDP.SOSDP5T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP5T ASC) as [SOSDP5T-BEST-TIME],")
                .AppendLine("	FIRST_VALUE(STO.StoreDescription) OVER (ORDER BY CASE WHEN TDP.SOSDP5T = 0 OR TDP.SOSDP5T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP5T ASC) as [SOSDP5T-BEST-STO],")
                .AppendLine("	FIRST_VALUE(TDP.BusinessDate) OVER (ORDER BY CASE WHEN TDP.SOSDP5T = 0 OR TDP.SOSDP5T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP5T ASC) as [SOSDP5T-BEST-DATE],")
                .AppendLine("	ISNULL(FIRST_VALUE(MDP.MGR01) OVER (ORDER BY CASE WHEN TDP.SOSDP5T = 0 OR TDP.SOSDP5T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP5T ASC),'Not Specified') as [SOSDP5T-BEST-MGR],")
                .AppendLine("")
                .AppendLine("	--DP6")
                .AppendLine("	FIRST_VALUE(TDP.SOSDP6T) OVER (ORDER BY CASE WHEN TDP.SOSDP6T = 0 OR TDP.SOSDP6T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP6T ASC) as [SOSDP6T-BEST-TIME],")
                .AppendLine("	FIRST_VALUE(STO.StoreDescription) OVER (ORDER BY CASE WHEN TDP.SOSDP6T = 0 OR TDP.SOSDP6T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP6T ASC) as [SOSDP6T-BEST-STO],")
                .AppendLine("	FIRST_VALUE(TDP.BusinessDate) OVER (ORDER BY CASE WHEN TDP.SOSDP6T = 0 OR TDP.SOSDP6T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP6T ASC) as [SOSDP6T-BEST-DATE],")
                .AppendLine("	ISNULL(FIRST_VALUE(MDP.MGR01) OVER (ORDER BY CASE WHEN TDP.SOSDP6T = 0 OR TDP.SOSDP6T IS NULL THEN 1 ELSE 0 END ASC, TDP.SOSDP6T ASC),'Not Specified') as [SOSDP6T-BEST-MGR]")

                If IsSingleStoreMode Then
                    .AppendLine("	,GOAL.[SOSDP1-GOAL]")
                    .AppendLine("	,GOAL.[SOSDP1-ATGOAL]")
                    .AppendLine("	,GOAL.[SOSDP2-GOAL]")
                    .AppendLine("	,GOAL.[SOSDP2-ATGOAL]")
                    .AppendLine("	,GOAL.[SOSDP3-GOAL]")
                    .AppendLine("	,GOAL.[SOSDP3-ATGOAL]")
                    .AppendLine("	,GOAL.[SOSDP4-GOAL]")
                    .AppendLine("	,GOAL.[SOSDP4-ATGOAL]")
                    .AppendLine("	,GOAL.[SOSDP5-GOAL]")
                    .AppendLine("	,GOAL.[SOSDP5-ATGOAL]")
                    .AppendLine("	,GOAL.[SOSDP6-GOAL]")
                    .AppendLine("	,GOAL.[SOSDP6-ATGOAL]")
                End If
                .AppendLine("   ,CAST([DPE].[1] AS BIT) [DP1EXISTS]")
                .AppendLine("   ,CAST([DPE].[2] AS BIT) [DP2EXISTS]")
                .AppendLine("   ,CAST([DPE].[3] AS BIT) [DP3EXISTS]")
                .AppendLine("   ,CAST([DPE].[4] AS BIT) [DP4EXISTS]")
                .AppendLine("   ,CAST([DPE].[5] AS BIT) [DP5EXISTS]")
                .AppendLine("   ,CAST([DPE].[6] AS BIT) [DP6EXISTS]")
                .AppendLine("   ,CAST([DPE].[7] AS BIT) [DP7EXISTS]")
                .AppendLine("   ,(DATEDIFF(DAY,@StartDate,CASE WHEN @EndDate > CAST(GETDATE() AS DATE) THEN CAST(GETDATE() AS DATE) ELSE @EndDate END)) AS [TotalDays]")
                .AppendLine("FROM")
                .AppendLine("	@StoreDateList SDL ")
                .AppendLine("	LEFT JOIN TimerDataPivoted TDP")
                .AppendLine("		ON SDL.StoreID = TDP.StoreID")
                .AppendLine("		AND SDL.BusinessDate = TDP.BusinessDate")
                .AppendLine("	LEFT JOIN MGRDataPivoted MDP")
                .AppendLine("		ON SDL.StoreID = MDP.STOREID")
                .AppendLine("		AND SDL.BusinessDate = MDP.BusinessDate")
                .AppendLine("	LEFT JOIN tmStore STO WITH(NOLOCK)")
                .AppendLine("		ON STO.StoreID = SDL.StoreID")
                If IsSingleStoreMode Then
                    .AppendLine("   CROSS JOIN SOSGoalsData GOAL WITH(NOLOCK)")
                End If
                .AppendLine("   CROSS JOIN [DayPartsExist] [DPE] WITH(NOLOCK)")
                .AppendLine("WHERE")
                .AppendLine("   SDL.BusinessDate < CAST(GETDATE() AS DATE)")
            End With

            InvWSS.GetData(sbSQL.ToString(), tabResults)
            If Not String.IsNullOrEmpty(InvWSS.ErrorMessage) Then Throw New ApplicationException(InvWSS.ErrorMessage)
            If tabResults.Rows.Count > 0 Then
                ''''''''''''''''''''''''''''''
                ' Build Actual Summary Table '
                ''''''''''''''''''''''''''''''
                Dim SumRow As DataRow = tabResults.Rows(0)

                'It's not the best solution, but the summary data is all accumulated in one row because I'm using the windowed FIRST_VALUE function
                Dim tabSummary As New DataTable()
                'Make sure you update the grid on the .aspx if you change these
                tabSummary.Columns.Add(New DataColumn("DayPart", GetType(String)))
                tabSummary.Columns.Add(New DataColumn("Best", GetType(String)))
                tabSummary.Columns.Add(New DataColumn("Goals", GetType(String)))

                For i As Integer = 1 To 6
                    If Not CBool(SumRow("DP" & i & "EXISTS")) Then Continue For
                    Dim nRow As DataRow = tabSummary.NewRow()
                    'Steal the header titles from the main report
                    With grdSOSReport.HeaderRow
                        nRow("DayPart") = CType(.FindControl("lblDP" & i), Label).Text & "<br/>" & CType(.FindControl("lblDP" & i & "Range"), Label).Text
                    End With

                    If Not IsDBNull(SumRow("SOSDP" & i & "T-BEST-TIME")) Then 'Fastest timers
                        nRow("Best") = String.Format(sTemplateBest, FormatNumber(SumRow("SOSDP" & i & "T-BEST-TIME"), ViewState("DecimalFormatting")), SumRow("SOSDP" & i & "T-BEST-STO"), CDate(SumRow("SOSDP" & i & "T-BEST-DATE")).ToShortDateString, CDate(SumRow("SOSDP" & i & "T-BEST-DATE")).DayOfWeek, SumRow("SOSDP" & i & "T-BEST-MGR"))
                    Else
                        nRow("Best") = "<span class='ErrMsg'>No timer data found</span>"
                    End If

                    If IsSingleStoreMode Then 'SOS Goals
                        If Not IsDBNull(SumRow("SOSDP" & i & "-ATGOAL")) AndAlso Not IsDBNull(SumRow("SOSDP" & i & "T-BEST-TIME")) Then
                            nRow("Goals") = String.Format(sTemplateGoals, SumRow("SOSDP" & i & "-GOAL"), SumRow("SOSDP" & i & "-ATGOAL"), SumRow("SOSDP" & i & "-ATGOAL") / SumRow("TotalDays"))
                        ElseIf IsDBNull(SumRow("SOSDP" & i & "T-BEST-TIME")) Then
                            nRow("Goals") = "<span class='ErrMsg'>(Goals Not Available)</span>"
                        Else
                            nRow("Goals") = "<span class='ErrMsg'>No goals data found</span>"
                        End If
                    End If
                    tabSummary.Rows.Add(nRow)
                Next

                grdStoreSummary.DataSource = tabSummary
                grdStoreSummary.DataBind()

                grdStoreSummary.Columns(SOSSummaryCols.Goals).Visible = IsSingleStoreMode

            Else
                Throw New ApplicationException("No Summary Data Available")
            End If

            With sbSQL
                .Clear()
                .Append(sCTEs)
                .AppendLine("--Main Query")
                .AppendLine("SELECT ")
                .AppendLine("	CASE WHEN GROUPING(STO.StoreDescription) = 1 THEN 'Total Exceptions' ELSE STO.StoreDescription END as [StoreName],")
                .AppendLine("	SUM(CASE WHEN ISNULL(TDP.SOSDP1T+TDP.SOSDP2T+TDP.SOSDP3T+TDP.SOSDP4T+TDP.SOSDP5T+TDP.SOSDP6T,0) = 0 AND ISNULL(TDP.SOSDP8T,0) <> 0 THEN 1 ELSE 0 END) AS [PartialDays],")
                .AppendLine("	SUM(CASE WHEN ISNULL(TDP.SOSDP8T,0) = 0 THEN 1 ELSE 0 END) AS [MissingDays],")
                .AppendLine("	SUM(CASE WHEN ISNULL(TDP.SOSDP1T+TDP.SOSDP2T+TDP.SOSDP3T+TDP.SOSDP4T+TDP.SOSDP5T+TDP.SOSDP6T,0) = 0 AND ISNULL(TDP.SOSDP8T,0) <> 0 THEN 1 WHEN ISNULL(TDP.SOSDP8T,0) = 0 THEN 1 ELSE 0 END) AS [TotalExceptions],")
                .AppendLine("   COUNT(DISTINCT SDL.StoreID) * (DATEDIFF(DAY,@StartDate,CASE WHEN @EndDate > CAST(GETDATE() AS DATE) THEN CAST(GETDATE() AS DATE) ELSE @EndDate END) + 1) AS [TotalDays]")
                .AppendLine("FROM")
                .AppendLine("	@StoreDateList SDL")
                .AppendLine("	LEFT JOIN TimerDataPivoted TDP")
                .AppendLine("		ON SDL.StoreID = TDP.StoreID")
                .AppendLine("		AND SDL.BusinessDate = TDP.BusinessDate")
                .AppendLine("	LEFT JOIN MGRDataPivoted MDP")
                .AppendLine("		ON SDL.StoreID = MDP.STOREID")
                .AppendLine("		AND TDP.BusinessDate = MDP.BusinessDate")
                .AppendLine("	LEFT JOIN tmStore STO WITH(NOLOCK)")
                .AppendLine("		ON STO.StoreID = SDL.StoreID")
                '.AppendLine("	LEFT JOIN tmSOSGoals GOAL WITH(NOLOCK)")
                '.AppendLine("		ON STO.StoreID = GOAL.StoreID")
                '.AppendLine("		AND (SDL.BusinessDate >= GOAL.EffectiveDate)")
                .AppendLine("WHERE")
                .AppendLine("   SDL.BusinessDate < CAST(GETDATE() AS DATE)")
                .AppendLine("GROUP BY GROUPING SETS (")
                .AppendLine("	(SDL.StoreID,STO.StoreDescription),")
                .AppendLine("	()")
                .AppendLine(")")
                .AppendLine("HAVING")
                .AppendLine("	SUM(CASE WHEN ISNULL(TDP.SOSDP1T+TDP.SOSDP2T+TDP.SOSDP3T+TDP.SOSDP4T+TDP.SOSDP5T+TDP.SOSDP6T,0) = 0 AND ISNULL(TDP.SOSDP8T,0) <> 0 THEN 1 ELSE 0 END) <> 0")
                .AppendLine("	OR SUM(CASE WHEN ISNULL(TDP.SOSDP8T,0) = 0 THEN 1 ELSE 0 END) <> 0")
            End With

            Dim tabExResults As New DataTable

            InvWSS.GetData(sbSQL.ToString(), tabExResults)
            If Not String.IsNullOrEmpty(InvWSS.ErrorMessage) Then Throw New ApplicationException(InvWSS.ErrorMessage)

            grdExceptions.DataSource = tabExResults
            grdExceptions.DataBind()

            'Don't believe visual studio, this cast is NOT redundant. I really want a UInt, not a double.
            lblRptSummaryGeneratedAt.Text = CType(DateTime.UtcNow.Subtract(New DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)).TotalMilliseconds, UInt64)

        Catch Ex As ApplicationException
            lblErrorMessage.Visible = True
            lblErrorMessage.Text = Ex.Message

            grdStoreSummary.DataSource = Nothing
            grdStoreSummary.DataBind()

            grdExceptions.DataSource = Nothing
            grdExceptions.DataBind()

        End Try
    End Sub
    Private Function BindPrimaryGrid() As Boolean

        Try
            ValidateReportOptions()
            lblErrorMessage.Visible = False
        Catch ex As Exception
            grdSOSReport.DataSource = Nothing
            grdSOSReport.DataBind()
            lblErrorMessage.Visible = True
            lblErrorMessage.Text = ex.Message
            Return False
        End Try

        Dim sTimespanGrouping As String = "SDL.BusinessDate"
        If ddlGroupByDate.SelectedValue = 1 Then sTimespanGrouping = "REP.ReportingPeriodID"

        '''''''''''''''''''''''''''''''
        '   Speed of Service Report   '
        '''''''''''''''''''''''''''''''
        Try

            Dim sbSQL As New StringBuilder
            Dim tabResults As New DataTable

            With sbSQL

                .AppendLine("DECLARE @StoreDateList AS StoreDateList")
                .AppendLine("DECLARE @StartDate DATE")
                .AppendLine("DECLARE @EndDate DATE")
                .AppendLine("DECLARE @Stores TABLE ([StoreID] INT)")
                .AppendLine("DECLARE @UniversalAccountIdentifier varchar(32)")


                .AppendLine("SET @UniversalAccountIdentifier = '" & InvWSS.Platform.Application.AccountIdentifier & "'")

                Dim tmp() As String


                'Doing this ugly voodoo saves us from having to fetch the start/end date from the reporting period tables
                Select Case ddlDateType.SelectedValue
                    Case 1
                        tmp = ddlWeek.SelectedItem.Text.Split(" ")
                    Case 2
                        tmp = ddlPeriod.SelectedItem.Text.Split(" ")
                    Case 3
                        tmp = {tbStartDate.Text, "-", tbEndDate.Text}

                End Select

                StartDate = CDate(tmp(0).Trim())
                EndDate = CDate(tmp(2).Trim())

                .AppendLine(String.Format("SET @StartDate = '{0}'", StartDate.ToShortDateString))
                .AppendLine(String.Format("SET @EndDate = '{0}'", EndDate.ToShortDateString))

                .AppendLine("DECLARE @PermittedStores TABLE")
                .AppendLine("	([StoreID] INT")
                .AppendLine("	,[StoreNum] INT")
                .AppendLine("	,[StoreDescription] VARCHAR(50)")
                .AppendLine("	,[UniversalStoreIdentifier]  VARCHAR(32)")
                .AppendLine("	,[ManagerName] VARCHAR(50)")
                .AppendLine("	,[Permitted] BIT")
                .AppendLine("	,[ViewReports] BIT")
                .AppendLine("	,[AlterData] BIT")
                .AppendLine("	)")

                .AppendLine("INSERT @PermittedStores")
                .AppendLine("	([StoreID]")
                .AppendLine("	,[StoreNum]")
                .AppendLine("	,[StoreDescription]")
                .AppendLine("	,[UniversalStoreIdentifier]")
                .AppendLine("	,[ManagerName]")
                .AppendLine("	,[Permitted]")
                .AppendLine("	,[ViewReports]")
                .AppendLine("	,[AlterData]")
                .AppendLine("	)")
                .AppendLine("EXECUTE [dbo].[WEBRPT_ps_di_GetPermittedStores] '', '', '', @UniversalAccountIdentifier;")

                .AppendLine("INSERT @Stores([StoreID])")
                .AppendLine("SELECT [StoreID]")
                .AppendLine("FROM @PermittedStores")
                .AppendLine("WHERE [StoreID] IN (" & CommaDelimitedCheckBoxList(cblStores) & ");")

                .AppendLine("WITH DateRange_CTE AS")
                .AppendLine("	(SELECT @StartDate AS [BusinessDate]")
                .AppendLine("	UNION ALL")
                .AppendLine("	SELECT DATEADD(dd, 1, [BusinessDate])")
                .AppendLine("	FROM DateRange_CTE")
                .AppendLine("	WHERE [BusinessDate] < @EndDate")
                .AppendLine("	)")
                .AppendLine("INSERT @StoreDateList")
                .AppendLine("	([StoreID]")
                .AppendLine("	,[BusinessDate]")
                .AppendLine("	)")
                .AppendLine("SELECT")
                .AppendLine("	 [StoreID]")
                .AppendLine("	,[BusinessDate]")
                .AppendLine("FROM DateRange_CTE")
                .AppendLine("	CROSS JOIN @Stores")
                .AppendLine("ORDER BY [BusinessDate];")


                .AppendLine("WITH TimerData AS")
                .AppendLine("( -- Basic timer data")
                .AppendLine("	SELECT")
                .AppendLine("		 StoreID")
                .AppendLine("		,BusinessDate")
                .AppendLine("		,Tag")
                .AppendLine("		,Value")
                .AppendLine("	FROM")
                .AppendLine("		[pf_GetDTTimerData](@StoreDateList)")
                .AppendLine("),")
                .AppendLine("DestinationTransCounts AS ")
                .AppendLine("(")
                .AppendLine("	SELECT")
                .AppendLine("		 S.StoreID")
                .AppendLine("		,BusinessDate")
                .AppendLine("		,tmTR.[DayPartID]")
                .AppendLine("		,CONCAT('SOSDP',tmDP.[Sequence],'Trans') as [TAG]")
                .AppendLine("		,SUM([COUNT]) AS [TransCount]")
                .AppendLine("	FROM [dbo].[ttsDestination] AS [tsD] WITH(NOLOCK)")
                .AppendLine("       INNER JOIN @Stores S ")
                .AppendLine("           ON S.StoreID = tsD.StoreID")
                .AppendLine("		INNER JOIN [dbo].[ttmTimeRange] AS [tmTR] WITH(NOLOCK)")
                .AppendLine("			ON tmTR.[StartTime] - CAST(FLOOR(CAST(tmTR.[StartTime] AS Float)) AS DateTime) <= tsD.[TIME_START] - tsD.[BUSINESSDATE]")
                .AppendLine("				AND DATEADD(mi, 1, tmTR.[EndTime]) - CAST(FLOOR(CAST(tmTR.[EndTime] AS Float)) AS DATETIME) >= tsD.[TIME_END] - tsD.[BUSINESSDATE]")
                .AppendLine("		INNER JOIN [dbo].[ttmDayPart] AS [tmDP] WITH(NOLOCK)")
                .AppendLine("			ON tmTR.[DayPartID] = tmDP.[DayPartID]")
                .AppendLine("	WHERE ")
                .AppendLine("		DEST_NUM in ('3','4','7','12','13','16','18','42')")
                .AppendLine("		AND BUSINESSDATE BETWEEN @StartDate AND @EndDate")
                .AppendLine("	GROUP BY")
                .AppendLine("		 S.StoreID")
                .AppendLine("		,BusinessDate")
                .AppendLine("		,tmTR.[DayPartID]")
                .AppendLine("		,tmDP.[Sequence]")
                .AppendLine("),")

                .AppendLine("TimerDataPivoted AS")
                .AppendLine("( -- Pivot timer data")
                .AppendLine("	SELECT ")
                .AppendLine("		 PV.SOSDP1C")
                .AppendLine("		,PV.SOSDP1T")
                .AppendLine("		,PV.SOSDP2C")
                .AppendLine("		,PV.SOSDP2T")
                .AppendLine("		,PV.SOSDP3C")
                .AppendLine("		,PV.SOSDP3T")
                .AppendLine("		,PV.SOSDP4C")
                .AppendLine("		,PV.SOSDP4T")
                .AppendLine("		,PV.SOSDP5C")
                .AppendLine("		,PV.SOSDP5T")
                .AppendLine("		,PV.SOSDP6C")
                .AppendLine("		,PV.SOSDP6T")
                .AppendLine("		,PV.SOSDP8C")
                .AppendLine("		,PV.SOSDP8T")
                .AppendLine("		,PV.StoreID")
                .AppendLine("		,PV.BusinessDate")

                If chkCompareTransactions.Checked Then
                    .AppendLine("		,PV.SOSDP1Trans")
                    .AppendLine("		,PV.SOSDP2Trans")
                    .AppendLine("		,PV.SOSDP3Trans")
                    .AppendLine("		,PV.SOSDP4Trans")
                    .AppendLine("		,PV.SOSDP5Trans")
                    .AppendLine("		,PV.SOSDP6Trans")
                    .AppendLine("		,PV.SOSDP8Trans")
                End If

                .AppendLine("	FROM")
                .AppendLine("		(")
                .AppendLine("			(")
                .AppendLine("				SELECT")
                .AppendLine("					 StoreID")
                .AppendLine("					,BusinessDate")
                .AppendLine("					,Tag")
                .AppendLine("					,Value")
                .AppendLine("				FROM")
                .AppendLine("					TimerData")
                .AppendLine("			)")
                If chkCompareTransactions.Checked Then
                    .AppendLine("			UNION")
                    .AppendLine("			(")
                    .AppendLine("				SELECT ")
                    .AppendLine("					 StoreID")
                    .AppendLine("					,BusinessDate")
                    .AppendLine("					,TAG")
                    .AppendLine("					,TransCount")
                    .AppendLine("				FROM DestinationTransCounts")
                    .AppendLine("			)")
                    .AppendLine("			UNION")
                    .AppendLine("			(")
                    .AppendLine("				SELECT ")
                    .AppendLine("					 StoreID")
                    .AppendLine("					,BusinessDate")
                    .AppendLine("					,'SOSDP8Trans' AS [Tag]")
                    .AppendLine("					,SUM(TransCount) as [Value]")
                    .AppendLine("				FROM DestinationTransCounts")
                    .AppendLine("				GROUP BY")
                    .AppendLine("					 StoreID")
                    .AppendLine("					,BusinessDate")
                    .AppendLine("			)")
                End If

                .AppendLine("		) AS DATA")
                .AppendLine("	PIVOT")
                .AppendLine("	(")
                .AppendLine("		SUM(DATA.Value)")
                .AppendLine("		FOR DATA.Tag IN ([SOSDP1C],[SOSDP1T],[SOSDP1Trans]")
                .AppendLine("						,[SOSDP2C],[SOSDP2T],[SOSDP2Trans]")
                .AppendLine("						,[SOSDP3C],[SOSDP3T],[SOSDP3Trans]")
                .AppendLine("						,[SOSDP4C],[SOSDP4T],[SOSDP4Trans]")
                .AppendLine("						,[SOSDP5C],[SOSDP5T],[SOSDP5Trans]")
                .AppendLine("						,[SOSDP6C],[SOSDP6T],[SOSDP6Trans]")
                .AppendLine("						,[SOSDP8C],[SOSDP8T],[SOSDP8Trans])")
                .AppendLine("	) PV")

                .AppendLine("),")
                .AppendLine("TimerDataTransCounts AS ")
                .AppendLine("( -- Number of businessdates with non-0 values")
                .AppendLine("	SELECT")
                .AppendLine("		Tag,")
                .AppendLine("		BusinessDate,")
                .AppendLine("		StoreID,")
                .AppendLine("		COUNT(Value) as [Count]")
                .AppendLine("	FROM")
                .AppendLine("		TimerData TD")
                .AppendLine("	WHERE")
                .AppendLine("		Value <> 0")
                .AppendLine("	GROUP BY")
                .AppendLine("		Tag,")
                .AppendLine("		BusinessDate,")
                .AppendLine("		StoreID")
                .AppendLine("),")
                .AppendLine("TimerDataTransCountsPivoted AS ")
                .AppendLine("( --Pivot businessday count, for use in the final query")
                .AppendLine("	SELECT ")
                .AppendLine("		 PV.SOSDP1T")
                .AppendLine("		,PV.SOSDP2T")
                .AppendLine("		,PV.SOSDP3T")
                .AppendLine("		,PV.SOSDP4T")
                .AppendLine("		,PV.SOSDP5T")
                .AppendLine("		,PV.SOSDP6T")
                .AppendLine("		,PV.SOSDP8T")
                .AppendLine("		,PV.StoreID")
                .AppendLine("		,PV.BusinessDate")
                .AppendLine("	FROM")
                .AppendLine("		(")
                .AppendLine("			SELECT")
                .AppendLine("				 StoreID")
                .AppendLine("				,BusinessDate")
                .AppendLine("				,Tag")
                .AppendLine("				,Count")
                .AppendLine("			FROM")
                .AppendLine("				TimerDataTransCounts")
                .AppendLine("		) AS DATA")
                .AppendLine("	PIVOT")
                .AppendLine("	(")
                .AppendLine("		SUM(DATA.Count)")
                .AppendLine("		FOR DATA.Tag IN ([SOSDP1T],[SOSDP2T],[SOSDP3T],[SOSDP4T],[SOSDP5T],[SOSDP6T],[SOSDP8T])")
                .AppendLine("	) PV")
                .AppendLine("),")
                .AppendLine("STAFData AS")
                .AppendLine("( -- MGR Data (Not used when grouping)")
                .AppendLine("	SELECT")
                .AppendLine("		 Tag")
                .AppendLine("		,TextValue")
                .AppendLine("		,BusinessDate")
                .AppendLine("		,STOREID")
                .AppendLine("		,ROW_NUMBER() OVER(PARTITION BY BusinessDate,Tag,StoreID ORDER BY BusinessDate DESC) [Ordinal]")
                .AppendLine("	FROM vjStoreAndForwardDetail STAFD WITH(NOLOCK)")
                .AppendLine("	WHERE")
                .AppendLine("		TAG LIKE 'MGR__'")
                .AppendLine("),")
                .AppendLine("MGRDataPivoted AS")
                .AppendLine("( -- Pivoted, to join to main query")
                .AppendLine("	SELECT")
                .AppendLine("		 PV.MGR01")
                .AppendLine("		,PV.MGR02")
                .AppendLine("		,PV.MGR03")
                .AppendLine("		,PV.MGR04")
                .AppendLine("		,PV.MGR05")
                .AppendLine("		,PV.MGR06")
                .AppendLine("		,PV.STOREID")
                .AppendLine("		,PV.BusinessDate")
                .AppendLine("	FROM ")
                .AppendLine("		(")
                .AppendLine("			SELECT")
                .AppendLine("				 Tag")
                .AppendLine("				,TextValue")
                .AppendLine("				,BusinessDate")
                .AppendLine("				,STOREID")
                .AppendLine("			FROM STAFData")
                .AppendLine("			WHERE Ordinal = 1")
                .AppendLine("		) DAT")
                .AppendLine("		PIVOT")
                .AppendLine("		(")
                .AppendLine("			MAX(TextValue)")
                .AppendLine("			FOR Tag IN ([MGR01],[MGR02],[MGR03],[MGR04],[MGR05],[MGR06])")
                .AppendLine("		) PV")
                .AppendLine(")")
                .AppendLine("--Main Query")
                .AppendLine("SELECT")
                .AppendLine("	 CASE ")

                If IsSummaryMode Then
                    'Summary mode is special, and needs different grouping rows - it's the only one that doesn't group by store
                    If CanGroupByDistrict Then .AppendLine("		WHEN GROUPING(STO.DistrictNum) = 0 THEN 3")
                    If CanGroupByRegion Then .AppendLine("		    WHEN GROUPING(STO.RegionNum) = 0 THEN 4")
                    If CanGroupByTimeSpan Then .AppendLine("		WHEN GROUPING(" & sTimespanGrouping & ") = 0 THEN 5")
                    If CanGroupByTimeSpan Then .AppendLine("		WHEN GROUPING(" & sTimespanGrouping & ") = 1 THEN 6")
                Else
                    If CanGroupByStore Then .AppendLine("		    WHEN GROUPING(SDL.StoreID) = 0 AND GROUPING(" & sTimespanGrouping & ") = 0 THEN 1 ")
                    If CanGroupByStore Then .AppendLine("		    WHEN GROUPING(SDL.StoreID) = 0 THEN 2 ")
                    If CanGroupByDistrict Then .AppendLine("		WHEN GROUPING(STO.DistrictNum) = 0 THEN 3")
                    If CanGroupByRegion Then .AppendLine("		    WHEN GROUPING(STO.RegionNum) = 0 THEN 4")
                    If CanGroupByTimeSpan Then .AppendLine("		WHEN GROUPING(" & sTimespanGrouping & ") = 0 THEN 5")
                    .AppendLine("		WHEN GROUPING(" & sTimespanGrouping & ") = 1 THEN 6")
                End If

                .AppendLine("        WHEN 1=1 THEN 0") 'Just in case somehow all of the above is false.
                .AppendLine("	 END AS [RowType]")

                .AppendLine("	,MAX(SDL.BusinessDate) As [BusinessDate]")
                .AppendLine("	,MAX(CAST(INF.DTTimerReady AS INT)) As [DTTimerReady]")

                If ddlGroupByDate.SelectedValue = 1 Then
                    .AppendLine("	,REP.ReportingPeriodID As [RepID]")
                Else
                    .AppendLine("    ,NULL As [RepID]")
                End If

                If CanGroupByStore Then
                    .AppendLine("	--Store Info")
                    .AppendLine("	,SDL.StoreID")
                    .AppendLine("	,STO.StoreNum")
                    .AppendLine("	,STO.StoreDescription")
                End If

                If CanGroupByRegion Then
                    .AppendLine("	,STO.RegionNum")
                    .AppendLine("	,REG.Region")
                End If

                If CanGroupByDistrict Then
                    .AppendLine("	,STO.DistrictNum")
                    .AppendLine("	,DIS.District")
                End If
                .AppendLine("	--SOS Counts/Times")
                .AppendLine("	,SUM(ISNULL(TDP.SOSDP1C,0)) AS SOSDP1C")
                .AppendLine("	,CASE WHEN ISNULL(SUM(TDTCP.SOSDP1T),0) = 0 THEN 0 ELSE SUM(ISNULL(TDP.SOSDP1T,0)) / SUM(TDTCP.SOSDP1T) END AS SOSDP1T")
                .AppendLine("")
                .AppendLine("	,SUM(ISNULL(TDP.SOSDP2C,0)) AS SOSDP2C")
                .AppendLine("	,CASE WHEN ISNULL(SUM(TDTCP.SOSDP2T),0) = 0 THEN 0 ELSE SUM(ISNULL(TDP.SOSDP2T,0)) / SUM(TDTCP.SOSDP2T) END AS SOSDP2T")
                .AppendLine("")
                .AppendLine("	,SUM(ISNULL(TDP.SOSDP3C,0)) AS SOSDP3C")
                .AppendLine("	,CASE WHEN ISNULL(SUM(TDTCP.SOSDP3T),0) = 0 THEN 0 ELSE SUM(ISNULL(TDP.SOSDP3T,0)) / SUM(TDTCP.SOSDP3T) END AS SOSDP3T")
                .AppendLine("")
                .AppendLine("	,SUM(ISNULL(TDP.SOSDP4C,0)) AS SOSDP4C")
                .AppendLine("	,CASE WHEN ISNULL(SUM(TDTCP.SOSDP4T),0) = 0 THEN 0 ELSE SUM(ISNULL(TDP.SOSDP4T,0)) / SUM(TDTCP.SOSDP4T) END AS SOSDP4T")
                .AppendLine("")
                .AppendLine("	,SUM(ISNULL(TDP.SOSDP5C,0)) AS SOSDP5C")
                .AppendLine("	,CASE WHEN ISNULL(SUM(TDTCP.SOSDP5T),0) = 0 THEN 0 ELSE SUM(ISNULL(TDP.SOSDP5T,0)) / SUM(TDTCP.SOSDP5T) END AS SOSDP5T")
                .AppendLine("")
                .AppendLine("	,SUM(ISNULL(TDP.SOSDP6C,0)) AS SOSDP6C")
                .AppendLine("	,CASE WHEN ISNULL(SUM(TDTCP.SOSDP6T),0) = 0 THEN 0 ELSE SUM(ISNULL(TDP.SOSDP6T,0)) / SUM(TDTCP.SOSDP6T) END AS SOSDP6T")
                .AppendLine("")
                .AppendLine("	,SUM(ISNULL(TDP.SOSDP8C,0)) AS SOSDP8C")
                .AppendLine("	,CASE WHEN ISNULL(SUM(TDTCP.SOSDP8T),0) = 0 THEN 0 ELSE SUM(ISNULL(TDP.SOSDP8T,0)) / SUM(TDTCP.SOSDP8T) END AS SOSDP8T")
                .AppendLine("")
                .AppendLine("	--MGR Info")
                .AppendLine("	,ISNULL(MAX(MDP.MGR01),'Not Specified') AS MGR1")
                .AppendLine("	,ISNULL(MAX(MDP.MGR02),'Not Specified') AS MGR2")
                .AppendLine("	,ISNULL(MAX(MDP.MGR03),'Not Specified') AS MGR3")
                .AppendLine("	,ISNULL(MAX(MDP.MGR04),'Not Specified') AS MGR4")
                .AppendLine("	,ISNULL(MAX(MDP.MGR05),'Not Specified') AS MGR5")
                .AppendLine("	,ISNULL(MAX(MDP.MGR06),'Not Specified') AS MGR6")
                .AppendLine("")
                .AppendLine("	--SOS Goals")
                .AppendLine("	--,GOAL.SOSGoal1")
                .AppendLine("	--,GOAL.SOSGoal2")
                .AppendLine("	--,GOAL.SOSGoal3")
                .AppendLine("	--,GOAL.SOSGoal4")
                .AppendLine("	--,GOAL.SOSGoal5")
                .AppendLine("	--,GOAL.SOSGoal6")
                If chkCompareTransactions.Checked Then
                    .AppendLine("	,SUM(ISNULL(TDP.SOSDP1Trans,0)) SOSDP1Trans")
                    .AppendLine("	,SUM(ISNULL(TDP.SOSDP2Trans,0)) SOSDP2Trans")
                    .AppendLine("	,SUM(ISNULL(TDP.SOSDP3Trans,0)) SOSDP3Trans")
                    .AppendLine("	,SUM(ISNULL(TDP.SOSDP4Trans,0)) SOSDP4Trans")
                    .AppendLine("	,SUM(ISNULL(TDP.SOSDP5Trans,0)) SOSDP5Trans")
                    .AppendLine("	,SUM(ISNULL(TDP.SOSDP6Trans,0)) SOSDP6Trans")
                    .AppendLine("	,SUM(ISNULL(TDP.SOSDP8Trans,0)) SOSDP8Trans")
                End If


                .AppendLine("FROM")
                .AppendLine("	@StoreDateList SDL ")
                .AppendLine("	LEFT JOIN TimerDataPivoted TDP")
                .AppendLine("		ON SDL.StoreID = TDP.StoreID")
                .AppendLine("		AND SDL.BusinessDate = TDP.BusinessDate")
                .AppendLine("	LEFT JOIN TimerDataTransCountsPivoted TDTCP")
                .AppendLine("		ON SDL.StoreID = TDTCP.StoreID")
                .AppendLine("		AND SDL.BusinessDate = TDTCP.BusinessDate")
                .AppendLine("	LEFT JOIN MGRDataPivoted MDP")
                .AppendLine("		ON SDL.StoreID = MDP.STOREID")
                .AppendLine("		AND SDL.BusinessDate = MDP.BusinessDate")
                .AppendLine("	INNER JOIN tmStore STO WITH(NOLOCK)")
                .AppendLine("		ON STO.StoreID = SDL.StoreID")
                .AppendLine("   INNER JOIN [tmStoreInvt] [INF] WITH(NOLOCK)")
                .AppendLine("       ON STO.StoreID = INF.StoreID")
                .AppendLine("	INNER JOIN tmRegion REG WITH(NOLOCK)")
                .AppendLine("		ON STO.RegionNum = REG.RegionNum ")
                .AppendLine("	INNER JOIN tmDistrict DIS WITH(NOLOCK)")
                .AppendLine("		ON STO.DistrictNum = DIS.DistrictNum")
                .AppendLine("	INNER JOIN tmReportingPeriod REP WITH(NOLOCK)")
                .AppendLine("		ON SDL.BusinessDate BETWEEN REP.ReportingStartDate AND REP.ReportingEndDate")

                .AppendLine("WHERE")
                .AppendLine("   SDL.BusinessDate < GETDATE()")

                .AppendLine("GROUP BY GROUPING SETS(")


                Dim sGroupByStoreDay As String = String.Format("    (SDL.StoreID,STO.StoreNum,STO.RegionNum,REG.Region,STO.DistrictNum,DIS.District,{0},STO.StoreDescription),", sTimespanGrouping)
                Dim sGroupByDistrictDay As String = String.Format("    (STO.RegionNum,REG.Region,STO.DistrictNum,DIS.District,{0}),", sTimespanGrouping)
                Dim sGroupByRegionDay As String = String.Format("    (STO.RegionNum,REG.Region,{0}),", sTimespanGrouping)

                Dim sGroupByStore As String = String.Format("    (SDL.StoreID,STO.StoreNum,STO.RegionNum,REG.Region,STO.DistrictNum,DIS.District,STO.StoreDescription),")
                Dim sGroupByDistrictStore As String = "    (STO.RegionNum,REG.Region,STO.DistrictNum,DIS.District),"
                Dim sGroupByRegionStore As String = "    (STO.RegionNum,REG.Region),"

                Dim sGroupByTimespan As String = String.Format("    ({0}),", sTimespanGrouping)
                Dim sGroupByGrandTotal As String = "    ()"


                If IsSingleStoreMode Then
                    .AppendLine(sGroupByStoreDay)
                    .AppendLine(sGroupByGrandTotal)
                    .AppendLine(")")
                    .AppendLine("ORDER BY")
                    .AppendLine("	GROUPING(" & sTimespanGrouping & "),")
                    .AppendLine("	" & sTimespanGrouping & ",")
                    If CanGroupByRegion Then .AppendLine("	GROUPING(STO.RegionNum),")
                    If CanGroupByRegion Then .AppendLine("	STO.RegionNum,")
                    If CanGroupByDistrict Then .AppendLine("	Grouping(STO.DistrictNum),")
                    If CanGroupByDistrict Then .AppendLine("	STO.DistrictNum,")
                    .AppendLine("	Grouping(SDL.StoreID),")
                    .AppendLine("	STO.StoreNum")
                ElseIf radioDay.Checked OrElse IsSummaryMode Then

                    If Not IsSummaryMode Then .AppendLine(sGroupByStoreDay)
                    If CanGroupByDistrict Then .AppendLine(sGroupByDistrictDay)
                    If CanGroupByRegion Then .AppendLine(sGroupByRegionDay)
                    If CanGroupByTimeSpan Then .AppendLine(sGroupByTimespan)
                    .AppendLine(sGroupByGrandTotal)
                    .AppendLine(")")
                    .AppendLine("ORDER BY")
                    .AppendLine("	GROUPING(" & sTimespanGrouping & "),")
                    .AppendLine("	" & sTimespanGrouping & ",")
                    If CanGroupByRegion Then .AppendLine("	GROUPING(STO.RegionNum),")
                    If CanGroupByRegion Then .AppendLine("	STO.RegionNum,")
                    If CanGroupByDistrict Then .AppendLine("	Grouping(STO.DistrictNum),")
                    If CanGroupByDistrict Then .AppendLine("	STO.DistrictNum,")
                    If Not IsSummaryMode Then .AppendLine("	Grouping(SDL.StoreID),")
                    If Not IsSummaryMode Then .AppendLine("	STO.StoreNum,")
                    .AppendLine(" 1")

                Else
                    .AppendLine(sGroupByStoreDay)
                    If Not (ddlDateType.SelectedValue = 1 AndAlso ddlGroupByDate.SelectedValue = 1) Then .AppendLine(sGroupByStore)
                    If CanGroupByDistrict Then .AppendLine(sGroupByDistrictStore)
                    If CanGroupByRegion Then .AppendLine(sGroupByRegionStore)
                    If CanGroupByTimeSpan Then .AppendLine(sGroupByTimespan)
                    .AppendLine(sGroupByGrandTotal)
                    .AppendLine(")")
                    .AppendLine("ORDER BY")
                    If CanGroupByRegion Then .AppendLine("	GROUPING(STO.RegionNum),")
                    If CanGroupByRegion Then .AppendLine("	STO.RegionNum,")
                    If CanGroupByDistrict Then .AppendLine("	Grouping(STO.DistrictNum),")
                    If CanGroupByDistrict Then .AppendLine("	STO.DistrictNum,")
                    .AppendLine("	Grouping(SDL.StoreID),")
                    .AppendLine("	STO.StoreNum,")
                    .AppendLine("	GROUPING(" & sTimespanGrouping & "),")
                    .AppendLine("	" & sTimespanGrouping & "")

                End If
            End With

            InvWSS.GetData(sbSQL.ToString(), tabResults)
            If Not String.IsNullOrEmpty(InvWSS.ErrorMessage) Then Throw New ApplicationException(InvWSS.ErrorMessage)

            If tabResults.Rows.Count > 0 Then

                grdSOSReport.DataSource = tabResults
                grdSOSReport.DataBind()

                For I As Integer = SOSReportCols.DP1 To SOSReportCols.DP6
                    grdSOSReport.Columns(I).Visible = False
                Next

                'Get the headers from the configured dayparts dictionary
                With grdSOSReport.HeaderRow
                    For Each Key As Integer In dictDayParts.Keys

                        With grdSOSReport.HeaderRow
                            CType(.FindControl("lblDP" & Key), Label).Text = dictDayParts(Key).Item1
                            CType(.FindControl("lblDP" & Key & "Range"), Label).Text = dictDayParts(Key).Item2
                        End With

                        grdSOSReport.Columns(SOSReportCols.DP1 + Key - 1).Visible = True

                    Next
                End With

                Return True

            Else
                Throw New ApplicationException("No DTTimer data found for the selected store(s)")
            End If
        Catch Ex As ApplicationException
            lblErrorMessage.Visible = True
            lblErrorMessage.Text = Ex.Message

            grdSOSReport.DataSource = Nothing
            grdSOSReport.DataBind()

            grdStoreSummary.DataSource = Nothing
            grdStoreSummary.DataBind()

            Return False
        End Try
    End Function

    Private Sub FetchDayPartHeaders()
        If ViewState("dictDayParts") IsNot Nothing Then
            dictDayParts = ViewState("dictDayParts")
        Else
            dictDayParts = New Dictionary(Of Integer, Tuple(Of String, String))()
            Dim sbSQL As New StringBuilder
            Dim tabResults As New DataTable

            With sbSQL

                .AppendLine("SELECT")
                .AppendLine("	 CONCAT(FORMAT([START].[StartTime],'t'), ' - ', FORMAT([END].[EndTime],'t')) AS [TimeRange]")
                .AppendLine("	,REPLACE(REPLACE(TimerTag,'SOSDP',''),'C','') as [Sequence]")
                .AppendLine("	,[Name]")
                .AppendLine("FROM")
                .AppendLine("	(")
                .AppendLine("	SELECT")
                .AppendLine("		 MIN([tmTR].[Sequence]) AS STARTSEQ")
                .AppendLine("		,MAX([tmTR].[Sequence]) AS ENDSEQ")
                .AppendLine("		,[Tags].[TimerTag]")
                .AppendLine("		,MAX([tmDP].[Name]) AS [Name]")
                .AppendLine("	FROM [dbo].[pf_GetDTTimerTag]() AS [Tags]")
                .AppendLine("		LEFT OUTER JOIN [dbo].[ttmDayPart] AS [tmDP]")
                .AppendLine("			ON tmDP.[Tag] = Tags.[DayPartTag]")
                .AppendLine("		INNER JOIN [dbo].[ttmTimeRange] AS [tmTR]")
                .AppendLine("			ON tmDP.[DayPartID] = tmTR.[DayPartID]")
                .AppendLine("	WHERE Tags.[Type] = 0")
                .AppendLine("		AND Tags.[TimerTag] <> 'SOSDP8C'")
                .AppendLine("   GROUP BY [TimerTag]")
                .AppendLine("	) AS [Data]")
                .AppendLine("	INNER JOIN tmTimeRange START WITH(NOLOCK)")
                .AppendLine("		ON START.[Sequence] = Data.STARTSEQ")
                .AppendLine("	INNER JOIN tmTimeRange [END] WITH(NOLOCK)")
                .AppendLine("		ON [END].[Sequence] = Data.ENDSEQ")

            End With

            InvWSS.GetData(sbSQL.ToString(), tabResults)
            If Not String.IsNullOrEmpty(InvWSS.ErrorMessage) Then Throw New ApplicationException(InvWSS.ErrorMessage)

            If tabResults.Rows.Count > 0 Then

                For Each Row As DataRow In tabResults.Rows
                    dictDayParts.Add(Row("Sequence"), New Tuple(Of String, String)(Row("Name"), Row("TimeRange")))
                Next

                ViewState("dictDayParts") = dictDayParts
            End If
        End If

    End Sub

    Protected Function MinDate(A As Date, B As Date) As Date
        If A < B Then Return A Else Return B
    End Function
    Protected Function MaxDate(A As Date, B As Date) As Date
        If A > B Then Return A Else Return B
    End Function

    Protected Sub grdSOSReport_RowDataBound(sender As Object, e As GridViewRowEventArgs)
        Select Case e.Row.RowType
            Case DataControlRowType.DataRow

                'Row Title
                Select Case e.Row.DataItem("RowType")
                    Case 1
                        If Not chkIncludeDetails.Checked Then e.Row.Visible = False
                        If e.Row.DataItem("DTTimerReady") = 0 Then
                            CType(e.Row.FindControl("lblRowName"), Label).Text = e.Row.DataItem("StoreDescription") & "*"
                            CType(e.Row.FindControl("lblRowName"), Label).ForeColor = System.Drawing.Color.Red
                        Else
                            CType(e.Row.FindControl("lblRowName"), Label).Text = e.Row.DataItem("StoreDescription")

                        End If
                    Case 2
                        If e.Row.DataItem("DTTimerReady") = 0 Then
                            CType(e.Row.FindControl("lblRowName"), Label).Text = e.Row.DataItem("StoreDescription") & " (Total)*"
                            CType(e.Row.FindControl("lblRowName"), Label).ForeColor = System.Drawing.Color.Red
                        Else
                            CType(e.Row.FindControl("lblRowName"), Label).Text = e.Row.DataItem("StoreDescription") & " (Total)"
                        End If
                    Case 3
                        CType(e.Row.FindControl("lblRowName"), Label).Text = e.Row.DataItem("District")
                    Case 4
                        CType(e.Row.FindControl("lblRowName"), Label).Text = e.Row.DataItem("Region")
                    Case 5
                        CType(e.Row.FindControl("lblRowName"), Label).Text = IIf(ddlGroupByDate.SelectedValue = 1, "Weekly Total", "Daily Total")
                    Case 6
                        CType(e.Row.FindControl("lblRowName"), Label).Text = IIf(ddlDateType.SelectedValue = 2, "Period Total", "Weekly Total")
                End Select

                If IsDBNull(e.Row.DataItem("RepID")) Then
                    'Reporting Period ID Not Found

                    If e.Row.DataItem("RowType") <> 6 Then
                        CType(e.Row.FindControl("lblBusinessDate"), Label).Text = e.Row.DataItem("BusinessDate") & "<br/>" & CDate(e.Row.DataItem("BusinessDate")).DayOfWeek.ToString()

                    End If

                Else
                        'Reporting Period ID Found
                        Dim repPerId As Integer = e.Row.DataItem("RepID")
                    If dictRepPeriods.ContainsKey(repPerId) Then
                        CType(e.Row.FindControl("lblBusinessDate"), Label).Text = dictRepPeriods.Item(repPerId)
                    Else
                        Dim Yr As Integer
                        Dim Per As Integer
                        Dim Week As Integer
                        Dim StartDt As Date
                        Dim EndDt As Date
                        InvWSS.GetReportingPeriod(e.Row.DataItem("RepID"), Yr, Per, Week, StartDt, EndDt)
                        dictRepPeriods.Add(repPerId, MaxDate(StartDt.ToShortDateString, StartDate) & " - " & MinDate(EndDt.ToShortDateString, EndDate))
                        CType(e.Row.FindControl("lblBusinessDate"), Label).Text = dictRepPeriods.Item(repPerId)
                    End If
                End If

                'Row Datestamp
                If e.Row.DataItem("RowType") <> 1 Then
                    e.Row.CssClass &= "Grouping GroupFooter Level-" & (5 - e.Row.DataItem("RowType") + If(radioDay.Checked OrElse IsSummaryMode, 1, 0)).ToString
                End If


                For i As Integer = 1 To 8
                    If i = 7 Then Continue For 'No dp7

                    CType(e.Row.FindControl("lblDP" & i & "C"), Label).Text = e.Row.DataItem("SOSDP" & i & "C")

                    If Not chkCompareTransactions.Checked Then
                        CType(e.Row.FindControl("lblDP" & i & "T"), Label).Text = FormatNumber(e.Row.DataItem("SOSDP" & i & "T"), ViewState("DecimalFormatting"))

                        If dictSOSAvgColors.ContainsKey(i - 1) AndAlso chkEnableColors.Checked Then
                            'Abracadabra
                            CType(e.Row.FindControl("indicatorDP" & i), HtmlGenericControl).Attributes.Add("style", String.Format("background-color:{0}", dictSOSAvgColors(i - 1).Invoke(e.Row.DataItem("SOSDP" & i & "T"))))
                        ElseIf dictSOSAvgColors.ContainsKey(i - 2) AndAlso chkEnableColors.Checked AndAlso i = 8 Then
                            'Abracadabra
                            CType(e.Row.FindControl("indicatorDP" & i), HtmlGenericControl).Attributes.Add("style", String.Format("background-color:{0}", dictSOSAvgColors(i - 2).Invoke(e.Row.DataItem("SOSDP" & i & "T"))))
                        Else
                            'Can get here if colors are disabled, or if they're just not configured.
                            CType(e.Row.FindControl("indicatorDP" & i), HtmlGenericControl).Attributes.Add("style", "display:none;")
                        End If

                    Else ' Compare To Transactions
                        CType(e.Row.FindControl("lblDP" & i & "Label2"), Label).Text = "Trans Vs Cars:"
                        CType(e.Row.FindControl("lblDP" & i & "T"), Label).Text = FormatNumber(e.Row.DataItem("SOSDP" & i & "Trans") - e.Row.DataItem("SOSDP" & i & "C"), ViewState("DecimalFormatting"))

                        'Set default color to the missing data color
                        CType(e.Row.FindControl("indicatorDP" & i), HtmlGenericControl).Attributes.Add("style", String.Format("background-color:{0}", "#004eff;"))
                        If chkEnableColors.Checked Then
                            For Each i1 As Integer In dictSOSTransVsCars.Keys
                                'Missing data check
                                If IsDBNull(e.Row.DataItem("SOSDP" & i & "Trans")) OrElse IsDBNull(e.Row.DataItem("SOSDP" & i & "C")) OrElse e.Row.DataItem("SOSDP" & i & "Trans") = 0 OrElse e.Row.DataItem("SOSDP" & i & "C") = 0 Then
                                    CType(e.Row.FindControl("indicatorDP" & i), HtmlGenericControl).Attributes.Add("style", String.Format("background-color:{0}", "#004eff"))
                                    Exit For
                                ElseIf dictSOSTransVsCars(i1).Item1.Invoke(e.Row.DataItem("SOSDP" & i & "Trans") - e.Row.DataItem("SOSDP" & i & "C")) Then
                                    'if Abracadabra Then color
                                    CType(e.Row.FindControl("indicatorDP" & i), HtmlGenericControl).Attributes.Add("style", String.Format("background-color:{0}", dictSOSTransVsCars(i1).Item2))
                                    Exit For
                                End If
                            Next
                        Else
                            'Don't enable the indicators (hide them)
                            CType(e.Row.FindControl("indicatorDP" & i), HtmlGenericControl).Attributes.Add("style", "display:none;")
                        End If
                    End If

                    If i = 8 Then Continue For 'No mgr8
                    If e.Row.DataItem("RowType") = 1 AndAlso Not ddlGroupByDate.SelectedValue = 1 Then
                        CType(e.Row.FindControl("MOD" & i), HtmlGenericControl).Visible = True
                        CType(e.Row.FindControl("MGR" & i), Label).Text = e.Row.DataItem("MGR" & i)
                    Else
                        CType(e.Row.FindControl("MOD" & i), HtmlGenericControl).Visible = False
                    End If
                Next i
        End Select
    End Sub
    Protected Sub grdStoreSummary_RowDataBound(sender As Object, e As GridViewRowEventArgs)
        If e.Row.RowType = DataControlRowType.DataRow Then
            'The regular databound escpaes HTML.. this un-escapes HTML.
            e.Row.Cells(SOSSummaryCols.RowName).Text = e.Row.DataItem("DayPart")
            e.Row.Cells(SOSSummaryCols.Best).Text = e.Row.DataItem("Best")
            If IsSingleStoreMode Then e.Row.Cells(SOSSummaryCols.Goals).Text = e.Row.DataItem("Goals")
        End If
    End Sub
    Protected Sub grdExceptions_RowDataBound(sender As Object, e As GridViewRowEventArgs)
        If e.Row.RowType = DataControlRowType.DataRow Then
            With (e.Row)
                .Cells(SOSExceptionCols.PartialDays).Text = String.Format(sTemplateExceptionsGrid, .DataItem("PartialDays"), .DataItem("PartialDays") / .DataItem("TotalDays"))
                .Cells(SOSExceptionCols.MissingDays).Text = String.Format(sTemplateExceptionsGrid, .DataItem("MissingDays"), .DataItem("MissingDays") / .DataItem("TotalDays"))
                .Cells(SOSExceptionCols.TotalExceptions).Text = String.Format(sTemplateExceptionsGrid, .DataItem("TotalExceptions"), .DataItem("TotalExceptions") / .DataItem("TotalDays"))

                Dim pctExceptions = (.DataItem("TotalExceptions") / .DataItem("TotalDays")) * 100

                'easy css coloring.
                If pctExceptions > 75 Then
                    .BackColor = System.Drawing.ColorTranslator.FromHtml("#FFACAC")

                ElseIf pctExceptions > 25 Then
                    .BackColor = System.Drawing.ColorTranslator.FromHtml("#FFDC9C")

                End If
            End With
        End If
    End Sub
#End Region

#Region "Export"
    Protected Sub btnExportSummary_Click(sender As Object, e As EventArgs)
        ExportToExcel(grdSOSReport)
    End Sub


#End Region

#Region "GridView - Export To Excel"
    'EXPORT SETTINGS
    Const sFileName As String = "Speed-Of-Service-Detail-Report"
    Const bShowTitle As Boolean = True
    Const bShowSubTitle As Boolean = True

    Private Function GetStyles(ByRef Grid As GridView) As List(Of String)

        GetStyles = New List(Of String)()
        GetStyles.AddRange(Grid.CssClass.Split(" "))

    End Function

    Private Function GetStyles(ByRef Grid As GridView, ByRef GridRow As GridViewRow) As List(Of String)

        GetStyles = New List(Of String)()
        GetStyles.AddRange(IIf(GridRow.RowState And DataControlRowState.Alternate, Grid.AlternatingRowStyle, Grid.RowStyle).CssClass.Split(" "))
        GetStyles.AddRange(GridRow.CssClass.Split(" "))

    End Function

    Private Function GetStyles(ByRef Grid As GridView, ByRef GridRow As GridViewRow, ByRef Cell As TableCell, ByVal iColumnIndex As Integer) As List(Of String)

        GetStyles = New List(Of String)()

        Dim Column As DataControlField = Grid.Columns(iColumnIndex - 1)

        Select Case GridRow.RowType
            Case DataControlRowType.Header
                GetStyles.AddRange(Column.HeaderStyle.CssClass.Split(" "))
                GetStyles.AddRange(Grid.HeaderStyle.CssClass.Split(" "))
            Case DataControlRowType.DataRow
                GetStyles.AddRange(Column.ItemStyle.CssClass.Split(" "))
            Case DataControlRowType.Footer
                GetStyles.AddRange(Column.FooterStyle.CssClass.Split(" "))
                GetStyles.AddRange(Grid.FooterStyle.CssClass.Split(" "))
        End Select
        GetStyles.AddRange(Cell.CssClass.Split(" "))

    End Function

    Private Function ChooseColorFromRowState(ByVal Grid As GridView, ByVal RowState As DataControlRowState, ByVal Normal As String, ByVal Alternate As String) As System.Drawing.Color
        ChooseColorFromRowState = IIf((RowState And DataControlRowState.Alternate) AndAlso Grid.CssClass.Contains("Zebra"), System.Drawing.ColorTranslator.FromHtml(Alternate), System.Drawing.ColorTranslator.FromHtml(Normal))
    End Function

    Private Sub DoColors(ByVal GridClasses As List(Of String), ByVal RowClasses As List(Of String), ByVal CellClasses As List(Of String), ByRef ExcelCell As ExcelRange, ByRef Row As GridViewRow)

        ExcelCell.Style.Fill.PatternType = Style.ExcelFillStyle.Solid


        If RowClasses.Contains("OnHand") Or CellClasses.Contains("OnHand") Then
            ExcelCell.Style.Fill.BackgroundColor.SetColor(ChooseColorFromRowState(CType(Row.NamingContainer, GridView), Row.RowState, "#add8e6", "#9bc2cf"))
            Exit Sub
        End If

        If RowClasses.Contains("Incidental") Or CellClasses.Contains("Incidental") Then
            ExcelCell.Style.Fill.BackgroundColor.SetColor(ChooseColorFromRowState(CType(Row.NamingContainer, GridView), Row.RowState, "#8fbc8f", "#80a980"))
            Exit Sub
        End If

        If RowClasses.Contains("Success") Or CellClasses.Contains("Success") Then
            ExcelCell.Style.Fill.BackgroundColor.SetColor(ChooseColorFromRowState(CType(Row.NamingContainer, GridView), Row.RowState, "#6eff6e", "#63e563"))
            Exit Sub
        End If

        If RowClasses.Contains("Warning") Or CellClasses.Contains("Warning") Then
            ExcelCell.Style.Fill.BackgroundColor.SetColor(ChooseColorFromRowState(CType(Row.NamingContainer, GridView), Row.RowState, "#ffb76e", "#e5a463"))
            Exit Sub
        End If

        If RowClasses.Contains("Error") Or CellClasses.Contains("Error") Then
            ExcelCell.Style.Fill.BackgroundColor.SetColor(ChooseColorFromRowState(CType(Row.NamingContainer, GridView), Row.RowState, "#ff6e6e", "#e56363"))
            Exit Sub
        End If

        If RowClasses.Contains("GroupHeader") Or CellClasses.Contains("GroupHeader") Then
            ExcelCell.Style.Fill.BackgroundColor.SetColor(ChooseColorFromRowState(CType(Row.NamingContainer, GridView), Row.RowState, "#A8BED7", "#A8BED7"))
            Exit Sub
        End If

        If RowClasses.Contains("GroupFooter") Or CellClasses.Contains("GroupFooter") Then
            ExcelCell.Style.Fill.BackgroundColor.SetColor(ChooseColorFromRowState(CType(Row.NamingContainer, GridView), Row.RowState, "#A8BED7", "#A8BED7"))
            Exit Sub
        End If

        If RowClasses.Contains("Header") Or CellClasses.Contains("Header") Then
            ExcelCell.Style.Fill.BackgroundColor.SetColor(ChooseColorFromRowState(CType(Row.NamingContainer, GridView), Row.RowState, "#DCE3E8", "#DCE3E8"))
            Exit Sub
        End If

        If RowClasses.Contains("Seperator") Or CellClasses.Contains("Seperator") Then
            ExcelCell.Style.Fill.BackgroundColor.SetColor(ChooseColorFromRowState(CType(Row.NamingContainer, GridView), Row.RowState, "#808080", "#808080"))
            Exit Sub
        End If

        If RowClasses.Contains("ExtraDetail") Then
            If RowClasses.Contains("Level-0") Then
                ExcelCell.Style.Fill.BackgroundColor.SetColor(ChooseColorFromRowState(CType(Row.NamingContainer, GridView), Row.RowState, "#CCCCCC", "#CCCCCC"))
            ElseIf RowClasses.Contains("Level-1") Then
                ExcelCell.Style.Fill.BackgroundColor.SetColor(ChooseColorFromRowState(CType(Row.NamingContainer, GridView), Row.RowState, "#B5B5B5", "#B5B5B5"))
            ElseIf RowClasses.Contains("Level-2") Then
                ExcelCell.Style.Fill.BackgroundColor.SetColor(ChooseColorFromRowState(CType(Row.NamingContainer, GridView), Row.RowState, "#A0A0A0", "#A0A0A0"))
            End If
            Exit Sub
        End If

        If CellClasses.Contains("Waste") Then
            ExcelCell.Style.Fill.BackgroundColor.SetColor(ChooseColorFromRowState(CType(Row.NamingContainer, GridView), Row.RowState, "#a6d7d8", "#a6d7d8"))
            Exit Sub
        End If
        If CellClasses.Contains("Transactions") Then
            ExcelCell.Style.Fill.BackgroundColor.SetColor(ChooseColorFromRowState(CType(Row.NamingContainer, GridView), Row.RowState, "#c7e9f5", "#c7e9f5"))
            Exit Sub
        End If
        If CellClasses.Contains("Usages") Then
            ExcelCell.Style.Fill.BackgroundColor.SetColor(ChooseColorFromRowState(CType(Row.NamingContainer, GridView), Row.RowState, "#ffefd5", "#ffefd5"))
            Exit Sub
        End If

        If CellClasses.Contains("Costs") Then
            ExcelCell.Style.Fill.BackgroundColor.SetColor(ChooseColorFromRowState(CType(Row.NamingContainer, GridView), Row.RowState, "#ffefd5", "#ffefd5"))
            Exit Sub
        End If


        ExcelCell.Style.Fill.BackgroundColor.SetColor(ChooseColorFromRowState(CType(Row.NamingContainer, GridView), Row.RowState, "#F5F5F5", "#DCDCDC"))

    End Sub

    Private Function GetControlText(Control As Control) As String
        GetControlText = String.Empty
        If Control.Visible = False Then Exit Function

        If Control.Controls.Count > 0 Then
            For Each InnerControl As Control In Control.Controls
                GetControlText &= GetControlText(InnerControl)
            Next
        Else
            If TypeOf Control Is DataBoundLiteralControl Then
                GetControlText &= CType(Control, DataBoundLiteralControl).Text.Trim()
            ElseIf TypeOf Control Is LiteralControl Then
                GetControlText &= CType(Control, LiteralControl).Text.Trim()
            ElseIf TypeOf Control Is Label Then
                GetControlText &= CType(Control, Label).Text.Trim()
            ElseIf TypeOf Control Is LinkButton Then
                GetControlText &= CType(Control, LinkButton).Text.Trim()
            Else
                Try 'Cant check for Datacontrollinkbutton because it's a friend - DataControlLinkButton happens to be used in sortable gridview headers.
                    GetControlText &= CType(Control, LinkButton).Text.Trim()
                Catch ex As Exception
                    GetControlText &= "[" & Control.GetType().Name & "]"
                End Try
            End If
        End If
    End Function

    Private Sub ExportToExcel(ByRef Grid As GridView)

        Dim iColumnCount As Integer = Grid.Columns.Count
        Dim iRowCount As Integer = Grid.Rows.Count

        Dim iRowIndex = 1
        Dim iColumnIndex = 1
        Dim iExcelColumn = 1

        Dim iVisibleColumns = 0

        For Each Col As DataControlField In Grid.Columns
            If Col.Visible Then iVisibleColumns += 1
        Next

        Dim ExcelMemoryStream As New MemoryStream()
        Dim oExcelPackagePlus As New OfficeOpenXml.ExcelPackage(ExcelMemoryStream)

        Dim ws As OfficeOpenXml.ExcelWorksheet = oExcelPackagePlus.Workbook.Worksheets.Add("Export")

        If bShowTitle Then
            Dim TitleCells As ExcelRange = ws.Cells(iRowIndex, 1, iRowIndex, iVisibleColumns)

            TitleCells.Merge = True
            TitleCells.Style.Font.Bold = True
            TitleCells.Style.Font.Size *= 1.25
            TitleCells.Style.HorizontalAlignment = Style.ExcelHorizontalAlignment.Center
            'PAGE SPECIFIC, TITLE HERE
            TitleCells.Value = "Speed Of Service Report"
            'END PAGE SPECIFIC
            iRowIndex += 1
        End If
        If bShowTitle Then
            Dim TitleCells As ExcelRange = ws.Cells(iRowIndex, 1, iRowIndex, iVisibleColumns)
            TitleCells.Merge = True
            TitleCells.Style.HorizontalAlignment = Style.ExcelHorizontalAlignment.Center

            'PAGE SPECIFIC, TITLE HERE
            TitleCells.Style.WrapText = True
            ws.Row(iRowIndex).Height *= ((lblSubHeader.Text.Length - (lblSubHeader.Text.Replace("<br/>", String.Empty).Length)) / 5) + 1
            TitleCells.Value = lblSubHeader.Text.Replace("<br/>", vbNewLine)
            'END PAGE SPECIFIC
            iRowIndex += 1

        End If


        Dim GridClasses As List(Of String) = GetStyles(Grid)

        For Each Row As GridViewRow In CType(Grid.Rows(0).Parent, Web.UI.WebControls.Table).Rows

            If Not Row.Visible Then
                Continue For
            End If

            Dim RowClasses As List(Of String) = GetStyles(Grid, Row)

            For Each Cell As TableCell In Row.Cells
                If Not Grid.Columns(Row.Cells.GetCellIndex(Cell)).Visible Then
                    iColumnIndex += 1
                    Continue For
                End If


                Dim CellClasses As List(Of String) = GetStyles(Grid, Row, Cell, iColumnIndex)

                Dim ExcelCell As ExcelRange = ws.Cells(iRowIndex, iExcelColumn, iRowIndex, iExcelColumn + IIf(Cell.ColumnSpan > 1, Cell.ColumnSpan - 1, 0))
                ExcelCell.Style.Numberformat.Format = "0"
                '-----------------------
                '   Styles 
                '-----------------------
                DoColors(GridClasses, RowClasses, CellClasses, ExcelCell, Row)

                If CellClasses.Contains("Numeric") Then
                    ExcelCell.Style.HorizontalAlignment = Style.ExcelHorizontalAlignment.Right
                    ExcelCell.Style.Numberformat.Format = "#,##0.00"

                End If
                If String.IsNullOrEmpty(Cell.Text) Then
                    Cell.Text = GetControlText(Cell)
                    Cell.Text = Regex.Replace(Cell.Text, "\r+\n", "")
                    Cell.Text = Cell.Text.Replace("<hr />", Environment.NewLine).Replace("<hr/>", Environment.NewLine)
                    Cell.Text = Cell.Text.Replace("<br />", Environment.NewLine).Replace("<br/>", Environment.NewLine)
                    Cell.Text = Regex.Replace(Cell.Text, "[ ]{2,}", " ")
                    Cell.Text = Regex.Replace(Cell.Text, "<(?:[^>=]|='[^']*'|=""[^""]*""|=[^'""][^\s>]*)*>", "")
                    Cell.Text = Cell.Text.Trim()
                End If

                ws.Row(iRowIndex).Height = (Regex.Matches(Cell.Text, vbNewLine).Count) * 15
                ExcelCell.Style.WrapText = True

                '-----------------------
                '   Borders 
                '-----------------------
                With ExcelCell.Style.Border
                    .BorderAround(Style.ExcelBorderStyle.Thin, System.Drawing.ColorTranslator.FromHtml("#333333"))
                    .Top.Style = Style.ExcelBorderStyle.None
                    .Bottom.Style = Style.ExcelBorderStyle.None
                    .Left.Style = Style.ExcelBorderStyle.None
                    .Right.Style = Style.ExcelBorderStyle.None

                    If (GridClasses.Contains("RowBorders") OrElse RowClasses.Contains("RowBorders") OrElse CellClasses.Contains("RowBorders")) AndAlso (Not CellClasses.Contains("Seperator") AndAlso Not CellClasses.Contains("Vertical")) Then
                        'Not done in vertical seperators
                        .Top.Style = Style.ExcelBorderStyle.Thin
                        .Bottom.Style = Style.ExcelBorderStyle.Thin
                    End If

                    If (GridClasses.Contains("ColumnBorders") OrElse CellClasses.Contains("ColumnBorders")) AndAlso (Not RowClasses.Contains("Seperator") AndAlso Not RowClasses.Contains("Horizontal")) Then
                        'Not done in horizontal seperators
                        .Left.Style = Style.ExcelBorderStyle.Thin
                        .Right.Style = Style.ExcelBorderStyle.Thin
                    End If

                End With
                ExcelCell.Value = Cell.Text.Replace("<br/>", vbNewLine).Replace("<br>", vbNewLine).Replace("<br />", vbNewLine).Replace("&nbsp;", "")
                If IsNumeric(Cell.Text.Replace("%", "")) Then
                    ExcelCell.Value = Double.Parse(Cell.Text.Replace("%", ""), System.Globalization.NumberStyles.Any)

                    If Cell.Text.Contains("$") Then
                        ExcelCell.Style.Numberformat.Format = "$#,##0.00"
                    ElseIf Cell.Text.Contains("%") Then
                        ExcelCell.Value /= 100
                        ExcelCell.Style.Numberformat.Format = "#,##0.00%"
                    End If
                End If

                If Cell.ColumnSpan > 1 Then
                    ExcelCell.Merge = True
                    iColumnIndex += Cell.ColumnSpan
                    iExcelColumn += Cell.ColumnSpan
                Else
                    iColumnIndex += 1
                    iExcelColumn += 1
                End If

            Next Cell

            iRowIndex += 1
            iExcelColumn = 1
            iColumnIndex = 1
        Next Row


        'I got tired of having to expand rows to fit numbers myself
        For i As Integer = 1 To iColumnCount
            ws.Column(i).BestFit = True
            ws.Column(i).AutoFit(18)
        Next i
        'Save the workbook1

        oExcelPackagePlus.Save()


        With Response
            .Clear()
            .ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            .AddHeader("content-disposition", "attachment;filename=" & sFileName & "-" & Date.Now.ToShortDateString.Replace("/", "-") & ".xlsx")
            ExcelMemoryStream.WriteTo(.OutputStream)
            .Flush() 'Send buffer to client
            .SuppressContent = True 'Prevent ASP.Net from appending further content
        End With

        Context.ApplicationInstance.CompleteRequest() 'Skip straight to end request event, do not pass go, do not collect $200


    End Sub

#End Region

End Class
