﻿Imports System.Data
Imports System.IO
Imports OfficeOpenXml
Imports System.Windows.Forms

''' <summary>
''' [ 2017-04-20 ] BDean: Sprint 33 // B-02779 // TK-03214 - Modified to allow the user to download an Excel template, and upload a workbook as opposed to a CSV file.
''' [ 2017-10-23 ] BDean: Sprint 41 // B-03139 // TK-04070 - Modified to provide a drop-down list of "Recruiter" types. The selected type will determine how the DLL is called when
'''                                                          the user uploads an employee file.
'''                                                        - I removed all of the previous changes, as they are no longer valid due to a change that <PERSON> made to the DLL for
'''                                                          story B-03137.
''' [ 2022-06-20 ] BDean: SBOD-48/SBOD-934 (duplicates), SBOD-1765, SBOD-2330 - I updated the template, modified how the DLL is called, and added "Processing Options" for users that are
'''                                                                             rank 900 or higher that will allow them to determine how the DLL is called.
''' </summary>

Public Class Admin_EmployeeBulkImport
    Inherits System.Web.UI.Page
    Dim InvWSS As InventoryWebSiteServices.InventoryServices

    Dim userRank As Integer = 0
    Dim userRoleID As String = 0

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        If Len(Session("UserID")) = 0 Then
            Response.Redirect("login.aspx?timeout=1")
        End If

        InvWSS = Session("IWSS")

        GetUserRank(Session("AccountID"), userRank, userRoleID)

        'If the user's rank is too low --> Do not show the "Processing Options" fieldset
        fsProcessingOptions.Visible = userRank >= 900

        tbShowPopup.Text = If(Not IsPostBack, "False", "True")

    End Sub

    Public Sub btnSubmit_OnClick(sender As Object, e As EventArgs)

        Dim objEmployeeImport As New EmployeeImport.EmpImporter(InvWSS)

        Select Case Path.GetExtension(FileUpload1.FileName.ToLower())

            Case ".xls", ".xlsx"

                Dim oExcelPackagePlus As New ExcelPackage(FileUpload1.PostedFile.InputStream)
                Dim ws As ExcelWorksheet = oExcelPackagePlus.Workbook.Worksheets(1)
                Dim wsEndRow As Integer = ws.Dimension.End.Row
                Dim wsEndColumn As Integer = 27

                oExcelPackagePlus.Workbook.Calculate()

                Dim ms As New MemoryStream
                Dim sw As New StreamWriter(ms)

                'Loop through each row...
                For y As Integer = 1 To wsEndRow

                    Dim line As String = ""

                    'Loop through each column, and build a comma separated string of values...
                    For x As Integer = 1 To wsEndColumn
                        line += ws.Cells(y, x).Value & If(x = wsEndColumn, "", ",")
                    Next

                    sw.WriteLine(line)

                Next

                sw.Flush()
                ms.Position = 0

                objEmployeeImport.DoManual(ms, divPopupMessageContainer.InnerHtml, CBool(ddlTrainingEmployees.SelectedValue), CBool(ddlTestMode.SelectedValue))

            Case ".csv"

                objEmployeeImport.DoManual(FileUpload1, divPopupMessageContainer, CBool(ddlTrainingEmployees.SelectedValue), CBool(ddlTestMode.SelectedValue))

            Case Else

                divPopupMessageContainer.InnerHtml = "<p>File must be .xls, .xlsx, or .csv format</p>"

        End Select

    End Sub

    Public Sub btnDownloadTemplate_OnClick(ByVal sender As Object, ByVal e As EventArgs)

        Me.EnableViewState = False

        Dim ExcelMemoryStream As New MemoryStream()
        Dim oExcelPackagePlus As New ExcelPackage(ExcelMemoryStream)

        Dim ws As ExcelWorksheet = oExcelPackagePlus.Workbook.Worksheets.Add("Template")

        With ws

            '--------------------------------------------------------------------------------
            ' Section Title (Row)
            '--------------------------------------------------------------------------------

            .Cells("A1").Value = "DO NOT INCLUDE THIS ROW - OR THE HEADER ROW - IN THE FILE TO BE UPLOADED"

            '--------------------------------------------------------------------------------
            ' Headers (Row)
            '--------------------------------------------------------------------------------

            .Cells("A2").Value = "Store Number"
            .Cells("B2").Value = "Employee Number"
            .Cells("C2").Value = "Birthday"
            .Cells("D2").Value = "Hire Date"
            .Cells("E2").Value = "Last Name"
            .Cells("F2").Value = "First Name"
            .Cells("G2").Value = "Middle Initial (Optional)"
            .Cells("H2").Value = "Address 1"
            .Cells("I2").Value = "Address 2 (Optional)"
            .Cells("J2").Value = "City"
            .Cells("K2").Value = "State Abbreviation"
            .Cells("L2").Value = "Zip Code"
            .Cells("M2").Value = "Social Number"
            .Cells("N2").Value = "Email"
            .Cells("O2").Value = "Home Phone"
            .Cells("P2").Value = "FT/PT Status"
            .Cells("Q2").Value = "State Marital Status"
            .Cells("R2").Value = "Federal Marital Status"
            .Cells("S2").Value = "Race Code"
            .Cells("T2").Value = "Gender"
            .Cells("U2").Value = "Citizen"
            .Cells("V2").Value = "Job Code"
            .Cells("W2").Value = "Pay Rate"
            .Cells("X2").Value = "Term Code"
            .Cells("Y2").Value = "Term Date"
            .Cells("Z2").Value = "Payroll ID"
            .Cells("AA2").Value = "Home Store Override"

            '--------------------------------------------------------------------------------
            ' Sample Data (Row)
            '--------------------------------------------------------------------------------

            .Cells("A3").Value = 1
            .Cells("B3").Value = 124222244
            .Cells("C3").Value = "12/16/1977"
            .Cells("D3").Value = "11/8/2016"
            .Cells("E3").Value = "Kenobi"
            .Cells("F3").Value = "Ben"
            .Cells("G3").Value = "O"
            .Cells("H3").Value = "1025 Tatooine Dr"
            .Cells("I3").Value = ""
            .Cells("J3").Value = "Syracuse"
            .Cells("K3").Value = "NY"
            .Cells("L3").Value = 13212
            .Cells("M3").Value = "124-22-2244"
            .Cells("N3").Value = "<EMAIL>"
            .Cells("O3").Value = "************"
            .Cells("P3").Value = "PT"
            .Cells("Q3").Value = 1
            .Cells("R3").Value = 0
            .Cells("S3").Value = 1
            .Cells("T3").Value = "M"
            .Cells("U3").Value = 1
            .Cells("V3").Value = 1
            .Cells("W3").Value = 8
            .Cells("X3").Value = ""
            .Cells("Y3").Value = ""
            .Cells("Z3").Value = ""
            .Cells("AA3").Value = ""

            '--------------------------------------------------------------------------------
            ' Sample Data (Row)
            '--------------------------------------------------------------------------------

            .Cells("A4").Value = 5
            .Cells("B4").Value = 124222245
            .Cells("C4").Value = "12/17/1987"
            .Cells("D4").Value = "11/8/2016"
            .Cells("E4").Value = "Perry"
            .Cells("F4").Value = "Katy"
            .Cells("G4").Value = ""
            .Cells("H4").Value = "522 Rock Star Lane"
            .Cells("I4").Value = ""
            .Cells("J4").Value = "New Hartford"
            .Cells("K4").Value = "NY"
            .Cells("L4").Value = 13413
            .Cells("M4").Value = "124-22-2245"
            .Cells("N4").Value = "<EMAIL>"
            .Cells("O4").Value = "************"
            .Cells("P4").Value = "PT"
            .Cells("Q4").Value = 1
            .Cells("R4").Value = 1
            .Cells("S4").Value = 3
            .Cells("T4").Value = "F"
            .Cells("U4").Value = 1
            .Cells("V4").Value = 1
            .Cells("W4").Value = 9
            .Cells("X4").Value = ""
            .Cells("Y4").Value = ""
            .Cells("Z4").Value = 555555555
            .Cells("AA4").Value = 1

            '--------------------------------------------------------------------------------
            ' Misc Styling
            '--------------------------------------------------------------------------------

            With .Cells("A1:AA1")
                .Merge = True
                .Style.HorizontalAlignment = Style.ExcelHorizontalAlignment.Center
                .Style.Font.Size = 14
                .Style.Font.Color.SetColor(System.Drawing.Color.Red)
            End With

            .Cells("A2:AA2").Style.Font.Bold = True
            .Cells("A2:AA4").Style.Font.Size = 10
            .Cells("A2:AA4").Style.HorizontalAlignment = Style.ExcelHorizontalAlignment.Left

            .Cells.AutoFitColumns()

        End With

        Dim ws2 As ExcelWorksheet = oExcelPackagePlus.Workbook.Worksheets.Add("Notes")

        With ws2

            '--------------------------------------------------------------------------------
            ' Section Title (Row)
            '--------------------------------------------------------------------------------

            .Cells("A1").Value = "FIELD REQUIREMENTS"

            '--------------------------------------------------------------------------------
            ' Headers (Row)
            '--------------------------------------------------------------------------------

            .Cells("A2").Value = "Store Number (SBOnet)"
            .Cells("B2").Value = "Employee Number"
            .Cells("C2").Value = "Birthday"
            .Cells("D2").Value = "Hire Date"
            .Cells("E2").Value = "Last Name"
            .Cells("F2").Value = "First Name"
            .Cells("G2").Value = "Middle Initial (Optional)"
            .Cells("H2").Value = "Address 1"
            .Cells("I2").Value = "Address 2 (Optional)"
            .Cells("J2").Value = "City"
            .Cells("K2").Value = "State Abbreviation"
            .Cells("L2").Value = "Zip Code"
            .Cells("M2").Value = "Social Number"
            .Cells("N2").Value = "Email"
            .Cells("O2").Value = "Home Phone"
            .Cells("P2").Value = "FT/PT Status"
            .Cells("Q2").Value = "State Marital Status"
            .Cells("R2").Value = "Federal Marital Status"
            .Cells("S2").Value = "Race Code"
            .Cells("T2").Value = "Gender"
            .Cells("U2").Value = "Citizen"
            .Cells("V2").Value = "Job Code"
            .Cells("W2").Value = "Pay Rate"
            .Cells("X2").Value = "Term Code (Optional)"
            .Cells("Y2").Value = "Term Date (Optional)"
            .Cells("Z2").Value = "Payroll ID (Optional)"
            .Cells("AA2").Value = "Home Store Override (Optional)"

            '--------------------------------------------------------------------------------
            ' Data Types (Row)
            '--------------------------------------------------------------------------------

            .Cells("A3").Value = "Numeric"
            .Cells("B3").Value = "Numeric"
            .Cells("C3").Value = "Date"
            .Cells("D3").Value = "Date"
            .Cells("E3").Value = "Non-Numeric"
            .Cells("F3").Value = "Non-Numeric"
            .Cells("G3").Value = "Non-Numeric"
            .Cells("H3").Value = "Alpha-Numeric"
            .Cells("I3").Value = "Alpha-Numeric"
            .Cells("J3").Value = "Non-Numeric"
            .Cells("K3").Value = "Non-Numeric"
            .Cells("L3").Value = "USA = Numeric"
            .Cells("L4").Value = "CAN = Alpha-Numeric (A1A 1A1)"
            .Cells("M3").Value = "Numeric"
            .Cells("N3").Value = "Alpha-Numeric"
            .Cells("O3").Value = "Numeric"
            .Cells("P3").Value = "Non-Numeric"
            .Cells("Q3").Value = "Numeric"
            .Cells("R3").Value = "Numeric"
            .Cells("S3").Value = "Numeric"
            .Cells("T3").Value = "Non-Numeric"
            .Cells("U3").Value = "Numeric"
            .Cells("V3").Value = "Numeric"
            .Cells("W3").Value = "Numeric"
            .Cells("X3").Value = "Numeric"
            .Cells("Y3").Value = "Date"
            .Cells("Z3").Value = "Numeric"
            .Cells("AA3").Value = "Numeric"


            '--------------------------------------------------------------------------------
            ' Multiple Job Code description
            '--------------------------------------------------------------------------------
            .Cells("V5").Value = "Multiple Job Codes and Pay Rates may be specified using the ""|"" character between each Value"
            .Cells("V6").Value = "There must be the same number of JobCodes and Payrates specified.  They're paired up in the order entered."


            '--------------------------------------------------------------------------------
            ' Section Title (Row)
            '--------------------------------------------------------------------------------

            .Cells("A7").Value = "ACCEPTED VALUES/DESCRIPTIONS"

            '--------------------------------------------------------------------------------
            ' Headers (Row)
            '--------------------------------------------------------------------------------

            .Cells("A8").Value = "Store Number (SBOnet)"
            .Cells("B8").Value = "Employee Number"
            .Cells("C8").Value = "Birthday"
            .Cells("D8").Value = "Hire Date"
            .Cells("E8").Value = "Last Name"
            .Cells("F8").Value = "First Name"
            .Cells("G8").Value = "Middle Initial (Optional)"
            .Cells("H8").Value = "Address 1"
            .Cells("I8").Value = "Address 2 (Optional)"
            .Cells("J8").Value = "City"
            .Cells("K8").Value = "State Abbreviation"
            .Cells("L8").Value = "Zip Code"
            .Cells("M8").Value = "Social Number"
            .Cells("N8").Value = "Email"
            .Cells("O8").Value = "Home Phone"
            .Cells("P8").Value = "FT/PT Status"
            .Cells("Q8").Value = "State Marital Status"
            .Cells("R8").Value = "Federal Marital Status"
            .Cells("S8").Value = "Race Code"
            .Cells("T8").Value = "Gender"
            .Cells("U8").Value = "Citizen"
            .Cells("V8").Value = "Job Code"  'This will be overwritten when querying the [dbo].[tmJobCodePosProgram] table for this customer's Job Codes
            .Cells("W8").Value = "Pay Rate"
            .Cells("X8").Value = "Term Code (Optional)"
            .Cells("Y8").Value = "Term Date (Optional)"
            .Cells("Z8").Value = "Payroll ID (Optional)"
            .Cells("AA8").Value = "Home Store Override (Optional)"

            '--------------------------------------------------------------------------------
            ' Employee Number (Column)
            '--------------------------------------------------------------------------------

            .Cells("B9").Value = "0 - 999999999"

            '--------------------------------------------------------------------------------
            ' FT/PT Status (Column)
            '--------------------------------------------------------------------------------

            .Cells("P9").Value = "FT = Full Time"
            .Cells("P10").Value = "PT = Part Time"

            '--------------------------------------------------------------------------------
            ' State Marital Status (Column)
            '--------------------------------------------------------------------------------

            .Cells("Q9").Value = "0 = Not Specified"
            .Cells("Q10").Value = "1 = Single"
            .Cells("Q11").Value = "2 = Married"
            .Cells("Q12").Value = "3 = Divorced"
            .Cells("Q13").Value = "4 = Widowed"

            '--------------------------------------------------------------------------------
            ' Federal Marital Status (Column)
            '--------------------------------------------------------------------------------

            .Cells("R9").Value = "0 = Not Specified"
            .Cells("R10").Value = "1 = Single"
            .Cells("R11").Value = "2 = Married"
            .Cells("R12").Value = "3 = Divorced"
            .Cells("R13").Value = "4 = Widowed"

            '--------------------------------------------------------------------------------
            ' Race Code (Column)
            '--------------------------------------------------------------------------------

            .Cells("S9").Value = "1 = White"
            .Cells("S10").Value = "2 = Black/African American"
            .Cells("S11").Value = "3 = Hispanic/Spanish"
            .Cells("S12").Value = "4 = Asian"
            .Cells("S13").Value = "5 = American Indian/Alaskan"
            .Cells("S14").Value = "6 = Native Hawaiian/Other Pacific Islander"
            .Cells("S15").Value = "7 = Middle Eastern"
            .Cells("S16").Value = "98 = Refused To Specify"
            .Cells("S17").Value = "99 = Multiple Races"

            '--------------------------------------------------------------------------------
            ' Gender (Column)
            '--------------------------------------------------------------------------------

            .Cells("T9").Value = "M = Male"
            .Cells("T10").Value = "F = Female"

            '--------------------------------------------------------------------------------
            ' Citizen (Column)
            '--------------------------------------------------------------------------------

            .Cells("U9").Value = "0 = No"
            .Cells("U10").Value = "1 = Yes"

            '--------------------------------------------------------------------------------
            ' Job Code (Column)
            '--------------------------------------------------------------------------------

            Dim jobCodeRowNumber As Integer = 8
            Dim sbJobCodes As New StringBuilder()
            Dim dtJobCodes As DataTable = Nothing

            With sbJobCodes

                .AppendLine("SELECT")
                .AppendLine("	(")
                .AppendLine("		CASE")
                .AppendLine("			WHEN Result.[RowType] = 0 THEN 'Job Code - ' + Result.[Description]")
                .AppendLine("			ELSE CAST( Result.[JobCode] AS VARCHAR ) + ' = ' + Result.[JobDescription]")
                .AppendLine("		END")
                .AppendLine("	 ) AS [JobCodeDescription]")
                .AppendLine("	,Result.[RowType]")
                .AppendLine("FROM")
                .AppendLine("(")
                .AppendLine("	SELECT")
                .AppendLine("		 ISNULL( tmPP.[Description], 'Unknown' ) AS [Description]")
                .AppendLine("		,tmJCPP.[JobCode]")
                .AppendLine("		,tmJCPP.[JobDescription]")
                .AppendLine("		,(")
                .AppendLine("			CASE")
                .AppendLine("				WHEN tmJCPP.[JobCode] IS NULL THEN 0")
                .AppendLine("				ELSE 1")
                .AppendLine("			END")
                .AppendLine("		 ) AS [RowType]")
                .AppendLine("	FROM")
                .AppendLine("		[dbo].[tmJobCodePosProgram] AS [tmJCPP] WITH( NOLOCK )")
                .AppendLine("		LEFT JOIN [dbo].[tmPOSProgram] AS [tmPP] WITH( NOLOCK )")
                .AppendLine("			ON tmJCPP.[POSProgramID] = tmPP.[POSProgramID]")
                .AppendLine("	WHERE")
                .AppendLine("		tmJCPP.[POSProgramID] IN ( SELECT [POSProgramID] FROM [dbo].[tmStoreInvt] WITH( NOLOCK ) GROUP BY [POSProgramID] )")
                .AppendLine("		AND tmJCPP.[JobDescription] IS NOT NULL")
                .AppendLine("		AND tmJCPP.[JobDescription] <> 'From ttsTimeKeeping'")
                .AppendLine("	GROUP BY GROUPING SETS")
                .AppendLine("	(")
                .AppendLine("		 ( tmPP.[Description], tmJCPP.[JobCode], tmJCPP.[JobDescription] )")
                .AppendLine("		,( tmPP.[Description] )")
                .AppendLine("	)")
                .AppendLine(") AS [Result]")
                .AppendLine("ORDER BY")
                .AppendLine("	 Result.[Description]")
                .AppendLine("	,Result.[RowType];")

            End With

            InvWSS.GetData(sbJobCodes.ToString(), dtJobCodes)

            For Each drJobCode As DataRow In dtJobCodes.Rows

                'If we are creating a "Header" cell other than the 1st one ( formatting is already applied below for row 8 ) --> Skip a row and add some formatting
                If jobCodeRowNumber > 8 AndAlso drJobCode("RowType") = "0" Then

                    jobCodeRowNumber += 1

                    .Cells("V" & jobCodeRowNumber).Style.Font.Bold = True
                    .Cells("V" & jobCodeRowNumber).Style.Font.Size = 10

                End If

                .Cells("V" & jobCodeRowNumber).Value = drJobCode("JobCodeDescription")

                jobCodeRowNumber += 1

            Next drJobCode

            '--------------------------------------------------------------------------------
            ' Term Code (Column)
            '--------------------------------------------------------------------------------

            Dim termCodeRowNumber As Integer = 9
            Dim sbTermCodes As New StringBuilder()
            Dim dtTermCodes As DataTable = Nothing

            With sbTermCodes

                .AppendLine("SELECT")
                .AppendLine("	CAST( [TermCode] AS VARCHAR ) + ' = ' + [TermReason] AS [TermCodeReason]")
                .AppendLine("FROM")
                .AppendLine("   [dbo].[tdEmployeeTermCode] WITH( NOLOCK );")

            End With

            InvWSS.GetData(sbTermCodes.ToString(), dtTermCodes)

            For Each drTermCode As DataRow In dtTermCodes.Rows

                .Cells("X" & termCodeRowNumber).Value = drTermCode("TermCodeReason")

                termCodeRowNumber += 1

            Next drTermCode

            '--------------------------------------------------------------------------------
            ' Payroll ID (Column)
            '--------------------------------------------------------------------------------

            .Cells("Z9").Value = "0 - 999999999"

            '--------------------------------------------------------------------------------
            ' Home Store Override (Column)
            '--------------------------------------------------------------------------------

            .Cells("AA9").Value = "0 = No"
            .Cells("AA10").Value = "1 = Yes"

            '--------------------------------------------------------------------------------
            ' Misc Styling
            '--------------------------------------------------------------------------------

            With .Cells("A1:AA1")
                .Merge = True
                .Style.HorizontalAlignment = Style.ExcelHorizontalAlignment.Center
                .Style.Font.Size = 14
                .Style.Font.Color.SetColor(System.Drawing.Color.Red)
            End With

            With .Cells("A7:AA7")
                .Merge = True
                .Style.HorizontalAlignment = Style.ExcelHorizontalAlignment.Center
                .Style.Font.Size = 14
                .Style.Font.Color.SetColor(System.Drawing.Color.Red)
            End With

            .Cells("A2:AA2").Style.Font.Bold = True
            .Cells("A8:AA8").Style.Font.Bold = True
            .Cells("A2:AA4").Style.Font.Size = 10
            .Cells("A8:AA65").Style.Font.Size = 10
            .Cells("A2:AA4").Style.HorizontalAlignment = Style.ExcelHorizontalAlignment.Left
            .Cells("A8:AA65").Style.HorizontalAlignment = Style.ExcelHorizontalAlignment.Left

            .Cells.AutoFitColumns()

        End With

        'Save the workbook
        oExcelPackagePlus.Save()

        With Response

            .Clear()
            .ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            .AddHeader("content-disposition", "attachment;filename=EmployeeBulkImport_Template.xlsx")
            .BinaryWrite(ExcelMemoryStream.ToArray())
            .Flush()
            .SuppressContent = True

        End With

        Context.ApplicationInstance.CompleteRequest()

    End Sub

    Private Sub GetUserRank(ByVal UAI As String, ByRef iUserRank As Integer, ByRef sRoleID As String)

        Dim tbRoles As New DataTable
        Dim row As DataRow
        Dim iRoleRank As Integer

        InvWSS.GetListUserRoles(UAI, tbRoles)

        For Each row In tbRoles.Rows

            iRoleRank = GetRoleRank(row("ItemID"))

            If row("IsMember") And iRoleRank > iUserRank Then

                iUserRank = iRoleRank
                sRoleID = row("itemid")

            End If

        Next

        tbRoles.Dispose()

    End Sub

    Private Function GetRoleRank(ByVal URI As String) As Integer

        Dim sbSQL As New StringBuilder
        Dim tabRank As New DataTable
        Dim row As DataRow
        Dim iRank As Integer

        sbSQL.AppendLine("SELECT")
        sbSQL.AppendLine("  [Rank]")
        sbSQL.AppendLine("FROM")
        sbSQL.AppendLine("  [dbo].[tmRole] WITH( NOLOCK )")
        sbSQL.AppendLine("WHERE")
        sbSQL.AppendLine("  [UniversalRoleIdentifier] = '" & URI & "'")

        'Get the SQL results
        InvWSS.GetData(sbSQL.ToString, tabRank)

        For Each row In tabRank.Rows
            iRank = row("Rank")
        Next

        'Clear the results from memory
        tabRank.Dispose()

        Return iRank

    End Function

End Class