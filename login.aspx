<%@ Page Language="VB" Debug="true" EnableViewState="true" AutoEventWireup="false" Inherits="Login" CodeFile="Login.aspx.vb" %>
<!-- [2019-07-11] BDean: I added a HTML5 DOCTYPE -->
<!DOCTYPE html>
<html>  
<head>
    <title>Dumac Business Systems Inventory Manager</title>            
    <link rel="shortcut icon" href="https://sbonet.ncrsaas.com/webinventoryproduction/favicon.ico" type="image/x-icon" />
    <link rel="STYLESHEET" href="https://sbonet.ncrsaas.com/webinventoryproduction400/clsWendysApp.css" type="text/css" media="screen"/> 
    <link rel="STYLESHEET" href="https://sbonet.ncrsaas.com/webinventoryproduction400/clsCalendar.css" type="text/css" />
    <!--2022-01-26 jjc removing google Analytics-->
    <!--<script src="https://sbonet.ncrsaas.com/webinventoryproduction400/JS/GA.js" type="text/javascript" ></script> -->
    <!-- [2019-07-11] BDean: I rolled this back to a 1.x version of jQuery, as 2.x doesn't support older versions of IE, and JS errors were preventing users from logging into SBOnet because of it -->
	<script src="JS/jquery-1.10.2.js"></script>
    <script type="text/javascript">
        function loadSubmit() {
            ProgressImage = document.getElementById("waitimage");
            document.getElementById("waitdiv").style.visibility = "visible";
            setTimeout("ProgressImage.src = ProgressImage.src", 100);
            return true;
        }
    </script> 
	<!-- [2019-07-11] BDean: If IE version 9 or older, Then manually create the HTML5 elements that old versions of IE don't recognize, so that they can be styled/used -->
	<!--[if lte IE 9]>
		<script>
			document.createElement("figure");
			document.createElement("figcaption");
		</script>
	<![endif]�>
    <!--BDean - Bug#2001-->
    <script type="text/javascript">
        function focusID() {
            document.getElementById("UserID").focus();
        }
    </script>
    <style type="text/css">
	
		/* [2019-07-11] BDean: This is needed to vertically center the login form now that I added a HTML5 DOCTYPE */
		html,
		body {
			height: 100%;
		}
		
        #wrapper {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            font-size: 0;
            text-align: center;
            z-index: 1;
        }
        #wrapper:before {
            display: inline-block;
            height: 100%;
            vertical-align: middle;
            content: '';
        }

        #interstitial {
            display: inline-block;
            vertical-align: middle;
            padding: 16px;
            width: 90%;
            max-width: 432px;
            background-color: #fff;
            border: 2px solid #000;
            font-family: Verdana;
            font-size: 14px;
        }
        #interstitial p {
            margin-bottom: 16px;
        }
        #interstitial p:last-child {
            margin-bottom: 0;
        }
        #interstitial p.title {
            font-weight: bold;
        }
        #interstitial a {
            color: #66e;
        }
        #interstitial a:hover {
            color: #44e;
        }
        #interstitial a#carbon {
            text-decoration: none;
            color: #000;
        }
        #interstitial input {
            margin-bottom: 16px;
            padding: 4px 8px;
        }
        #interstitial #logo {
            margin-bottom: 16px;
        }
        #interstitial figure {
            display: inline-block;
            margin-bottom: 16px;
            width: 50%;
        }		
		#interstitial figure figcaption {
			margin-top: 4px;
            font-weight: bold;
        }		
        #interstitial figure img {
            width: 100%;
            box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.3);
        }
        #interstitial figure img:hover {
            box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.5);
        }
    </style>
</head>    
<!--BDean - Bug#2001: Added a call to the "focusID()" function-->    
<body class="pagebody" onload="<%=sLoadURL%>; focusID();">
     
	 <!-- [2019-07-11] BDean: I added 100% height to vertically center the login form with the new HTML5 DOCTYPE -->
	 <form runat="server" style="height: 100%;">
     <table style="height: 100%; width: 100%;" border="0">
        <tr>
            <td valign="middle" align="center" style="text-align: center;">
                <center>
                <table border="0" class="maintable" style="width: 250px;">
                    <tr>
                        <td style="text-align: center; padding: 10px;" colspan="2"><img src="images/sbonetr.jpg" border="0" alt="Dumac Business Systems Inventory Manager" /><br /></td>
                    </tr>				
                    <tr>
                        <td style="width: 35%; font-size: 10px;font-family: helvetica; text-align: right;">User ID:</td>
                        <td style="width: 65%;"><asp:TextBox ClientIDMode="Static" id="UserID" text="" MaxLength="25" runat="server" cssclass="inputcontrols" style="width: 120px;" /></td>
                    </tr>
                    <tr>
                        <td style="width: 35%; font-size :10px;font-family: helvetica; text-align: right;">Password:</td>
                        <td style="width: 65%;"><asp:TextBox ClientIDMode="Static" id="Password" text="" textmode="password" MaxLength="25" runat="server" cssclass="inputcontrols" style="width: 120px;" /></td>
                    </tr>
                    <tr>
						<!-- [2019-07-11] BDean: I made this a "submit" type button so that the user can click "Enter" to login -->
						<!--					 I did not change the "Mobile Login" button, because I wanted to make sure the desktop button was clicked by default -->
                        <td style="text-align: center;padding-top: 10px;padding-bottom: 5px;" colspan="2"><button id="ClLogin" type="submit" class="buttons" style="width: 150px;">Login</button></td>
                    </tr>                   
                    <tr>
                        <td style="text-align: center;padding-top: 5px;padding-bottom: 5px;" colspan="2"><button id="ClMobileLogin" type="button" class="buttons" style="width: 150px;">Mobile Login</button></td>
                    </tr>              
		            <tr>
			            <td style="text-align: center;padding-top: 5px;padding-bottom: 15px;" colspan="2">
			                <a href="<%= If(Request.Url.Host.ToLower().Contains("localhost") OrElse Request.Url.Host.ToLower().Contains("127.0.0.1"), "ForgotUIDPwd.aspx", "../WebInventoryProduction400/ForgotUIDPwd.aspx") %>" style="font:9pt;">Trouble Signing In?</a><br />
			            </td>
			        </tr>		    
                    <tr>
                        <td style="text-align: center;font-size: 10px; color: Red;" colspan="2"><%=sCaption%></td>				                
                    </tr>
                    <tr>
                        <td style="text-align: center; padding-top: 15px; padding-bottom: 15px; border-top: dotted 2px #000000;" colspan="2"><a href="http://itunes.apple.com/app/sbonet/id460876592?mt=8" target="_blank" style="font-size: 8pt;"><img src="Images/AppStore.jpg" alt="SBOnet available on the App Store" style="border: 0px;" /></a></td>				                
                    </tr>	
                </table>
                <span style="font-size:9pt;">� 2004-<%=Now().Year.ToString%> DUMAC Business Systems, Inc.</span>
                </center>
            </td>
        </tr>		
     </table>		

    <%--BDean - Bug#2189: Adjusted the position/margins so that this div is centered to the browser window, as opposed to the content of the page--%>
    <div id="waitdiv" style="visibility:hidden; text-align:center; background-color:white; position:fixed; z-index:200; top:50%; left:50%; margin-left:-75px; margin-top:-15px; height:30px; width:150px; border: 1px solid #000000; padding:20px;">	
        <table><tr><td><img id="waitimage" src="images/wait.gif" alt="Please Wait" /></td><td style="font-family:Verdana, Arial, Helvetica, sans-serif; padding-left: 10px;">Please&nbsp;wait...</td></tr></table>
    </div>
         <div id="wrapper" style="display: none">
             <div id="interstitial">
                 <img id="logo" src="Images/logo_sbonet.jpg" />
                 <p>We are excited to provide you with exclusive early access to the new SBOnet v6.0!</p>
                 <p class="title">Click The Image To Proceed</p>
                 <a id="carbonLink" href="https://www.dumacsbo.com/">
                     <figure>
                         <div class="image-container">
                             <img src="Images/Carbon_Thumbnail_Lg.png" />
                         </div>
                         <figcaption>SBOnet v6.0</figcaption>
                     </figure>
                 </a>
                 <p id="continue-message" style="display: none;">If you are having issues using the new site, click <a id="continueOld" href="#">here</a> to continue to the old one.</p>
             </div>
         </div>
         
         <div style="visibility: hidden; display: none">
             <input type="submit" ClientIDMode="Static" OnServerClick="Login" runat="server" id="SrvLogin" name="SrvLogin"/>
             <input type="submit" ClientIDMode="Static" OnServerClick="MobileLogin" runat="server" id="SrvMobileLogin" name="SrvMobileLogin"/>
             <input type="submit" ClientIDMode="Static" OnServerClick="CarbonLogin" runat="server" id="SrvCarbonLogin" name="SrvCarbonLogin"/>
         </div>
    </form>

<script>
    function wait() {
        var progressImage = document.getElementById("waitimage");
        document.getElementById("waitdiv").style.visibility = "visible";
        setTimeout(function() {
            //I don't know what this does, but okay.
            progressImage.src = progressImage.src;
        }, 100);
    }

    function unwait() {
        document.getElementById("waitdiv").style.visibility = "hidden";
    }

    $(function() {
        'use strict';

        var $carbonModal = $("#wrapper"),
            $userId = $("#UserID"),
            $pwd = $("#Password"),
            $form = $("form"),
            $continueMessage = $("#continue-message"),
            $continue = $("#continueOld");

        $("#carbonLink").on('click',
            function(ev) {
                ev.preventDefault();
                $form.off('submit.login');
                $("#SrvCarbonLogin").click();
            });

        $continue.on('click',
            function(ev) {
                ev.preventDefault();
                $form.off('submit.login');
                if ($(this).data('target') === 'ClMobileLogin') {
                    $("#SrvMobileLogin").click();
                } else {
                    $("#SrvLogin").click();
                }
            });

        function handleLogin(ev) {

            wait();
            ev.preventDefault();

            $.ajax({
                    type: "POST",
                    url: "./Login.aspx/GetUserLoginOptions",
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    data: JSON.stringify({
                        userId: $userId.val(),
                        pwd: $pwd.val()
                    })
                })
                .then(function (result) {
                    
                    // If Carbon user --> Show the interstitial page
                    // Else --> Continue to SBOnet 4.0
                    if (result.d.IsCarbonUser) {

                        unwait();
                        $continue.data("target", ev.target.id);

                        // If the user can still access SBOnet 4.0 --> Show the message/hyperlink to do so
                        if (result.d.CanAccessSBOnet4) {
                            $continueMessage.show();
                        }

                        $carbonModal.show();

                        return;

                    }

                    $form.off('submit.login');

                    if (ev.target.id === "ClMobileLogin") {
                        $("#SrvMobileLogin").click();
                    } else {
                        $("#SrvLogin").click();
                    }

                });
        }

		// [2019-07-11] BDean: I modified this to execute an anonymous function that prevents the default action ( form submission )
		//					   This will allow the user to click "Enter" to log in, instead of having to click the button manually
        $("#ClLogin, #ClMobileLogin").on("click", function(event) {
		
			event.preventDefault();
			handleLogin( event );
			
		});
		
        $form.on('submit.login', handleLogin);
    });	
</script>
</body>
</html>