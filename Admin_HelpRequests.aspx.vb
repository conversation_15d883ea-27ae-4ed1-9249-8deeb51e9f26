Option Explicit On

Imports System.Data
Imports System.Data.SqlClient
Imports SBOTypeLibrary
Imports SBOWebSiteServices
Imports InventoryWebSiteServices

Partial Class Admin_HelpRequests
    Inherits System.Web.UI.Page

    Dim InvWSS As InventoryWebSiteServices.InventoryServices
    Private sBodyOnLoad As String
    Private tTable As New DataTable

    Sub Page_Load(ByVal sender As Object, ByVal e As EventArgs) Handles Me.Load
        If Len(Session("UserID")) = 0 Then
            Response.Redirect("login.aspx?timeout=1")
        End If

        InvWSS = Session("IWSS")

        If Not IsPostBack Then
            BindGrid()
        End If
    End Sub

    Sub BindGrid()
        Dim sSQL As String = "SELECT "
        sSQL &= "HelpRequestID, "
        sSQL &= "LEFT(Subject,50) AS Subject, "
        sSQL &= "REPLACE(PageAddedFrom,'https://www.dumacwebservices.com/webinventoryproduction400/','') AS PageAddedFrom, "
        sSQL &= "IsNull(Convert(VARCHAR,DateResolved,101), 'N/A') AS DateResolved, "
        sSQL &= "dbo.pf_FetchAccountName(tmHelpRequest.Requestor) AS Requestor, "
        sSQL &= "Birthday "
        sSQL &= "FROM tmHelpRequest "
        'Mark Williams Bug 1705
        sSQL &= "WHERE IsInHouse = 0 "

        If Not chklblShowAll.Checked Then
            sSQL &= "AND DateResolved IS NULL "
        End If
        sSQL &= "ORDER BY Birthday"

        If InvWSS.GetData(sSQL, tTable) Then
            MyDataGrid.DataSource = tTable
            MyDataGrid.DataBind()
            tTable.Dispose()
        End If
    End Sub

    Sub ShowAll(ByVal sender As Object, ByVal e As EventArgs)
        BindGrid()
    End Sub

    Sub btnNew_click(ByVal sender As Object, ByVal e As EventArgs)
        Response.Redirect("Admin_HelpRequestEdit.aspx?HelpRequestID=-1")
    End Sub

    Sub GoToReportPage(ByVal sender As Object, ByVal e As System.EventArgs)
        'Mark Willims Bug 521
        Response.Redirect("Admin_HelpRequestReport.aspx")
    End Sub
End Class
