﻿Imports System.IO
Imports SBOTypeLibrary
Imports OfficeOpenXml
Imports System.Data.SqlClient
Imports iTextSharp.text
Imports iTextSharp.text.pdf
Imports Obout.Grid
Imports AmazonUtilities.Mail.SimpleEmailService


'2016-10-11 jjc Sprint 25 // B-02541 // TK-02246 Added Columns: TheoreticalPct, ActualPct, VarPct
'2016-10-20 jjc Sprint 25 // B-02550 // TK-02482 Created link to New Inventory Detail report for the "Invt. Variance" column
'2016-12-27 jjc Sprint 28 // B-02658 // TK-02744 Change Behavior of closing Layout Options Div
'2016-12-28 jjc Sprint 28 // B-02658 // TK-02746 Add tooltips to headers
'2017-02-03 jjc Sprint 30 // B-02730 // TK-02904 Added [Franchise] to Order By.  To group together franchises within region/district
'2017-02-20 jjc Sprint 30 // B-02715 // TK-02920 // TK-02921 // TK-02922 // TK-02923 // TK-02924 // TK-02925 // TK-02926 // TK-02927 // TK-02928 // TK-02929
'   Financial Summary Layout Templates
'2017-02-20 jjc Sprint 30 // B-02753 // TK-02912 Added 3 New Columns: Labor Guide Hours / Budget Labor % / Overtime Hours
'2017-03-03 jjc Sprint 31 // B-02773 // TK-03024 Added Order By to PopStores()
'2017-03-03 jjc Sprint 31 // B-02772 // TK-03022 Integrated Template feature and email
'2017-05-04 jjc Sprint 33 // B-02832 // TK-03217 // TK-03226 // TK-03227 Add selection, saving and processing changes
'   to allow for specifying which template to use for sending email exports of the financial summary
'2017-07-07 jjc GCWen email - old Financial Summary sorted Distiricts by name not number, changed to match this behavior for Districts and Regions
'2017-07-25 jjc Sprint 37 // D-01250 // TK-03805 Implement @IncludeToday, determine if today's data is used for WTD comparisions.
'2018-01-09 jjc Sprint 44 // B-03212 // TK-04384 Add [KioskCount],[Kiosk],[KioskPct] to ColumnDefaults()
'   And [Tax] ,[GiftCardSold], [GiftCardRedeem], [CashToCount], [MobileOdr] Too
'2018-01-31 jjc Sprint 44 // B-03212 // TK-04384 Adding new Columns.  New columns were being defaulted to displayed for global templates
'2019-02-06 jjc Sprint 61 SBOD-419/424 Adding OtherHours Column
'2019-03-04 jjc Sprint 62 SBOD-549/550 Adding Meal replacement and CashVar %
'2019-07-15 jjc Rev 25 Sprint 64 SBOD-664/751 Added Surcharge
'2020-02-21 jjc Sprint 78 SBOD-1154/1670 Added handling of Average rows, coresponding to additions made to [pf_GetFinancialSummary] Revision 32
'2020-02-28 jjc Sprint 78 SBOD-1686/1701 Modified Template Save Javascript to account for the posability of someone entering multiple *'s in a template name.
'2020-04-07 jjc Sprint 79w15 SBOD-1686/1701 need to pass "Default" override for Global Templates in the mailrun section.
'2020-10-13 jjc Sprint 79w42 SBOD-2001 Adding Delivery Column for Uber Eats, Door Dash, and Grub Hub
'2020-11-09 jjc Rev 35 Sprint 79w45 SBOD-2025 Adding Breakfast $ and %  +/- LY
'2021-02-03 jjc Rev 38 Sprint 79w57 SBOD-2134 Adding Curbside Qty and Curbside Amt
'2021-03-10 jjc Rev 40 Sprint 79w61 SBOD-2154 Adding in Other Discounts.
'2021-04-01 jjc Rev 42 Sprint 79w64 SBOD-2190 Adding 2 years ago
'2021-04-01 jjc Rev 42 Sprint 79w64 SBOD-2190 Adding 2 years ago
'2021-04-12 jjc Rev 43 Sprint 79w65 SBOD-2199 Adding DineIn
'2021-07-02 jjc Rev 43 Sprint 79w65 SBOD-2199 Adding Tool tip for 2 years ago and DineIn
'2025-04-22 jjc Rev 50 Sprint 28 SBOO-204 Adding Round It Up America

Structure FinSumColumn
    Dim nPosition As Integer
    Dim sColName As String
    Dim sToolTip As String
    Dim bVisible As Boolean
    Dim nVisiblePosition As Integer
    Dim bGroupComparable As Boolean
End Structure

Partial Public Class FinancialSummary
    Inherits System.Web.UI.Page

    Const nHiddenColumnsRight As Integer = 4

    Private iCurrentYear As Integer
    Private iCurrentPeriodID As Integer
    Private iCurrentWeekID As Integer

    Private InvWSS As InventoryWebSiteServices.InventoryServices

    Dim sbCSVExport As StringBuilder
    Dim bEmailCSV As Boolean = False

    Dim pdfStream As FileStream
    Dim pdfDoc As Document
    Dim pdfWri As PdfWriter
    Dim font8 As Font
    Dim font8r As Font
    Dim font8g As Font
    Dim iVisibleColCount1 As Integer = 0
    Dim iVisibleColCount2 As Integer = 0
    Dim iVisibleColCount3 As Integer = 0
    Dim pdfTableCols As Integer = 19
    Dim pdfDGTable() As PdfPTable
    Dim pdfDGTable1 As PdfPTable
    Dim pdfDGTable2 As PdfPTable
    Dim pdfDGTable3 As PdfPTable
    Dim sEmailTemplate As String = ""
    Dim sRequestedEmailTemplate As String = ""

    Dim sOrgName As String = ""
    Dim sOrgSQLServer As String = ""
    Dim sOrgSQLDatabase As String = ""
    Dim oColList As System.Collections.Generic.SortedDictionary(Of String, FinSumColumn)
    Dim oVisibleColList As System.Collections.Generic.SortedDictionary(Of Integer, FinSumColumn)
    Friend sTitle As New StringBuilder
    Friend sSubTitle As New StringBuilder

    Enum RowType
        Header = -1
        Store = 0
        District = 1
        Region = 2
        DistrictByRegion = 3
        Franchise = 4
        GrandAverage = 6
        GrandComparable = 7
        GrandNonComparable = 8

        DistrictAverage = 11
        RegionAverage = 12
        DistrictByRegionAverage = 13
    End Enum

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        If Len(Session("UserID")) = 0 Then
            If Len(Request.QueryString("org")) Then
                SetOrgInfo(Request.QueryString("org"))

                If sOrgName = String.Empty Then
                    Response.Redirect("https://sbonet.ncrsaas.com")
                End If
            Else
                Response.Redirect("login.aspx?timeout=1")
            End If
        Else
            If Len(Request.QueryString("org")) Then
                SetOrgInfo(Request.QueryString("org"))
            Else
                SetOrgInfo(Session("OrganizationName"))
                InvWSS = Session("IWSS")
            End If
        End If

        If Not IsPostBack Then
            If Len(Request.QueryString("csv")) AndAlso Request.QueryString("csv") Then
                bEmailCSV = True
                sbCSVExport = New StringBuilder
            End If
            If Len(Request.QueryString("template")) Then
                sRequestedEmailTemplate = Request.QueryString("template")
            End If
            If Len(Request.QueryString("function")) Then
                If Request.QueryString("function") = "mail" Then
                    MailRun()

                    Response.Redirect("https://sbonet.ncrsaas.com")
                    Exit Sub
                End If
            End If
        End If

        LastPeriodDate()

        sTitle.Append("Financial Summary Report")

        If Not IsPostBack Then
            SetGlobalEditPermission()
            PopLayoutTemplates()
            If ddlLayoutTemplate.SelectedValue = String.Empty Then
                PopColumns() 'Enabled/Ordering of Columns
                ApplyOptionPreferences() 'Grouping Checkboxes
            Else
                PopColumns(ddlLayoutTemplate.SelectedValue) 'Enabled/Ordering of Columns
                If ddlLayoutTemplate.SelectedValue.Contains("*") Then
                    ApplyOptionPreferences(ddlLayoutTemplate.SelectedValue, "Default") 'Grouping Checkboxes
                Else
                    ApplyOptionPreferences(ddlLayoutTemplate.SelectedValue) 'Grouping Checkboxes
                End If
            End If

            PopRegions()
            PopDistricts()
            PopStores()

            PopReportingYears()
            PopReportingPeriods()
            PopReportingWeeks()

            ddlDateOption_SelectedIndexChanged()
        Else
            HeaderLinkObjects()

            If Page.Request.Params.Get("__EVENTTARGET") Like "*lbHeader*" Then
                Dim EventTarget As String() = Page.Request.Params.Get("__EVENTTARGET").Split("$")

                Session("SortColumnNum") = EventTarget(EventTarget.Length - 1).Substring(Len("lbHeader"))

                BindGrid()
            End If
            If Page.Request.Params.Get("__EVENTTARGET") Like "*linkVarianceDollars*" Then
                Dim EventTarget As String() = Page.Request.Params.Get("__EVENTTARGET").Split("$")
                Dim nStoreID As Integer

                nStoreID = EventTarget(EventTarget.Length - 1).Substring(Len("linkVarianceDollars"))

                Dim dStartDate As Date
                Dim dEndDate As Date

                Date.TryParse(tbStartDate.Text, dStartDate)
                Date.TryParse(tbEndDate.Text, dEndDate)

                Dim sRedirectPage As New StringBuilder
                sRedirectPage.Append("InventoryDetail.aspx?Redirect=1")

                sRedirectPage.Append("&rptopStore=" & nStoreID.ToString)
                sRedirectPage.Append("&rptopRange=" & ddlDateOption.SelectedItem.Value.ToString)


                Select Case ddlDateOption.SelectedItem.Value
                    Case 1 'Daily
                        sRedirectPage.Append("&rptopStartDate=" & dStartDate.ToString("yyyy-MM-dd"))
                    Case 2 'Weekly
                        sRedirectPage.Append("&rptopYear=" & ddlYear.SelectedValue.ToString)
                        sRedirectPage.Append("&rptopPeriod=" & ddlPeriod.SelectedValue.ToString)
                        sRedirectPage.Append("&rptopWeek=" & ddlWeek.SelectedValue.ToString)
                    Case 3 'Period
                        sRedirectPage.Append("&rptopYear=" & ddlYear.SelectedValue.ToString)
                        sRedirectPage.Append("&rptopPeriod=" & ddlPeriod.SelectedValue.ToString)
                    Case 4 'Range
                        sRedirectPage.Append("&rptopStartDate=" & dStartDate.ToString("yyyy-MM-dd"))
                        sRedirectPage.Append("&rptopEndDate=" & dEndDate.ToString("yyyy-MM-dd"))
                End Select

                Response.Redirect(sRedirectPage.ToString)
            End If
        End If

    End Sub

    Public Sub HeaderLinkObjects()

        If gv1.Rows.Count > 0 Then
            For nCellCount As Integer = 0 To gv1.Rows(0).Cells.Count - 1
                Dim HeaderLink As New LinkButton
                HeaderLink.ID = "lbHeader" + nCellCount.ToString
                HeaderLink.CommandArgument = gv1.Rows(0).Cells(nCellCount).Text
                'HeaderLink.CommandArgument = gv1.Rows(0).Cells(nCellCount).containingField.DataField  'gv1.Rows(0).Cells(nCellCount).Text
                HeaderLink.CommandName = "NavigationLink_Click"
                HeaderLink.Text = gv1.Rows(0).Cells(nCellCount).Text
                gv1.Rows(0).Cells(nCellCount).Controls.Add(HeaderLink)
            Next
        End If

    End Sub

    Sub SetGlobalEditPermission()
        Dim nGlobalTemplateEditRank As Integer = InvWSS.Platform.PersistentValue("InventoryConfig", "GlobalTemplateEditRank")
        cbAllowGlobalEdit.Checked = IIf(Session("UserRank") < nGlobalTemplateEditRank, False, True)
    End Sub

    Sub PopRegions()

        fsRegion.Visible = True

        ddlRegion.Items.Clear()

        Dim tabRegions As DataTable
        Dim li As System.Web.UI.WebControls.ListItem

        tabRegions = InvWSS.GetPermittedRegions()

        'If only one row is returned --> Hide the <asp:DropDownList>
        'Else --> Add an "All Regions" option
        Select Case tabRegions.Rows.Count

            Case 1

                fsRegion.Visible = False

            Case Else

                li = New System.Web.UI.WebControls.ListItem("All Regions")
                li.Value = -1
                ddlRegion.Items.Add(li)

        End Select

        For Each row As DataRow In tabRegions.Rows

            li = New System.Web.UI.WebControls.ListItem(row("Region"))
            li.Value = row("RegionNum")
            ddlRegion.Items.Add(li)

        Next

        tabRegions.Dispose()
        li = Nothing

    End Sub

    Sub PopDistricts()

        fsDistrict.Visible = True

        ddlDistrict.Items.Clear()

        Dim tabDistricts As New DataTable
        Dim li As System.Web.UI.WebControls.ListItem

        If Not ddlRegion.SelectedItem Is Nothing Then

            tabDistricts = InvWSS.GetPermittedDistricts(ddlRegion.SelectedItem.Value)

            'If no rows are returned --> Display a message to the user
            'ElseIf one row is returned --> Hide the <asp:DropDownList>
            'Else --> Add an "All Districts" option
            Select Case tabDistricts.Rows.Count

                Case 0

                    lblDistrict.Text = "No Districts found in the selected Region."
                    lblDistrict.Visible = True
                    tbShowOpts.Text = "True"

                Case 1
                    lblDistrict.Visible = False

                    fsDistrict.Visible = False

                Case Else
                    lblDistrict.Visible = False

                    li = New System.Web.UI.WebControls.ListItem("All Districts")
                    li.Value = -1
                    ddlDistrict.Items.Add(li)

            End Select

            For Each row As DataRow In tabDistricts.Rows

                li = New System.Web.UI.WebControls.ListItem(row("District"))
                li.Value = row("DistrictNum")
                ddlDistrict.Items.Add(li)

            Next

        End If

        tabDistricts.Dispose()
        li = Nothing

    End Sub

    Sub PopStores()

        fsStore.Visible = True
        cblStore.Items.Clear()

        Dim sbStores As New StringBuilder
        Dim tabStores As New DataTable
        Dim li As System.Web.UI.WebControls.ListItem

        With sbStores

            .AppendLine("DECLARE @RegionNum INT")
            .AppendLine("DECLARE @DistrictNum INT")
            .AppendLine("")
            'If a "Region" is selected --> Set @RegionNum Variable
            If ddlRegion.SelectedValue <> "-1" Then
                .AppendLine("SET @RegionNum = " & ddlRegion.SelectedValue)
            End If
            'If a "District" is selected --> Append the WHERE clause
            If ddlDistrict.SelectedValue <> "-1" AndAlso ddlDistrict.SelectedValue <> "" Then
                .AppendLine("SET @DistrictNum = " & ddlDistrict.SelectedValue)
            End If
            .AppendLine("")
            .AppendLine("SELECT")
            .AppendLine("	 tmS.[StoreID]")
            .AppendLine("	,tmS.[StoreDescription]")
            .AppendLine("FROM [dbo].[WEBRPT_vjBinPermissions] AS [vjBP] WITH(NOLOCK)")
            .AppendLine("	INNER JOIN [dbo].[tmStore] AS [tmS] WITH(NOLOCK)")
            .AppendLine("		ON vjBP.[UniversalStoreIdentifier] = tmS.[UniversalStoreIdentifier]")
            .AppendLine("	INNER JOIN [dbo].[tmNode] AS [tmN] WITH(NOLOCK)")
            .AppendLine("		ON tmS.[UniversalNodeIdentifier] = tmN.[UniversalNodeIdentifier]")
            .AppendLine("WHERE vjBP.[BinLevel] = 1")
            .AppendLine("	AND tmN.[ActiveNode] <> 0 ")
            .AppendLine("	AND vjBP.[UniversalAccountIdentifier] = '" & Session("AccountID") & "'")
            .AppendLine("	AND (@RegionNum IS NULL OR tmS.[RegionNum] = @RegionNum)")
            .AppendLine("	AND (@DistrictNum IS NULL OR tmS.[DistrictNum] = @DistrictNum)")
            .AppendLine("ORDER BY tmS.[StoreDescription]")

        End With

        InvWSS.GetData(sbStores.ToString, tabStores)

        If Not IsDBNull(tabStores) AndAlso Not tabStores Is Nothing Then

            'If no rows are returned --> Display a message to the user
            'Else --> Populate the store list
            If tabStores.Rows.Count = 0 Then

                lblStore.Text = "You do not have access to any stores in the selected Region/District."
                lblStore.Visible = True

                tbShowOpts.Text = "True"

            Else
                lblStore.Visible = False

                For Each row As DataRow In tabStores.Rows

                    li = New System.Web.UI.WebControls.ListItem(row("StoreDescription"))
                    li.Value = row("StoreID")
                    cblStore.Items.Add(li)

                Next

                tabStores.Dispose()
                li = Nothing

            End If

        Else

            lblStore.Text = "Error - Store list returned Null/Nothing"
            lblStore.Visible = True

            tbShowOpts.Text = "True"

        End If

    End Sub

    Private Sub PopReportingYears()

        ddlYear.Items.Clear()

        Dim sbYears As New StringBuilder
        Dim tabYears As New DataTable

        With sbYears

            .AppendLine("SELECT")
            .AppendLine("	[ReportingYear]")
            .AppendLine("FROM")
            .AppendLine("	pf_GetReportingYears()")
            .AppendLine("ORDER BY")
            .AppendLine("	[ReportingYear] DESC")

        End With

        InvWSS.GetData(sbYears.ToString(), tabYears)

        If tabYears.Rows.Count > 0 Then

            With ddlYear
                .DataSource = tabYears
                .DataTextField = "ReportingYear"
                .DataValueField = "ReportingYear"
                .DataBind()
            End With

            ddlYear.SelectedValue = iCurrentYear

        End If

    End Sub

    Private Sub PopReportingPeriods()

        ddlPeriod.Items.Clear()

        Dim sbperiods As New StringBuilder
        Dim tabperiods As New DataTable

        With sbperiods

            .AppendLine("select")
            .AppendLine("	 [reportingperiod]")
            .AppendLine("	,format([reportingstartdate], 'MM/dd/yyyy') + ' - ' + format([reportingenddate], 'MM/dd/yyyy') as [periodrange]")
            .AppendLine("from")
            .AppendLine("	pf_getreportingperiodsforyear(" & ddlYear.SelectedItem.Text & ")")

        End With

        InvWSS.GetData(sbperiods.ToString(), tabperiods)

        If tabperiods.Rows.Count > 0 Then

            With ddlPeriod
                .DataSource = tabperiods
                .DataTextField = "periodrange"
                .DataValueField = "reportingperiod"
                .DataBind()
            End With

            'select the current period
            ddlPeriod.SelectedValue = iCurrentPeriodID

        End If

    End Sub

    Private Sub PopReportingWeeks()

        ddlWeek.Items.Clear()

        Dim sbWeeks As New StringBuilder
        Dim tabWeeks As New DataTable

        With sbWeeks

            .AppendLine("SELECT")
            .AppendLine("	 [ReportingWeekNum]")
            .AppendLine("	,FORMAT([ReportingStartDate], 'MM/dd/yyyy') + ' - ' + FORMAT([ReportingEndDate], 'MM/dd/yyyy') AS [PeriodRange]")
            .AppendLine("FROM")
            .AppendLine("	pf_GetReportingWeeksForPeriod(" & ddlYear.SelectedItem.Text & ", " & ddlPeriod.SelectedItem.Value & ")")

        End With

        InvWSS.GetData(sbWeeks.ToString(), tabWeeks)

        If tabWeeks.Rows.Count > 0 Then

            With ddlWeek
                .DataSource = tabWeeks
                .DataTextField = "PeriodRange"
                .DataValueField = "ReportingWeekNum"
                .DataBind()
            End With

            'Loop through each week...
            For Each li As System.Web.UI.WebControls.ListItem In ddlWeek.Items

                'If the value contains the current week's ID --> Select that item
                If ddlYear.SelectedValue = iCurrentYear AndAlso ddlPeriod.SelectedValue = iCurrentPeriodID Then
                    If li.Value = iCurrentWeekID Then
                        li.Selected = True
                    End If
                End If

            Next

        End If

    End Sub

    Sub PopLayoutTemplates()
        Dim sbSQL As New StringBuilder
        Dim tabResults As New DataTable

        With sbSQL
            .AppendLine("SELECT '' AS [Template]")
            .AppendLine("")
            .AppendLine("UNION ALL")
            .AppendLine("")
            .AppendLine("SELECT DISTINCT [Template]")
            .AppendLine("FROM [dbo].[tdUserPreference]")
            .AppendLine(String.Format("WHERE [UniversalAccountIdentifier] IN ('Default', '{0}')", Session("AccountID")))
            .AppendLine("	AND [PageName] = 'FinancialSummary'")
            .AppendLine("	AND [ObjectName] = 'olColumns'")
            .AppendLine("	AND NOT [Template] IS NULL")
            .AppendLine("ORDER BY [Template]")
            .AppendLine("")
        End With

        InvWSS.GetData(sbSQL.ToString(), tabResults)

        ddlLayoutTemplate.Items.Clear()
        ddlLayoutTemplateReportOptions.Items.Clear()

        If tabResults.Rows.Count > 0 Then
            With ddlLayoutTemplate
                .DataSource = tabResults
                .DataValueField = "Template"
                .DataBind()
            End With

            With ddlLayoutTemplateReportOptions
                .DataSource = tabResults
                .DataValueField = "Template"
                .DataBind()
            End With

            With ddlEmailTemplate
                .DataSource = tabResults
                .DataValueField = "Template"
                .DataBind()
            End With

            ApplyLayoutTemplate()
        End If

        tabResults.Dispose()
    End Sub

    Sub DeleteLayoutTemplates(ByVal sLayoutTemplate As String, Optional ByVal sAccountOverRide As String = "")
        Dim sbSQL As New StringBuilder

        sbSQL.AppendLine("DECLARE @UniversalAccountIdentifier VARCHAR(50)")
        sbSQL.AppendLine("DECLARE @LayoutTemplate VARCHAR(50)")
        sbSQL.AppendLine("DECLARE @PageName VARCHAR(50) = 'FinancialSummary'")
        sbSQL.AppendLine("")
        sbSQL.AppendLine(String.Format("SET @UniversalAccountIdentifier = '{0}'", IIf(sAccountOverRide = String.Empty, InvWSS.Platform.Application.AccountIdentifier, sAccountOverRide)))
        If Not sLayoutTemplate = "None" Then
            sbSQL.AppendLine(String.Format("SET @LayoutTemplate = '{0}'", sLayoutTemplate))
        End If
        sbSQL.AppendLine("")
        sbSQL.AppendLine("DELETE")
        sbSQL.AppendLine("FROM [dbo].[tdUserPreference]")
        sbSQL.AppendLine("WHERE [UniversalAccountIdentifier] = @UniversalAccountIdentifier")
        sbSQL.AppendLine("	AND [PageName] = @PageName")
        sbSQL.AppendLine("	AND [Template] = @LayoutTemplate")
        sbSQL.AppendLine("")

        InvWSS.Platform.SBODBExecNonQuerySQL("SBOCore", sbSQL.ToString())
    End Sub

    Sub PopColumns(Optional ByVal slayouttemplate As String = "none")
        Dim sbSQL As New StringBuilder
        Dim tabResults As New DataTable

        With sbSQL
            .AppendLine("DECLARE @UniversalAccountIdentifier VARCHAR(50)")
            .AppendLine("DECLARE @LayoutTemplate VARCHAR(50) = 'None'")
            .AppendLine("SET @UniversalAccountIdentifier = '" & Session("AccountID") & "'")
            .AppendLine("SET @LayoutTemplate = '" & slayouttemplate & "'")
            .AppendLine("")
            .AppendLine("DECLARE @NewTemplate BIT")
            .AppendLine("")
            .AppendLine("SELECT @NewTemplate = CASE WHEN COUNT(*) = 0 THEN 1 ELSE 0 END")
            .AppendLine("FROM [dbo].[tdUserPreference]")
            .AppendLine("WHERE [PageName] = 'FinancialSummary'")
            .AppendLine("	AND")
            .AppendLine("		(")
            .AppendLine("			(   CHARINDEX ('*', @LayoutTemplate) = 0")
            .AppendLine("			AND [UniversalAccountIdentifier] = @UniversalAccountIdentifier")
            .AppendLine("			AND [Template] = @LayoutTemplate")
            .AppendLine("			)")
            .AppendLine("		OR")
            .AppendLine("			(   CHARINDEX ('*', @LayoutTemplate) > 0")
            .AppendLine("			AND [UniversalAccountIdentifier] = 'Default'")
            .AppendLine("			AND [Template] = @LayoutTemplate")
            .AppendLine("			)")
            .AppendLine("		)")
            .AppendLine("")
            .AppendLine("DECLARE @tdUserPreference TABLE")
            .AppendLine("	([UniversalAccountIdentifier] VARCHAR(50) NOT NULL")
            .AppendLine("	,[PageName] VARCHAR(50) NOT NULL")
            .AppendLine("	,[ObjectName] VARCHAR(50) NOT NULL")
            .AppendLine("	,[ObjectType] VARCHAR(50) NOT NULL")
            .AppendLine("	,[SelectedValue] VARCHAR(50) NULL")
            .AppendLine("	,[FriendlyName] VARCHAR(50) NULL")
            .AppendLine("	,[ToolTip] VARCHAR(100) NULL")
            .AppendLine("	,[Value] VARCHAR(MAX) NOT NULL")
            .AppendLine("	,[Template] VARCHAR(50) NULL")
            .AppendLine("	)")
            .AppendLine("")
            .Append(ColumnDefaults())
            .AppendLine("")
            .AppendLine("SELECT")
            .AppendLine("	 @UniversalAccountIdentifier AS [UniversalAccountIdentifier]")
            .AppendLine("	,Base.[PageName]")
            .AppendLine("	,Base.[ObjectName]")
            .AppendLine("	,Base.[ObjectType]")
            .AppendLine("	,Base.[SelectedValue]")
            .AppendLine("	,Base.[FriendlyName]")
            .AppendLine("	,Base.[ToolTip]")
            .AppendLine("	--,Usr.[Template]")
            .AppendLine("	--,ComapnyDefault.[Template]")
            .AppendLine("	--,Base.[Value] AS [Base.Value] ")
            .AppendLine("	--,ComapnyDefault.[Value] AS [ComapnyDefault.Value]")
            .AppendLine("	--,Usr.[Value] AS [Usr.Value]")
            .AppendLine("	--,ISNULL(Usr.[Value], ISNULL(ComapnyDefault.[Value], Base.[Value]))")
            .AppendLine("	--Select the values for the @UniversalAccountIdentifier, if not available fail back first to the [UniversalAccountIdentifier] = 'Default', and finally to the hard coded column layout.")
            .AppendLine("	--In case somehow we end up with duplicates in the ColumnNum, Get the rownumber based on the ABS of the Value, AND the Column Name, then multiply by 1 or -1 based on the Value.")
            .AppendLine("	,(ROW_NUMBER() OVER(PARTITION BY @UniversalAccountIdentifier ORDER BY ABS(ISNULL(Usr.[Value], ISNULL(ComapnyDefault.[Value], Base.[Value]))), Base.[SelectedValue]) -1)")
            .AppendLine("	--	* CASE WHEN ISNULL(Usr.[Value], ISNULL(ComapnyDefault.[Value], Base.[Value])) < 0 THEN -1 ELSE 1 END AS [Value]")
            .AppendLine("		*")
            .AppendLine("			CASE WHEN ComapnyDefault.[SelectedValue] IS NULL AND Usr.[SelectedValue] IS NULL THEN --Column is new ")
            .AppendLine("				CASE WHEN ISNULL(@LayoutTemplate, 'None') = 'None' OR @NewTemplate = 1")
            .AppendLine("					THEN 1 --If we're not using a template, or using a new template, show new columns")
            .AppendLine("					ELSE -1 --If we are using a template, don't show new columns")
            .AppendLine("				END")
            .AppendLine("			ELSE --Column is not new, Look to the Config")
            .AppendLine("				CASE WHEN ISNULL(Usr.[Value], ISNULL(ComapnyDefault.[Value], Base.[Value])) < 0")
            .AppendLine("					THEN -1")
            .AppendLine("					ELSE 1")
            .AppendLine("				END")
            .AppendLine("			END")
            .AppendLine("	AS [Value]")
            .AppendLine("	,(ROW_NUMBER() OVER(PARTITION BY @UniversalAccountIdentifier ORDER BY ABS(ISNULL(Usr.[Value], ISNULL(ComapnyDefault.[Value], Base.[Value]))), Base.[SelectedValue]) -1) AS [AbsOrder]")
            .AppendLine("	,CASE WHEN ComapnyDefault.[SelectedValue] IS NULL AND Usr.[SelectedValue] IS NULL THEN --Column is new ")
            .AppendLine("		CASE WHEN ISNULL(@LayoutTemplate, 'None') = 'None' OR @NewTemplate = 1")
            .AppendLine("			THEN CAST(1 AS BIT) --If we're not using a template, or using a new template, show new columns")
            .AppendLine("			ELSE CAST(0 AS BIT) --If we are using a template, don't show new columns")
            .AppendLine("		END")
            .AppendLine("	ELSE --Column is not new, Look to the Config")
            .AppendLine("		CASE WHEN ISNULL(Usr.[Value], ISNULL(ComapnyDefault.[Value], Base.[Value])) < 0")
            .AppendLine("			THEN CAST(0 AS BIT)")
            .AppendLine("			ELSE CAST(1 AS BIT)")
            .AppendLine("		END")
            .AppendLine("	END AS [Visible]")
            .AppendLine("FROM @tdUserPreference AS [Base]")
            .AppendLine("	LEFT OUTER JOIN [dbo].[tdUserPreference] AS [ComapnyDefault]")
            .AppendLine("		ON ComapnyDefault.[UniversalAccountIdentifier] = 'Default'")
            .AppendLine("			AND Base.[PageName] = ComapnyDefault.[PageName]")
            .AppendLine("			AND Base.[ObjectName] = ComapnyDefault.[ObjectName]")
            .AppendLine("			AND Base.[ObjectType] = ComapnyDefault.[ObjectType]")
            .AppendLine("			AND (Base.[SelectedValue] = ComapnyDefault.[SelectedValue] OR (Base.[SelectedValue] IS NULL AND ComapnyDefault.[SelectedValue] IS NULL))")
            .AppendLine("			AND ISNULL(ComapnyDefault.[Template], 'None') = @LayoutTemplate") 'jjc PutBack
            .AppendLine("	LEFT OUTER JOIN [dbo].[tdUserPreference] AS [Usr]")
            .AppendLine("		ON Usr.[UniversalAccountIdentifier] = @UniversalAccountIdentifier")
            .AppendLine("			AND Base.[PageName] = Usr.[PageName]")
            .AppendLine("			AND Base.[ObjectName] = Usr.[ObjectName]")
            .AppendLine("			AND Base.[ObjectType] = Usr.[ObjectType]")
            .AppendLine("			AND (Base.[SelectedValue] = Usr.[SelectedValue] OR (Base.[SelectedValue] IS NULL AND Usr.[SelectedValue] IS NULL))")
            .AppendLine("			AND ISNULL(Usr.[Template], 'None') = @LayoutTemplate") 'jjc PutBack
            .AppendLine("ORDER BY")
            .AppendLine("	 Base.[ObjectType]")
            .AppendLine("	,Base.[ObjectName]")
            .AppendLine("	,ABS(ISNULL(Usr.[Value], ISNULL(ComapnyDefault.[Value], Base.[Value])))")
        End With

        InvWSS.GetData(sbSQL.ToString(), tabResults)

        If tabResults.Rows.Count > 0 Then
            SetVisibleLists(tabResults)
        End If

        Dim dvColumnsFiltered As New DataView(tabResults, "SelectedValue <> 'StoreDescription'", "AbsOrder", DataViewRowState.CurrentRows)

        grdColumns.DataSource = dvColumnsFiltered

        Session("TempTable") = tabResults

        grdColumns.DataBind()

        tabResults.Dispose()
    End Sub

    Private Sub SetVisibleLists(ByVal dtSessionTable As DataTable)
        'Dim SessionTable As New DataTable
        'SessionTable = Session("TempTable")


        oColList = New SortedDictionary(Of String, FinSumColumn)
        oVisibleColList = New SortedDictionary(Of Integer, FinSumColumn)

        Dim nVisibleCounter As Integer = 0

        Dim dvColumnsOrdered As New DataView(dtSessionTable, "", "AbsOrder", DataViewRowState.CurrentRows)
        'For Each drColumns As DataRow In dtSessionTable.Rows
        For Each drColumns As DataRowView In dvColumnsOrdered
            Dim oFinSumColumn As New FinSumColumn
            oFinSumColumn.nPosition = Math.Abs(drColumns("Value"))
            oFinSumColumn.sColName = drColumns("SelectedValue")
            oFinSumColumn.sToolTip = drColumns("ToolTip")
            If drColumns("Value") >= 0 Then
                oFinSumColumn.nVisiblePosition = nVisibleCounter
                oFinSumColumn.bVisible = True

                Select Case oFinSumColumn.sColName
                    Case "NetSales_YrVar", "NetSales_YrVarPct", "TransactionYrVar", "TransactionYrVarPct", "AvgCheckLY", "NetSalesLY", "TransactionLY"
                        oFinSumColumn.bGroupComparable = True
                End Select


                oVisibleColList.Add(nVisibleCounter, oFinSumColumn)

                nVisibleCounter += 1
            Else
                oFinSumColumn.nVisiblePosition = -1
                oFinSumColumn.bVisible = False
            End If

            oColList.Add(oFinSumColumn.sColName, oFinSumColumn)
        Next

        Dim dvColumnsFiltered As New DataView(dtSessionTable, "SelectedValue <> 'StoreDescription'", "AbsOrder", DataViewRowState.CurrentRows)

        grdColumns.DataSource = dvColumnsFiltered

        grdColumns.DataBind()

    End Sub


    Private Function ColumnDefaults() As String
        Dim sbSQL As New StringBuilder

        With sbSQL
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'StoreDescription', 'StoreDescription', 'Store Name', 0)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'GrossSales', 'Gross Sales', 'The value of sales with coupons & discounts', 1)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'NetSales', 'Net Sales', 'The value of sales after coupons & discounts', 2)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'NetSales_WkVar', '+/- Last Week', 'Net Sales Compared to Last Weeks Net Sales', 3)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'NetSales_YrVar', '+/- Last Year', 'Net Sales Compared to Last Years Net Sales', 4)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'NetSales_YrVarPct', '+/- Last Year %', 'Net Sales Percentage Compared to Last Years Net Sales', 5)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'NetSales_ProjVar', '+/- Projection', 'Gross Sales Compared to Projected Sales entered in StoreAndforward', 6)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'LaborCrewHrs', 'Crew Hours', 'Total hours of employees that punched into the POS System', 7)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'LaborPct', 'Crew Labor %', 'Timeclock Punch Cost', 8)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'LaborMgmntHrs', 'Management Hours', 'Entered in StoreAndforward', 9)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'LaborTotalHrs', 'Total Hours', 'Timeclock Punch Hours + Mgr Hours from StoreAndforward', 10)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'LaborTrainingHrs', 'Training Hours', 'Entered in StoreAndforward', 11)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'SalesPerManHour', 'Sales Per Man Hour', 'Sales Per Man Hour', 12)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'TotalLaborPct', 'Total Labor %', 'Timeclock Punch Cost + (Mgr Hours from StoreAndforward * Avg Mgr Rate in Storeandforward)', 13)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'LaborGuideVarHrs', '+/- Guide', 'Guide From StoreAndforward - (Timeclock Punch Hours + Mgr Hours from StoreAndforward)', 14)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'LaborGuideVarWTDHrs', 'WTD +/- Guide', 'Guide: Guide From StoreAndforward - (Timeclock Punch Hours + Mgr Hours from StoreAndforward)', 15)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'PaidOut', 'Paid Out', 'POS System Cash Tendered - Total Deposits Entered into the POS System', 16)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'CashVar', 'Cash +/-', 'POS System Cash Tendered - Total Deposits Entered into the POS System', 17)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'PUW_Pct', 'PUW %', 'Net Sales at the Drive Thru (Pickupwindow) Destination / Net Sales', 18)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'BreakfastSales', 'Breakfast Sales', 'Sales from open to 10 AM (Unless configured otherwise)', 19)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'BreakfastPct', 'Breakfast Sales %', 'Sales from open to 10 AM (Unless configured otherwise)', 20)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'Sales_Late', 'Late Night Sales', 'Sales from 10 PM to Close (Unless configured otherwise)', 21)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'Sales_Late_Pct', 'Late Night Sales %', 'Sales % from 10 PM to Close (Unless configured otherwise)', 22)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'EmpMealsCount', 'Emp. Meals Qty.', 'Number of transactions associated with Employee Meals', 23)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'EmpMeals', 'Emp. Meals Amt.', 'Net Sales associated with transactions associated with Employee Meals', 24)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'EmpMealsPct', 'Emp. Meals %', 'Net Sales % associated with transactions associated with Employee Meals', 25)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'MgrMealsCount', 'Mgr. Meals Qty.', 'Number of transactions associated with Manager Meals', 26)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'MgrMeals', 'Mgr. Meals Amt.', 'Net Sales associated with transactions associated with Manager Meals', 27)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'MgrMealsPct', 'Mgr. Meals %', 'Net Sales associated with transactions associated with Manager Meals', 28)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'Coupons', 'Coupons', 'Transaction Totals that were couponed', 29)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'CouponsPct', 'Coupons %', 'Percent of Transaction Totals that were couponed', 30)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'SeniorCitizenDiscounts', 'Senior Disc.', 'Net Sales associated with transactions associated with Senior Discounts', 31)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'SeniorCitizenDiscountsPct', 'Senior Disc. %', 'Net Sales % associated with transactions associated with Senior Discounts', 32)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'ErrorCorrectCount', 'Error Correct Qty.', 'Quantity of items that were rung in, then immediately voided', 33)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'ErrorCorrect', 'Error Correct Amt.', 'Net Sales of items that were rung in, then immediately voided', 34)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'ErrorCorrectPct', 'Error Correct %', 'Net Sales % of items that were rung in, then immediately voided', 35)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'OrderVoidCount', 'Order Void Qty.', 'Quantity of items that were rung in, then voided later in the transaction', 36)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'OrderVoid', 'Order Void Amt.', 'Net Sales of items that were rung in, then voided later in the transaction', 37)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'OrderVoidPct', 'Order Void %', 'Net Sales % of items that were rung in, then voided later in the transaction', 38)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'DeletesAllVoidsCount', 'Deletes/All Voids Count', 'Count of Deletes and Voids', 39)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'DeletesAllVoidsAmount', 'Deletes/All Voids Amount', 'Postive Amount of Deletes/All Voids.', 40)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'DeletesAllVoidsPct', 'Deletes/All Voids %', '% of Deletes and Voids', 41)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'VarianceDollars', 'Invt. Variance', 'Dollars cost of ingredients actual usage daily versus the theorectical usage', 42)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'NetProfit', 'Net Profit', 'Net Profit', 43)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'SOSAverage', 'SOS Average', 'Average Time for all Drive Thru Customers( Sum of menuboard, queue and window times)', 44)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'TransactionCount', 'Trans. Count', 'Transaction Count', 45)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'TPMH', 'TPMH', 'Transaction Count', 46)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'TransactionAverage', 'Trans. Avg.', 'Transaction Average', 47)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'TransactionYrVar', 'Trans. +/- Last Year', 'Transactions +/- Last Year', 48)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'TransactionYrVarPct', 'Trans. +/- Last Year %', 'Transactions +/- Last Year %', 49)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'TotalWaste', 'Total Waste', 'Total Waste', 50)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'AvgCheckLY', 'Avg. Check Last Year', 'Avg Check Last Year', 51)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'Charge', 'Credit Card $', 'Credit Card $', 52)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'ChargePct', 'Credit Card %', 'Credit Card %', 53)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'ProjectedCPR', 'Projected CPR', 'Projected CPR', 54)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'NetSalesLY', 'LY Sales', 'The value of last year sales after coupons & discounts', 55)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'TransactionLY', 'LY Trans. Count', 'Last Year transaction count', 56)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'MobilePay', 'Mobile Pay', 'Mobile Pay', 57)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'ScheduledHours', 'Total Scheduled Hours', 'Scheduled Hours', 58)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'ScheduledHoursVar', '+/- Scheduled Hours', '+/- Scheduled Hours', 59)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'NetSales_WkVarPct', '+/- Last Week %', '+/- Last Week %', 60)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'Projections', 'Projections', 'Projections', 61)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'LaborCost', 'LaborCost', 'Labor Cost', 62)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'TheoreticalPct', 'Theo Pct', 'Theoretical Food Cost Percent', 63)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'ActualPct', 'Act Pct', 'Actual Food Cost Percent', 64)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'VarPct', 'Var Pct', 'Food Cost Variance Percent', 65)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'LaborOTHrs', 'Labor OT Hours', 'Labor Overtime Hours', 66)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'LaborGuideHours', 'Labor Guide Hours', 'Labor Guide Hours', 67)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'BudgetLaborPct', 'Budget Labor %', 'Budget Labor Percent', 68)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'KioskCount', 'Kiosk Qty.', 'Quantity of kiosk orders', 69)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'Kiosk', 'Kiosk Amt.', 'Kiosk Sales', 70)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'KioskPct', 'Kiosk %', 'Kiosk Percent', 71)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'Tax', 'Tax', 'Tax', 72)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'GiftCardSold', 'Gift Card Sold', 'Gift Card Sold', 73)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'GiftCardRedeem', 'Gift Card Redeem', 'Gift Card Redeem', 74)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'CashToCount', 'Cash To Count', 'Cash To Count', 75)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'MobileOdr', 'Mobile Order', 'Mobile Order', 76)")

            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'OtherHours', 'Other Hours', 'Other Hours', 77)")

            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'CashVarPct', 'Cash +/- (%)', 'Cash +/- (%)', 78)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'MealReplacementCount', 'Meal Replacement Count', 'Meal Replacement Count', 79)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'MealReplacement', 'Meal Replacement', 'Meal Replacement', 80)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'MealReplacementPct', 'Meal Replacement %', 'Meal Replacement %', 81)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'Surcharge', 'Surcharge', 'Surcharge', 82)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'Delivery', 'Delivery', 'Delivery', 83)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'BreakfastSales_YrVar', 'Breakfast +/- Last Year', 'Breakfast Sales Compared to Last Years Breakfast Sales', 84)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'BreakfastSales_YrVarPct', 'Breakfast +/- Last Year %', 'Breakfast Sales Percentage Compared to Last Years Breakfast Sales', 85)")

            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'MblReward', 'Mobile Reward', 'Mobile Reward', 86)")

            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'CurbsideCount', 'Curbside Qty', 'Curbside Qty', 87)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'Curbside', 'Curbside Amt', 'Curbside Amt', 88)")

            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'OtherDiscounts', 'Other Disc', 'Other Disc', 89)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'OtherDiscountsPct', 'Other Disc %', 'Other Disc %', 90)")

            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'NetSales_2YAVar', '+/- 2 Year Ago', '+/- 2 Year Ago', 91)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'NetSales_2YAVarPct', '+/- 2 Year Ago %', '+/- 2 Year Ago %', 92)")

            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'DineIn', 'Dine In', 'Dine In', 93)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'DineInPct', 'Dine In %', 'Dine In %', 94)")

            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'DigitalSales', 'Digital Sales', 'Digital Sales', 95)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'DigitalSalesPct', 'Digital Sales %', 'Digital Sales %', 96)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'SOSDP1', 'SOS DP1', 'SOS DP1', 97)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'SOSDP2', 'SOS DP2', 'SOS DP2', 98)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'SOSDP3', 'SOS DP3', 'SOS DP3', 99)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'SOSDP4', 'SOS DP4', 'SOS DP4', 100)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'SOSDP5', 'SOS DP5', 'SOS DP5', 101)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'SOSDP6', 'SOS DP6', 'SOS DP6', 102)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'CarsDP1', 'Cars DP1', 'Cars DP1', 103)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'CarsDP2', 'Cars DP2', 'Cars DP2', 104)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'CarsDP3', 'Cars DP3', 'Cars DP3', 105)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'CarsDP4', 'Cars DP4', 'Cars DP4', 106)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'CarsDP5', 'Cars DP5', 'Cars DP5', 107)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'CarsDP6', 'Cars DP6', 'Cars DP6', 108)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'BiggieBagPct', 'Biggie Bag %', 'Biggie Bag %', 109)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', '444Pct', '444 %', '444 %', 110)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'UpsizeLargePct', 'Upsize to L %', 'Upsize to L %', 111)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'UpsizeMedLargePct', 'Upsize to M/L %', 'Upsize to M/L %', 112)")

            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'MobileOdrPct', 'Mobile Order %', 'Mobile Order %', 113)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'DeliveryPct', 'Delivery %', 'Delivery %', 114)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'PUW', 'PUW $', 'PUW $', 115)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'PUWTrans', 'PUW Trans', 'PUW Trans', 116)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'LunchLYVar', 'Lunch +/- Last Year', 'Lunch +/- Last Year', 117)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'LunchLYVarPct', 'Lunch +/- Last Year %', 'Lunch +/- Last Year %', 118)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'AfternoonLYVar', 'Afternoon +/- Last Year', 'Afternoon +/- Last Year', 119)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'AfternoonLYVarPct', 'Afternoon +/- Last Year %', 'Afternoon +/- Last Year %', 120)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'DinnerLYVar', 'Dinner +/- Last Year', 'Dinner +/- Last Year', 121)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'DinnerLYVarPct', 'Dinner +/- Last Year %', 'Dinner +/- Last Year %', 122)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'PMSnackLYVar', 'PM Snack +/- Last Year', 'PM Snack +/- Last Year', 123)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'PMSnackLYVarPct', 'PM Snack +/- Last Year %', 'PM Snack +/- Last Year %', 124)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'LateNightLYVar', 'Late Night +/- Last Year', 'Late Night +/- Last Year', 125)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'LateNightLYPct', 'Late Night +/- Last Year %', 'Late Night +/- Last Year %', 126)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'KioskInsidePct', 'Kiosk % of DR Sales', 'Kiosk % of DR Sales', 127)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'DigitalSalesCount', 'Digital Sales Count', 'Digital Sales Count', 128)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'MobileOrderCount', 'Mobile Order Count', 'Mobile Order Count', 129)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'DeliveryCount', 'Delivery Count', 'Delivery Count', 130)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'RoundItUpAmerica', 'Round It Up America', 'Round It Up America', 131)")

            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'PUWAvgCheck', 'PUW Avg Check', 'PUW Avg Check', 132)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'MobilePUW', 'Mobile PUW $', 'Mobile PUW $', 133)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'MobilePUWTrans', 'Mobile PUW Trans', 'Mobile PUW Trans', 134)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'MobilePUWPct', 'Mobile PUW %', 'Mobile PUW %', 135)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'MobilePUWAvgCheck', 'Mobile PUW Avg Check', 'Mobile PUW Avg Check', 136)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'AIPct', '% AI', '% AI', 137)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'AiPUW', 'AI Sales Amount', 'AI Sales Amount', 138)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'AiPUWTrans', 'AI Check Ct', 'AI Check Ct', 139)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'AiPUWPct', '% AI PUW', '% AI PUW', 140)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'AiPUWAvgCheck', 'AI PUW Avg Check', 'AI PUW Avg Check', 141)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'AiPUWLYVar', 'AI +/- LY', 'AI +/- LY', 142)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'AiPUWPartial', 'AI Partial Sales Amount', 'AI Partial Sales Amount', 143)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'AiPUWPartialTrans', 'AI Partial Check Ct', 'AI Partial Check Ct', 144)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'AiPUWPartialPct', '% AI Partial PUW', '% AI Partial PUW', 145)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'AiPUWPartialAvgCheck', 'AI PUW Partial Avg Check', 'AI PUW Partial Avg Check', 146)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'AiPUWPartialLYVar', 'AI Partial +/- LY', 'AI Partial +/- LY', 147)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'AiVoid', 'AI Void Amt', 'AI Void Amt', 148)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'AiVoidTrans', 'AI Void Ct', 'AI Void Ct', 149)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'AiVoidTransPct', 'AI Void %', 'AI Void %', 150)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'AiPartialVoid', 'Partial AI Void Amt', 'Partial AI Void Amt', 151)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'AiPartialVoidTrans', 'Partial AI Void %', 'Partial AI Void %', 152)")
            .AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', 'AiPartialVoidPct', 'Partial AI Void Ct', 'Partial AI Void Ct', 153)")
            '.AppendLine("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[FriendlyName],[ToolTip],[Value]) VALUES (@UniversalAccountIdentifier, 'FinancialSummary', 'olColumns', 'ol', '', '', '', 154)")

        End With

        Return sbSQL.ToString
    End Function

    Public Sub txtTemplateSave_TextChanged(sender As Object, e As EventArgs) Handles txtTemplateSave.TextChanged
        Dim _txtTemplateName As TextBox = TryCast(sender, TextBox)
        Dim sTemplateName As String = _txtTemplateName.Text
        _txtTemplateName.Text = String.Empty

        SetOrder()
        If sTemplateName.Contains("*") Then
            SetUserPreferences(sTemplateName, "Default") 'Enabled/Ordering of Columns
        Else
            SetUserPreferences(sTemplateName) 'Enabled/Ordering of Columns
        End If

        PopLayoutTemplates()

        PopColumns(sTemplateName)

        'If ddlLayoutTemplate.SelectedValue = String.Empty Then
        '    PopColumns()
        'Else
        '    PopColumns(ddlLayoutTemplate.SelectedValue)
        'End If
    End Sub

    Public Sub txtTemplateDelete_TextChanged(sender As Object, e As EventArgs) Handles txtTemplateDelete.TextChanged
        Dim _txtTemplateName As TextBox = TryCast(sender, TextBox)
        Dim sTemplateName As String = _txtTemplateName.Text
        _txtTemplateName.Text = String.Empty

        If sTemplateName.Contains("*") Then
            'MsgBox("Delete Global Template: " + sTemplateName)
            DeleteLayoutTemplates(sTemplateName, "Default")
        Else
            'MsgBox("Delete User Template: " + sTemplateName)
            DeleteLayoutTemplates(sTemplateName)
        End If

        ddlLayoutTemplate.SelectedValue = String.Empty

        SetUserPreferences(bSelectedTemplateOnly:=True)

        PopLayoutTemplates()
    End Sub

    Private Sub SetUserPreferences(Optional ByVal sLayoutTemplate As String = "None", Optional ByVal sAccountOverRide As String = "", Optional ByVal bSelectedTemplateOnly As Boolean = False, Optional ByVal sPreferences As String = "")
        Dim cmdUserPreferences As New SqlCommand
        Dim sbSQL As New StringBuilder

        Try
            With sbSQL
                Dim SessionTable As New DataTable
                SessionTable = Session("TempTable")

                If Not SessionTable Is Nothing Then
                    .AppendLine("DECLARE @UniversalAccountIdentifier VARCHAR(50)")
                    .AppendLine("DECLARE @LayoutTemplate VARCHAR(50)")
                    .AppendLine("DECLARE @PageName VARCHAR(50) = 'FinancialSummary'")
                    .AppendLine("")
                    .AppendLine(String.Format("SET @UniversalAccountIdentifier = '{0}'", IIf(sAccountOverRide = String.Empty, InvWSS.Platform.Application.AccountIdentifier, sAccountOverRide)))
                    If Not sLayoutTemplate = "None" Then
                        .AppendLine(String.Format("SET @LayoutTemplate = '{0}'", sLayoutTemplate))
                    End If
                    .AppendLine("")
                    .AppendLine("DECLARE @tdUserPreference TABLE")
                    .AppendLine("	([UniversalAccountIdentifier] VARCHAR(50) NOT NULL")
                    .AppendLine("	,[ObjectName] VARCHAR(50) NOT NULL")
                    .AppendLine("	,[ObjectType] VARCHAR(50) NOT NULL")
                    .AppendLine("	,[SelectedValue] VARCHAR(50) NULL")
                    .AppendLine("	,[Value] VARCHAR(MAX) NOT NULL")
                    .AppendLine("	,[Template] VARCHAR(50) NULL")
                    .AppendLine("	)")
                    .AppendLine("")
                    '.Appendline(string.format("{0}", ""))

                    If sPreferences = String.Empty Then
                        If Not bSelectedTemplateOnly Then
                            For Each row As DataRow In SessionTable.Rows
                                AppendUserPreferenceLine(sbSQL, "olColumns", "ol", row("SelectedValue"), row("Value"), "@LayoutTemplate")
                            Next

                            AppendUserPreferenceLine(sbSQL, cbGroupRegion.ID, "chk", Nothing, IIf(cbGroupRegion.Checked, "True", "False"), "@LayoutTemplate")
                            AppendUserPreferenceLine(sbSQL, cbGroupDistrict.ID, "chk", Nothing, IIf(cbGroupDistrict.Checked, "True", "False"), "@LayoutTemplate")
                            AppendUserPreferenceLine(sbSQL, cbFloatingHeader.ID, "chk", Nothing, IIf(cbFloatingHeader.Checked, "True", "False"), "@LayoutTemplate")
                            .AppendLine("")
                        End If

                        If Not sLayoutTemplate = "None" Then
                            AppendUserPreferenceLine(sbSQL, ddlLayoutTemplate.ID, "ddl", Nothing, sLayoutTemplate, "NULL")
                        ElseIf ddlLayoutTemplate.SelectedValue = String.Empty Then
                            AppendUserPreferenceLine(sbSQL, ddlLayoutTemplate.ID, "ddl", Nothing, Nothing, "NULL")
                        Else
                            AppendUserPreferenceLine(sbSQL, ddlLayoutTemplate.ID, "ddl", Nothing, ddlLayoutTemplate.SelectedValue, "NULL")
                        End If
                    Else
                        .AppendLine(sPreferences)
                    End If
                End If

                .AppendLine("")
                .AppendLine("")
                .AppendLine("	MERGE [dbo].[tdUserPreference] AS TARGET")
                .AppendLine("	USING")
                .AppendLine("		/*")
                .AppendLine("		SELECT")
                .AppendLine("			 TARGET.[UniversalAccountIdentifier]")
                .AppendLine("			,TARGET.[PageName]")
                .AppendLine("			,TARGET.[ObjectName]")
                .AppendLine("			,TARGET.[ObjectType]")
                .AppendLine("			,TARGET.[SelectedValue]")
                .AppendLine("			,TARGET.[Value]")
                .AppendLine("			,SOURCE.*")
                .AppendLine("		FROM [dbo].[tdUserPreference] AS [TARGET] RIGHT OUTER JOIN")
                .AppendLine("		--*/")
                .AppendLine("		(")
                .AppendLine("		SELECT")
                .AppendLine("			 [UniversalAccountIdentifier]")
                .AppendLine("			,@PageName AS [PageName]")
                .AppendLine("			,[ObjectName]")
                .AppendLine("			,[ObjectType]")
                .AppendLine("			,[SelectedValue]")
                .AppendLine("			,[Value]")
                .AppendLine("			,[Template]")
                .AppendLine("		FROM @tdUserPreference")
                .AppendLine("		) AS SOURCE")
                .AppendLine("			ON TARGET.[UniversalAccountIdentifier] = SOURCE.[UniversalAccountIdentifier]")
                .AppendLine("				AND TARGET.[PageName] = SOURCE.[PageName]")
                .AppendLine("				AND TARGET.[ObjectName] = SOURCE.[ObjectName]")
                .AppendLine("				AND TARGET.[ObjectType] = SOURCE.[ObjectType]")
                .AppendLine("				AND (TARGET.[SelectedValue] = SOURCE.[SelectedValue] OR (TARGET.[SelectedValue] IS NULL AND SOURCE.[SelectedValue] IS NULL))")
                .AppendLine("				AND (TARGET.[Template] = SOURCE.[Template] OR (TARGET.[Template] IS NULL AND SOURCE.[Template] IS NULL))")
                .AppendLine("	WHEN MATCHED THEN")
                .AppendLine("		UPDATE SET")
                .AppendLine("			[Value] = SOURCE.[Value]")
                .AppendLine("	WHEN NOT MATCHED THEN")
                .AppendLine("	INSERT")
                .AppendLine("		([UniversalAccountIdentifier]")
                .AppendLine("		,[PageName]")
                .AppendLine("		,[ObjectName]")
                .AppendLine("		,[ObjectType]")
                .AppendLine("		,[SelectedValue]")
                .AppendLine("		,[Value]")
                .AppendLine("		,[Template]")
                .AppendLine("		)")
                .AppendLine("	VALUES")
                .AppendLine("		(SOURCE.[UniversalAccountIdentifier]")
                .AppendLine("		,SOURCE.[PageName]")
                .AppendLine("		,SOURCE.[ObjectName]")
                .AppendLine("		,SOURCE.[ObjectType]")
                .AppendLine("		,SOURCE.[SelectedValue]")
                .AppendLine("		,SOURCE.[Value]")
                .AppendLine("		,SOURCE.[Template]")
                .AppendLine("		)")
                .AppendLine("	;")
                .AppendLine("")
            End With

            InvWSS.Platform.SBODBExecNonQuerySQL("SBOCore", sbSQL.ToString())

            'MsgBox(sbSQL.ToString())
        Catch ex As Exception
            Throw ex
        End Try
    End Sub

    Private Sub AppendUserPreferenceLine(ByRef rsbSQL As StringBuilder, ByVal sObjectName As String, ByVal sObjectType As String, ByVal sSelectedValue As String, ByVal sValue As String, ByVal sLayoutTemplate As String)
        'rsbSQL.AppendLine(String.Format("INSERT @tdUserPreference ([UniversalAccountIdentifier],[PageName],[ObjectName],[ObjectType],[SelectedValue],[Value]) VALUES (@UniversalAccountIdentifier, @PageName, '{0}', '{1}', '{2}', '{3}')", sObjectName, sObjectType, sSelectedValue, sValue))
        rsbSQL.Append("INSERT @tdUserPreference ([UniversalAccountIdentifier],[ObjectName],[ObjectType],[SelectedValue],[Value],[Template]) VALUES")

        If sObjectName = "ddlLayoutTemplate" Then
            rsbSQL.Append(String.Format("('{0}'", InvWSS.Platform.Application.AccountIdentifier))
        Else
            rsbSQL.Append("(@UniversalAccountIdentifier")
        End If
        rsbSQL.Append(String.Format(", '{0}'", sObjectName))
        rsbSQL.Append(String.Format(", '{0}'", sObjectType))
        If sSelectedValue Is Nothing Then
            rsbSQL.Append(", NULL")
        Else
            rsbSQL.Append(String.Format(", '{0}'", sSelectedValue))
        End If
        rsbSQL.Append(String.Format(", '{0}'", sValue))
        rsbSQL.Append(String.Format(", {0}", sLayoutTemplate))
        rsbSQL.AppendLine(")")
    End Sub


    Private Sub ApplyLayoutTemplate()
        Dim sbSQL As StringBuilder
        Dim dtTemplate As DataTable
        Dim dtEmailActive As DataTable

        sbSQL = New StringBuilder
        sbSQL.AppendLine("SELECT")
        sbSQL.AppendLine("	 [ddlLayoutTemplate]")
        sbSQL.AppendLine("	,[ddlEmailTemplate]")
        sbSQL.AppendLine("FROM")
        sbSQL.AppendLine("	(")
        sbSQL.AppendLine("	SELECT")
        sbSQL.AppendLine("		 [ObjectName]")
        sbSQL.AppendLine("		,[Value]")
        sbSQL.AppendLine("	FROM [dbo].[tdUserPreference] WITH(NOLOCK)")
        sbSQL.AppendLine("	WHERE [UniversalAccountIdentifier] = '" & InvWSS.Platform.Application.AccountIdentifier & "'")
        sbSQL.AppendLine("		AND [PageName] = 'FinancialSummary'")
        sbSQL.AppendLine("		AND [ObjectType] = 'ddl'")
        sbSQL.AppendLine("	) AS [DAT]")
        sbSQL.AppendLine("	PIVOT")
        sbSQL.AppendLine("	(")
        sbSQL.AppendLine("	MAX(DAT.[VALUE])")
        sbSQL.AppendLine("	FOR DAT.[ObjectName] IN")
        sbSQL.AppendLine("		([ddlLayoutTemplate]")
        sbSQL.AppendLine("		,[ddlEmailTemplate]")
        sbSQL.AppendLine("		)")
        sbSQL.AppendLine("	) AS [PVT]")

        dtTemplate = InvWSS.Platform.SBODBExecQuerySQL("SBOCore", sbSQL.ToString())


        sbSQL = New StringBuilder
        sbSQL.AppendLine("SELECT")
        sbSQL.AppendLine("	 tmEM.[EmailMessageID]")
        sbSQL.AppendLine("	,tmEM.[Title]")
        sbSQL.AppendLine("	,tdEM.[UniversalAccountIdentifier]")
        sbSQL.AppendLine("	,tmA.[EmailAddress]")
        sbSQL.AppendLine("	,tmA.[ActiveAccount]")
        sbSQL.AppendLine("FROM [dbo].[tmEmailMessages] AS [tmEM]")
        sbSQL.AppendLine("	INNER JOIN [dbo].[tdEmailMessages] AS [tdEM]")
        sbSQL.AppendLine("		ON tmEM.[EmailMessageID] = tdEM.[EmailMessageID]")
        sbSQL.AppendLine("	INNER JOIN [dbo].[tdEmailFrequency] AS [tdEF]")
        sbSQL.AppendLine("		ON tmEM.[EmailMessageID] = tdEF.[EmailMessageID]")
        sbSQL.AppendLine("	INNER JOIN [dbo].[tmAccount] AS [tmA]")
        sbSQL.AppendLine("		ON tdEM.[UniversalAccountIdentifier] = tmA.[UniversalAccountIdentifier]")
        sbSQL.AppendLine("WHERE tmEM.[Title] IN  ('Financial Summary', 'Financial Summary - CSV')")
        sbSQL.AppendLine("	AND tdEM.[UniversalAccountIdentifier] = '" & InvWSS.Platform.Application.AccountIdentifier & "'")
        sbSQL.AppendLine("	AND tmA.[ActiveAccount] <> 0")
        sbSQL.AppendLine("	AND tmA.[EmailAddress] IS NOT NULL")
        sbSQL.AppendLine("	AND tmA.[EmailAddress] <> ''")
        sbSQL.AppendLine("	AND tdEF.[Active] = 1")

        dtEmailActive = InvWSS.Platform.SBODBExecQuerySQL("SBOCore", sbSQL.ToString())


        Dim sFormatTemplate As String
        If Not dtTemplate Is Nothing AndAlso dtTemplate.Rows.Count > 0 Then
            sFormatTemplate = dtTemplate.Rows(0).Item("ddlLayoutTemplate").ToString
        End If

        If Not dtTemplate Is Nothing AndAlso dtTemplate.Rows.Count > 0 AndAlso Not dtTemplate.Rows(0).Item("ddlLayoutTemplate") Is DBNull.Value AndAlso Not ddlLayoutTemplate.Items.FindByValue(sFormatTemplate) Is Nothing Then
            ddlLayoutTemplate.Items.FindByValue(sFormatTemplate).Selected = True
            ddlLayoutTemplateReportOptions.Items.FindByValue(sFormatTemplate).Selected = True
        Else
            ddlLayoutTemplate.Items.FindByValue(String.Empty).Selected = True
            ddlLayoutTemplateReportOptions.Items.FindByValue(String.Empty).Selected = True
        End If

        If Not dtEmailActive Is Nothing AndAlso dtEmailActive.Rows.Count > 0 Then
            Dim sSelectedEmailTemplate As String
            If dtTemplate.Rows.Count > 0 Then
                sSelectedEmailTemplate = dtTemplate.Rows(0).Item("ddlEmailTemplate").ToString
            End If

            If dtTemplate.Rows.Count > 0 AndAlso Not dtTemplate.Rows(0).Item("ddlEmailTemplate") Is DBNull.Value AndAlso Not ddlLayoutTemplate.Items.FindByValue(sSelectedEmailTemplate) Is Nothing Then
                ddlEmailTemplate.Items.FindByValue(sSelectedEmailTemplate).Selected = True
            Else
                ddlEmailTemplate.Items.FindByValue(String.Empty).Selected = True
            End If

            fsEmailTemplate.Visible = True
        Else
            fsEmailTemplate.Visible = False
        End If
    End Sub
    Private Sub ApplyOptionPreferences(Optional ByVal sLayoutTemplate As String = "None", Optional ByVal sAccountOverRide As String = "")
        Dim sbSQL As New StringBuilder
        sbSQL.AppendLine("DECLARE @LayoutTemplate VARCHAR(50)")
        sbSQL.AppendLine("")
        If Not sLayoutTemplate = "None" Then
            sbSQL.AppendLine(String.Format("SET @LayoutTemplate = '{0}'", sLayoutTemplate))
        End If
        sbSQL.AppendLine("")
        sbSQL.AppendLine("SELECT")
        'sbSQL.AppendLine("	[UniversalAccountIdentifier]")
        'sbSQL.AppendLine("	[PageName]")
        sbSQL.AppendLine("	 [ObjectName]")
        sbSQL.AppendLine("	,[ObjectType]")
        sbSQL.AppendLine("	,[SelectedValue]")
        sbSQL.AppendLine("	,[Value]")
        'sbSQL.AppendLine("	[Template]")
        sbSQL.AppendLine("	")
        sbSQL.AppendLine("FROM [dbo].[tdUserPreference] WITH(NOLOCK)")
        sbSQL.AppendLine("WHERE [UniversalAccountIdentifier] = '" & IIf(sAccountOverRide = String.Empty, InvWSS.Platform.Application.AccountIdentifier, sAccountOverRide) & "'")
        sbSQL.AppendLine("	AND [PageName] = 'FinancialSummary'")
        sbSQL.AppendLine("	AND ([Template] = @LayoutTemplate OR ([Template] IS NULL AND @LayoutTemplate IS NULL))")
        sbSQL.AppendLine("")
        sbSQL.AppendLine("")


        Dim tabUserPrefs As New DataTable
        InvWSS.GetData(sbSQL.ToString(), tabUserPrefs)
        If Not IsNothing(tabUserPrefs) AndAlso tabUserPrefs.Rows.Count > 0 Then
            For Each row As DataRow In tabUserPrefs.Rows
                If row("ObjectType").ToLower = "cbl" Then

                    Dim cblGeneric As CheckBoxList = CType(FindControlRecursive(Me, row("ObjectName")), WebControl)
                    For Each item As WebControls.ListItem In cblGeneric.Items
                        If item.Value = row("SelectedValue") Then
                            item.Selected = CBool(row("Value"))
                            Exit For
                        End If
                    Next
                    cblGeneric.Dispose()
                ElseIf row("ObjectType").ToLower = "chk" Then
                    CType(FindControlRecursive(Me, row("ObjectName")), CheckBox).Checked = CBool(row("Value"))
                End If
            Next
        Else
            cbGroupRegion.Checked = True
            cbGroupDistrict.Checked = True
            cbFloatingHeader.Checked = True

            SetUserPreferences(sLayoutTemplate, sAccountOverRide)
        End If
    End Sub

    Protected Function FindControlRecursive(ctlToSearch As Control, sControlID As String) As WebControl
        If (ctlToSearch.ID = sControlID) Then
            Return ctlToSearch
        End If

        For Each ctlToCheck As Control In ctlToSearch.Controls
            Dim ctlFoundIt As WebControl = FindControlRecursive(ctlToCheck, sControlID)

            If (ctlFoundIt IsNot Nothing) Then
                Return ctlFoundIt
            End If
        Next
        Return Nothing
    End Function


    Private Function getColumnSelect() As String
        Dim sbColList As New StringBuilder

        For Each iKey As Integer In oVisibleColList.Keys
            Select Case iKey
                Case 0
                    sbColList.AppendLine("SELECT")
                    sbColList.AppendLine("	 [" & oVisibleColList(iKey).sColName & "]")
                Case Else
                    sbColList.AppendLine("	,[" & oVisibleColList(iKey).sColName & "]")
            End Select
        Next

        sbColList.AppendLine("	,[RowType]")
        sbColList.AppendLine("	,[StoreComparable]")
        sbColList.AppendLine("	,[FranchiseNum]")
        sbColList.AppendLine("	,[StoreID]")



        Return sbColList.ToString
    End Function

    Private Sub BuildTitle()
        Dim sbSQL As New StringBuilder
        Dim tabResults As New DataTable

        Dim dStartDate As Date
        Dim dEndDate As Date

        Date.TryParse(tbStartDate.Text, dStartDate)
        Date.TryParse(tbEndDate.Text, dEndDate)

        sTitle = New StringBuilder

        'Build Title
        Select Case ddlDateOption.SelectedItem.Value
            Case 1 'Daily
                sTitle.Append("Daily Financial Summary Report")
            Case 2 'Weekly
                sTitle.Append("Weekly Financial Summary Report")
            Case 3 'Period
                sTitle.Append("Period Financial Summary Report")
            Case 4 'Range
                sTitle.Append("Range Financial Summary Report")
        End Select

        sSubTitle.Append("<br>")

        If ddlDateOption.SelectedItem.Value = 2 Or ddlDateOption.SelectedItem.Value = 3 Then
            With sbSQL
                .AppendLine("DECLARE @ReportingYear INT")
                .AppendLine("DECLARE @ReportingPeriod INT")
                .AppendLine("DECLARE @ReportingWeekNum INT")
                .AppendLine("")
                .AppendLine("SET @ReportingYear = " & ddlYear.SelectedValue)
                .AppendLine("SET @ReportingPeriod = " & ddlPeriod.SelectedValue)
                If ddlDateOption.SelectedItem.Value = 2 Then
                    .AppendLine("SET @ReportingWeekNum = " & ddlWeek.SelectedValue)
                End If
                .AppendLine("")
                .AppendLine("SELECT")
                .AppendLine("	 MIN(tmRP.[ReportingStartDate]) AS [ReportingStartDate]")
                .AppendLine("	,MAX(tmRP.[ReportingEndDate]) AS [ReportingEndDate]")
                .AppendLine("FROM [dbo].[tmReportingPeriod] AS [tmRP]")
                .AppendLine("WHERE tmRP.[ReportingYear] = @ReportingYear")
                .AppendLine("	AND tmRP.[ReportingPeriod] = @ReportingPeriod")
                .AppendLine("	AND (@ReportingWeekNum IS NULL OR tmRP.[ReportingWeekNum] = @ReportingWeekNum)")
            End With

            InvWSS.GetData(sbSQL.ToString(), tabResults)

            If Not tabResults Is Nothing AndAlso tabResults.Rows.Count > 0 Then
                dStartDate = tabResults.Rows(0).Item("ReportingStartDate")
                dEndDate = tabResults.Rows(0).Item("ReportingEndDate")
            End If

            tabResults = Nothing
            sbSQL = New StringBuilder
        End If


        Select Case ddlDateOption.SelectedItem.Value
            Case 1 'Daily
                sSubTitle.Append("Business Date: " & dStartDate.ToShortDateString)
                'pdfSubTitle = "Business Date: " & dStartDate.ToShortDateString)
            Case 2 'Weekly
                sSubTitle.Append("Year: ")
                sSubTitle.Append(ddlYear.SelectedValue & ", ")
                sSubTitle.Append("Period: ")
                sSubTitle.Append(ddlPeriod.SelectedValue & ", ")
                sSubTitle.Append("Week: ")
                sSubTitle.Append(ddlWeek.SelectedValue)
                'Mark Williams bug 565
                sSubTitle.Append(", Reporting Week: ")
                sSubTitle.Append(dStartDate.ToShortDateString & " - " & dEndDate.ToShortDateString)
                'pdfSubTitle = Session("rptopWeekRange")
            Case 3 'Period
                sSubTitle.Append("Year: ")
                sSubTitle.Append(ddlYear.SelectedValue & ", ")
                sSubTitle.Append("Period: ")
                sSubTitle.Append(ddlPeriod.SelectedValue)
                'Mark Williams bug 565
                sSubTitle.Append(", Reporting Period: ")
                sSubTitle.Append(dStartDate.ToShortDateString & " - " & dEndDate.ToShortDateString)
                'pdfSubTitle = Session("rptopPeriodRange")
            Case 4 'Range
                sSubTitle.Append("Date Range: " & dStartDate.ToShortDateString & " - " & dEndDate.ToShortDateString)
                'pdfSubTitle = "Date Range: " & Session("StartDate") & " - " & Session("EndDate")
        End Select
        If Not ddlLayoutTemplate.SelectedValue = String.Empty Then
            sSubTitle.Append("<br>")
            sSubTitle.Append(String.Format("Layout Template: {0}", ddlLayoutTemplate.SelectedValue))
        End If
    End Sub


    Private Sub BindGrid()
        Dim bShowCPR As Boolean = False

        Try
            Dim dtSessionTable As New DataTable
            dtSessionTable = Session("TempTable")

            SetVisibleLists(dtSessionTable)

            BuildTitle()

            Dim sbSQL As New StringBuilder
            Dim tabResults As New DataTable

            Dim dStartDate As Date
            Dim dEndDate As Date

            Date.TryParse(tbStartDate.Text, dStartDate)
            Date.TryParse(tbEndDate.Text, dEndDate)

            With sbSQL
                .AppendLine("DECLARE @StartDate DATETIME")
                .AppendLine("DECLARE @EndDate DATETIME")
                .AppendLine("DECLARE @ReportingYear INT")
                .AppendLine("DECLARE @ReportingPeriod INT")
                .AppendLine("DECLARE @ReportingWeekNum INT")
                .AppendLine("DECLARE @FranchiseNum INT")
                .AppendLine("DECLARE @RegionNum INT")
                .AppendLine("DECLARE @DistrictNum INT")
                .AppendLine("DECLARE @UniversalAccountIdentifier VARCHAR(32)")
                .AppendLine("DECLARE @StoreList StoreList")
                .AppendLine("DECLARE @IncludeToday BIT")
                .AppendLine("")
                .AppendLine("/* Set Variables */")
                .AppendLine("")
                If ddlRegion.SelectedValue <> "-1" AndAlso ddlRegion.SelectedValue <> "" Then
                    .AppendLine("SET @RegionNum = " & ddlRegion.SelectedValue)
                End If
                'If a "District" is selected --> Append the WHERE clause
                If ddlDistrict.SelectedValue <> "-1" AndAlso ddlDistrict.SelectedValue <> "" Then
                    .AppendLine("SET @DistrictNum = " & ddlDistrict.SelectedValue)
                End If

                Select Case ddlDateOption.SelectedItem.Value
                    Case 1 'Daily
                        If Not dStartDate = Date.MinValue Then
                            .AppendLine("SET @StartDate = '" & dStartDate.ToString("yyyy-MM-dd") & "'")
                        Else
                            .AppendLine("SET @StartDate = '" & Today.ToString("yyyy-MM-dd") & "'")
                        End If
                    Case 2 'Weekly
                        .AppendLine("SET @ReportingYear = " & CInt(ddlYear.SelectedValue).ToString)
                        .AppendLine("SET @ReportingPeriod = " & CInt(ddlPeriod.SelectedValue).ToString)
                        .AppendLine("SET @ReportingWeekNum = " & CInt(ddlWeek.SelectedValue).ToString)
                    Case 3 'Period
                        .AppendLine("SET @ReportingYear = " & CInt(ddlYear.SelectedValue).ToString)
                        .AppendLine("SET @ReportingPeriod = " & CInt(ddlPeriod.SelectedValue).ToString)
                    Case 4 'Range
                        .AppendLine("SET @StartDate = '" & dStartDate.ToString("yyyy-MM-dd") & "'")
                        .AppendLine("SET @EndDate = '" & dEndDate.ToString("yyyy-MM-dd") & "'")
                End Select

                .AppendLine("SET @UniversalAccountIdentifier = '" & InvWSS.Platform.Application.AccountIdentifier & "'")

                For Each li As System.Web.UI.WebControls.ListItem In cblStore.Items
                    'If a store is selected --> Update the boolean and exit the loop
                    If li.Selected Then
                        .AppendLine("INSERT @StoreList([StoreID]) VALUES(" & li.Value & ")")
                    End If
                Next

                If cbIncludeTodaysData.Checked = True Then
                    .AppendLine("SET @IncludeToday = 1")
                Else
                    .AppendLine("SET @IncludeToday = 0")
                End If


                .AppendLine("")
                .AppendLine("/* /Set Variables */")
                .AppendLine("")
                .Append(getColumnSelect())
                .AppendLine("FROM [dbo].[pf_GetFinancialSummary]")
                .AppendLine("	(@StartDate")
                .AppendLine("	,@EndDate")
                .AppendLine("	,@ReportingYear")
                .AppendLine("	,@ReportingPeriod")
                .AppendLine("	,@ReportingWeekNum")
                .AppendLine("	,@FranchiseNum")
                .AppendLine("	,@RegionNum")
                .AppendLine("	,@DistrictNum")
                .AppendLine("	,@UniversalAccountIdentifier")
                .AppendLine("	,@StoreList")
                .AppendLine("	,@IncludeToday")
                .AppendLine("	)")
                .AppendLine("WHERE [RowType] IN (" & RowType.Header & ", " & RowType.Store & ", " & RowType.GrandAverage & ", " & RowType.GrandComparable & ", " & RowType.GrandNonComparable & ") --Header/Store/Grand Average/Comparable/Non-Comparable")
                '2017-01-03 jjc no-one wants this at the moment, but I've sorted out how to do it in the Function, so I've put this here for potential future usage
                'If cbGroupFranchise.Checked = True Then
                '    .AppendLine("	OR [RowType] = " & RowType.Franchise & " --Franchise")
                'End If
                If cbGroupRegion.Checked = True Then
                    .AppendLine("	OR [RowType] = " & RowType.Region & " --Region")
                    .AppendLine("	OR [RowType] = " & RowType.RegionAverage & " --RegionAverage")
                End If
                If cbGroupDistrict.Checked = True Then
                    If cbGroupRegion.Checked = True Then
                        .AppendLine("	OR [RowType] = " & RowType.DistrictByRegion & " --DistrictByRegion")
                        .AppendLine("	OR [RowType] = " & RowType.DistrictByRegionAverage & " --DistrictByRegionAverage")
                    Else
                        .AppendLine("	OR [RowType] = " & RowType.District & " --District")
                        .AppendLine("	OR [RowType] = " & RowType.DistrictAverage & " --DistrictAverage")
                    End If
                End If
                .AppendLine("ORDER BY")
                .AppendLine("	 [Header] DESC")
                .AppendLine("	,[GroupingNonComparable] DESC")
                .AppendLine("	,[GroupingComparable] DESC")
                '2017-01-03 jjc no-one wants this at the moment, but I've sorted out how to do it in the Function, so I've put this here for potential future usage
                'If cbGroupFranchise.Checked = True Then
                '    .AppendLine("	,[GroupingFranchise]")
                '    .AppendLine("	,[Franchise]")
                'End If
                If cbGroupRegion.Checked = True Then
                    .AppendLine("	,[GroupingRegion]")
                    .AppendLine("	,[Region]")
                End If
                If cbGroupDistrict.Checked = True Then
                    .AppendLine("	,[GroupingDistrict]")
                    .AppendLine("	,[District]")
                End If
                .AppendLine("	,[GroupingStore]")
                .AppendLine("	,[GroupingAverage] DESC")
                If Session("SortColumnNum") Is Nothing OrElse Session("SortColumnNum") = 0 Then
                    'If cbGroupFranchise.Checked = False Then
                    .AppendLine("	,[Franchise]")
                    'End If
                    .AppendLine("	,[StoreDescription]")
                Else
                    'jjc So all of our columns are Formatted strings.  To Sort by $ amounts and %'s properly, strip out any '%' characters and convert the text to money for sorting.
                    .AppendLine("	,CASE [RowType] WHEN 0 THEN CAST(REPLACE([" & oVisibleColList(Session("SortColumnNum")).sColName & "], '%', '') AS MONEY) ELSE 0 END")
                End If

            End With

            'InvWSS.GetData(sbSQL.ToString, tabResults)
            tabResults = InvWSS.Platform.SBODBExecQuerySQL("SBOCore", sbSQL.ToString)

            If tabResults.Rows.Count > 0 Then

                For Each finRow As DataRow In tabResults.Rows
                    If Not finRow.Item("FranchiseNum").Equals(DBNull.Value) AndAlso finRow.Item("FranchiseNum") = 1 Then
                        bShowCPR = True
                        Exit For
                    End If
                Next


                Dim nRemovedColCount As Integer = 0
                Dim oUpdateVisibleColList As New SortedDictionary(Of Integer, FinSumColumn)

                For Each FinSumCol As FinSumColumn In oVisibleColList.Values
                    Select Case FinSumCol.sColName
                        Case "ProjectedCPR"
                            If bShowCPR = False Then
                                If tabResults.Columns.Contains("ProjectedCPR") Then
                                    tabResults.Columns.Remove("ProjectedCPR")
                                End If

                                nRemovedColCount += 1
                                Continue For
                            End If
                        Case "LaborGuideVarWTDHrs"
                            If ddlDateOption.SelectedItem.Value <> 1 Then 'Daily
                                If tabResults.Columns.Contains("LaborGuideVarWTDHrs") Then
                                    tabResults.Columns.Remove("LaborGuideVarWTDHrs")
                                End If

                                nRemovedColCount += 1
                                Continue For
                            End If
                    End Select

                    FinSumCol.nPosition -= nRemovedColCount
                    FinSumCol.nVisiblePosition -= nRemovedColCount

                    oUpdateVisibleColList.Add(FinSumCol.nVisiblePosition, FinSumCol)
                Next
                oVisibleColList = oUpdateVisibleColList

                With gv1
                    .DataSource = tabResults
                    .DataBind()
                End With

                gridContainer.Visible = True

                ExportPDF(InvWSS.Platform.Application.AccountIdentifier)

                ddlExportAs.Visible = True
                btnExport.Visible = True
                lblExportAs.Visible = True
            End If
        Catch ex As Exception
            'Eat it...
        End Try
    End Sub

    Protected Sub btnExport_Click(sender As Object, e As EventArgs)
        Select Case ddlExportAs.SelectedValue
            Case "pdf"
                ExportPDF()
            Case "excel"
                BuildTitle()
                GoExcelEPPlus(gv1)
            Case "csv"
                ExportCSV(InvWSS.Platform.Application.AccountIdentifier)
                ExportCSV()
        End Select
    End Sub

    Public Sub ExportPDF()
        Dim sPath As String = Server.MapPath("ChartImages/FinSum-" & Session("AccountID") & ".pdf")
        Response.ClearContent()
        Response.ClearHeaders()
        Response.Clear()
        Response.Buffer = True
        Response.AddHeader("content-disposition", "attachment;filename=FinancialSummary.pdf")
        Response.ContentType = "application/pdf"
        Response.WriteFile(sPath)
        Response.End()
    End Sub

    Public Sub ExportCSV()
        Dim sPath As String = Server.MapPath("ChartImages/FinSum-" & Session("AccountID") & ".csv")
        Response.ClearContent()
        Response.ClearHeaders()
        Response.Clear()
        Response.Buffer = True
        Response.AddHeader("content-disposition", "attachment;filename=FinSumReport.csv")
        Response.ContentType = "application/text"
        Response.Write(sbCSVExport.ToString())
        Response.End()
    End Sub


    Public Sub btnSubmit_Click(ByVal sender As Object, ByVal e As EventArgs)
        If Page.IsValid Then
            Dim bLayoutChanged As Boolean = LayoutChanged()

            SetOrder()

            If bLayoutChanged Then
                If Not ddlLayoutTemplate.Items.FindByValue(String.Empty) Is Nothing Then
                    ddlLayoutTemplate.SelectedItem.Selected = False
                    ddlLayoutTemplate.Items.FindByValue(String.Empty).Selected = True

                    If Not ddlLayoutTemplateReportOptions.Items.FindByValue(String.Empty) Is Nothing Then
                        ddlLayoutTemplateReportOptions.SelectedItem.Selected = False
                        ddlLayoutTemplateReportOptions.Items.FindByValue(String.Empty).Selected = True
                    End If

                    SetUserPreferences() 'Enabled/Ordering of Columns
                    PopColumns()
                End If
            Else
                SetUserPreferences(bSelectedTemplateOnly:=True)
            End If

            BindGrid()
        End If
    End Sub

    Public Sub ddlRegion_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs)

        lblDistrict.Visible = False
        lblStore.Visible = False

        PopDistricts()
        PopStores()

        tbShowOpts.Text = "True"

    End Sub

    Public Sub ddlDistrict_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs)

        lblStore.Visible = False

        PopStores()

        tbShowOpts.Text = "True"

    End Sub

    Public Sub ddlStore_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs)

        lblStore.Visible = False

        tbShowOpts.Text = "True"

    End Sub

    Public Sub grdColumns_OnRowDataBound(ByVal sender As Object, ByVal e As GridRowEventArgs)

        e.Row.Cells(3).Text = "<input type=""checkbox"" data-column=""" & e.Row.Cells(0).Text & """" & If(CBool(e.Row.Cells(2).Text), " checked", "") & " />"

    End Sub

    Public Sub ddlLayoutTemplate_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs)
        Dim ddlTemplate As ListControl = TryCast(sender, ListControl)
        Dim sTemplate As String = ddlTemplate.SelectedValue

        If ddlTemplate.ID = "ddlLayoutTemplate" Then
            ddlLayoutTemplateReportOptions.SelectedValue = sTemplate
        Else
            tbShowOpts.Text = True
            ddlLayoutTemplate.SelectedValue = sTemplate
        End If


        If sTemplate = String.Empty Then
            PopColumns() 'Enabled/Ordering of Columns
        Else
            PopColumns(sTemplate) 'Enabled/Ordering of Columns
        End If

        With gv1
            .DataSource = Nothing
            .DataBind()
        End With

        ddlExportAs.Visible = False
        btnExport.Visible = False
        lblExportAs.Visible = False


        'jjc Test
        tbColumnVisible.Text = String.Empty
        neworder.Text = "nochange"
    End Sub

    Public Sub ddlEmailTemplate_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs)
        Dim ddlTemplate As ListControl = TryCast(sender, ListControl)
        Dim sTemplate As String = ddlTemplate.SelectedValue
        Dim sbPreferences As New StringBuilder

        If ddlLayoutTemplate.SelectedValue = String.Empty Then
            AppendUserPreferenceLine(sbPreferences, ddlEmailTemplate.ID, "ddl", Nothing, Nothing, "NULL")
        Else
            AppendUserPreferenceLine(sbPreferences, ddlEmailTemplate.ID, "ddl", Nothing, sTemplate, "NULL")
        End If

        SetUserPreferences(sPreferences:=sbPreferences.ToString)
    End Sub

    Public Sub ddlDateOption_SelectedIndexChanged()
        Select Case ddlDateOption.SelectedItem.Value
            Case 1 'Daily
                fsYear.Visible = False
                fsPeriod.Visible = False
                fsWeek.Visible = False
                fsDates.Visible = True
                fsIncludeTodaysData.Visible = False
                tbEndDate.Visible = False
                ImageEndDate.Visible = False
                lblEndDate.Visible = False

            Case 2 'Weekly
                fsYear.Visible = True
                fsPeriod.Visible = True
                fsWeek.Visible = True
                fsDates.Visible = False
                fsIncludeTodaysData.Visible = True
            Case 3 'Period
                fsYear.Visible = True
                fsPeriod.Visible = True
                fsWeek.Visible = False
                fsDates.Visible = False
                fsIncludeTodaysData.Visible = True
            Case 4 'Range
                fsYear.Visible = False
                fsPeriod.Visible = False
                fsWeek.Visible = False
                fsDates.Visible = True
                tbEndDate.Visible = True
                ImageEndDate.Visible = True
                lblEndDate.Visible = True
                fsIncludeTodaysData.Visible = True
        End Select

    End Sub

    Public Sub ddlYear_SelectedIndexChanged()

        PopReportingPeriods()
        PopReportingWeeks()

        'tbShowOpts.Text = "True"

    End Sub

    Public Sub ddlPeriod_SelectedIndexChanged()

        PopReportingWeeks()

        'tbShowOpts.Text = "True"

    End Sub

    Sub grdColumns_UpdateRow(ByVal Sender As Object, ByVal e As GridRecordEventArgs)
        SetOrder()
        Dim SessionTable As New DataTable
        SessionTable = Session("TempTable")
        Dim row As DataRow
        For Each row In SessionTable.Rows
            If row("SelectedValue") = e.Record("SelectedValue") Then
                row("FriendlyName") = e.Record("FriendlyName")
                row("Visible") = e.Record("Visible")
            End If
        Next

        Session("TempTable") = SessionTable

    End Sub

    Sub grdColumns_RebindGrid(ByVal sender As Object, ByVal e As EventArgs)
        Dim SessionTable As New DataTable
        SessionTable = Session("TempTable")
        Dim dvColumnsFiltered As New DataView(SessionTable, "SelectedValue <> 'StoreDescription'", "AbsOrder", DataViewRowState.CurrentRows)
        grdColumns.DataSource = dvColumnsFiltered

        grdColumns.DataBind()
    End Sub

    Function LayoutChanged() As Boolean
        Dim bLayoutChanged As Boolean

        Dim SessionTable As New DataTable
        Dim row As DataRow
        Dim sOrder() As String
        Dim sVisible() As String
        Dim lVisible As New List(Of String)


        SessionTable = Session("TempTable")

        sOrder = neworder.Text.Split("|")

        Dim iCount As Integer
        For iCount = 1 To sOrder.GetUpperBound(0)
            For Each row In SessionTable.Rows
                If row("SelectedValue") = sOrder(iCount - 1) Then
                    If Not row("AbsOrder") = iCount Then
                        bLayoutChanged = True
                    End If
                    Exit For
                End If
            Next
        Next

        If Not tbColumnVisible.Text = String.Empty Then
            lVisible.Add("StoreDescription")
            sVisible = tbColumnVisible.Text.Split("|")

            For Each VisibleCol As String In sVisible
                lVisible.Add(VisibleCol)
            Next


            For Each row In SessionTable.Rows
                If lVisible.Contains(row("SelectedValue")) Then
                    If Not row("Value") = row("AbsOrder") Then
                        bLayoutChanged = True
                    End If
                Else
                    If Not row("Value") = row("AbsOrder") * -1 Then
                        bLayoutChanged = True
                    End If
                End If
            Next
        End If

        Return bLayoutChanged
    End Function

    Sub SetOrder()

        Dim SessionTable As New DataTable
        Dim row As DataRow
        Dim sOrder() As String
        Dim sVisible() As String
        Dim lVisible As New List(Of String)

        SessionTable = Session("TempTable")

        sOrder = neworder.Text.Split("|")

        Dim iCount As Integer
        For iCount = 1 To sOrder.GetUpperBound(0)
            For Each row In SessionTable.Rows
                If row("SelectedValue") = sOrder(iCount - 1) Then
                    If Not row("AbsOrder") = iCount Then
                        row("AbsOrder") = iCount
                    End If
                    Exit For
                End If
            Next
        Next

        If Not tbColumnVisible.Text = String.Empty Then
            lVisible.Add("StoreDescription")
            sVisible = tbColumnVisible.Text.Split("|")

            For Each VisibleCol As String In sVisible
                lVisible.Add(VisibleCol)
            Next


            For Each row In SessionTable.Rows
                If lVisible.Contains(row("SelectedValue")) Then
                    row("Value") = row("AbsOrder")
                    row("Visible") = True
                Else
                    row("Value") = row("AbsOrder") * -1
                    row("Visible") = False
                End If
            Next
        End If

        Session("TempTable") = SessionTable
    End Sub


    Sub MailRun()

        Dim sbSQL As New StringBuilder

        With sbSQL
            .AppendLine("SELECT")
            .AppendLine("	 tmA.[UniversalAccountIdentifier]")
            .AppendLine("	,tmA.[EmailAddress]")
            .AppendLine("	,tmEM.[Subject]")
            .AppendLine("	,tdEM.[LastReportDate]")
            .AppendLine("FROM [$Server$].[$Database$].[dbo].[tmAccount] AS [tmA] WITH(NOLOCK)")
            .AppendLine("	INNER JOIN [$Server$].[$Database$].[dbo].[tdEmailMessages] AS [tdEM] WITH(NOLOCK)")
            .AppendLine("		ON tmA.[UniversalAccountIdentifier] = tdEM.[UniversalAccountIdentifier]")
            .AppendLine("	INNER JOIN [$Server$].[$Database$].[dbo].[tmEmailMessages] AS [tmEM] WITH(NOLOCK)")
            .AppendLine("		ON tdEM.[EmailMessageID] = tmEM.[EmailMessageID]")
            .AppendLine("	INNER JOIN [$Server$].[$Database$].[dbo].[tdEmailFrequency] AS [tdEF]")
            .AppendLine("		ON tmEM.[EmailMessageID] = tdEF.[EmailMessageID]")
            If bEmailCSV Then
                .AppendLine("WHERE tmEM.[Title] = 'Financial Summary - CSV'")
            Else
                .AppendLine("WHERE tmEM.[Title] = 'Financial Summary'")
            End If
            .AppendLine("	AND tmA.[EmailAddress] IS NOT NULL")
            .AppendLine("	AND tmA.[EmailAddress] <> ''")
            .AppendLine("	AND tmA.[ActiveAccount] <> 0")
            .AppendLine("	AND tdEF.[Active] = 1")
            If Len(Request.QueryString("Address")) Then
                .AppendLine("   AND tmA.EmailAddress = '" & Request.QueryString("address") & "'")
            End If
        End With

        sbSQL.Replace("$Server$", sOrgSQLServer)
        sbSQL.Replace("$Database$", sOrgSQLDatabase)

        Dim PlatformRoot As ISBOPlatformV100
        InvWSS = New InventoryWebSiteServices.InventoryServices
        PlatformRoot = InvWSS.Login("UserMaster", "B6056157A99942D78B118A7F94EC7208", True)

        Dim tTable As DataTable = PlatformRoot.SBODBExecQuerySQL("SBORoot", sbSQL.ToString)

        If tTable.Rows.Count Then
            Dim sGUID As String = Guid.NewGuid.ToString
            Dim Yesterday As Date = DateAdd(DateInterval.Day, -1, CDate(Now.ToShortDateString))

            tbStartDate.Text = Yesterday.ToString("yyyy-MM-dd")
            tbEndDate.Text = Yesterday.ToString("yyyy-MM-dd")

            For Each row As DataRow In tTable.Rows
                Dim Platform As ISBOPlatformV100
                InvWSS = New InventoryWebSiteServices.InventoryServices
                Platform = InvWSS.Login(Request.QueryString("org"), row("UniversalAccountIdentifier"), True)
                Session("AccountID") = Platform.Application.AccountIdentifier

                sEmailTemplate = sRequestedEmailTemplate

                If sEmailTemplate = String.Empty Then 'no
                    sbSQL = New StringBuilder

                    sbSQL.AppendLine("DECLARE @UniversalAccountIdentifier VARCHAR(50)")
                    sbSQL.AppendLine("")
                    sbSQL.AppendLine(String.Format("SET @UniversalAccountIdentifier = '{0}'", row("UniversalAccountIdentifier")))
                    sbSQL.AppendLine("")
                    sbSQL.AppendLine("SELECT [Value] AS [EmailTemplate]")
                    sbSQL.AppendLine("FROM [dbo].[tdUserPreference]")
                    sbSQL.AppendLine("WHERE [PageName] = 'FinancialSummary'")
                    sbSQL.AppendLine("	AND [UniversalAccountIdentifier] = @UniversalAccountIdentifier")
                    sbSQL.AppendLine("	AND [ObjectType] = 'ddl'")
                    sbSQL.AppendLine("	AND [ObjectName] = 'ddlEmailTemplate'")

                    Dim tEmailTemplate As DataTable = Platform.SBODBExecQuerySQL("SBOCore", sbSQL.ToString)
                    If Not tEmailTemplate Is Nothing AndAlso tEmailTemplate.Rows.Count > 0 Then
                        sEmailTemplate = tEmailTemplate.Rows(0).Item("EmailTemplate")
                    End If

                    If sEmailTemplate = String.Empty Then
                        Continue For
                    End If
                Else
                    'jjc todo template exists
                    sbSQL = New StringBuilder

                    sbSQL.AppendLine("DECLARE @UniversalAccountIdentifier VARCHAR(50)")
                    sbSQL.AppendLine("DECLARE @LayoutTemplate VARCHAR(50) = 'None'")
                    sbSQL.AppendLine("DECLARE @TemplateExists BIT")
                    sbSQL.AppendLine("")
                    sbSQL.AppendLine(String.Format("SET @UniversalAccountIdentifier = '{0}'", row("UniversalAccountIdentifier")))
                    sbSQL.AppendLine(String.Format("SET @LayoutTemplate = '{0}'", sEmailTemplate))
                    sbSQL.AppendLine("")
                    sbSQL.AppendLine("SELECT @TemplateExists = CASE WHEN COUNT(*) = 0 THEN 0 ELSE 1 END")
                    sbSQL.AppendLine("FROM [dbo].[tdUserPreference]")
                    sbSQL.AppendLine("WHERE [PageName] = 'FinancialSummary'")
                    sbSQL.AppendLine("	AND [UniversalAccountIdentifier] = @UniversalAccountIdentifier")
                    sbSQL.AppendLine("	AND [Template] = @LayoutTemplate")
                    sbSQL.AppendLine("")
                    sbSQL.AppendLine("SELECT @TemplateExists AS [TemplateExists]")

                    Dim bTemplateExists As Boolean = False
                    Dim tTemplateExists As DataTable = Platform.SBODBExecQuerySQL("SBOCore", sbSQL.ToString)

                    If Not tTemplateExists Is Nothing AndAlso tTemplateExists.Rows.Count > 0 Then
                        bTemplateExists = tTemplateExists.Rows(0).Item("TemplateExists")
                    End If

                    If Not bTemplateExists Then
                        Continue For
                    End If
                End If


                'DAF 2016-11-16 - S18 //D-01122 - Reset sbCSVExport
                sbCSVExport = New StringBuilder

                '2020-04-07 jjc Sprint 79w15 SBOD-1686/1701 need to pass "Default" override for Global Templates.
                'ApplyOptionPreferences(sEmailTemplate) 'Grouping Checkboxes
                PopColumns(sEmailTemplate) 'Enabled/Ordering of Columns
                If sEmailTemplate.Contains("*") Then
                    ApplyOptionPreferences(sEmailTemplate, "Default") 'Grouping Checkboxes
                Else
                    ApplyOptionPreferences(sEmailTemplate) 'Grouping Checkboxes
                End If

                PopRegions()
                PopDistricts()
                PopStores()

                ddlDateOption.SelectedValue = 1

                '************************************************************************
                sGUID = "FinSum-" & row("UniversalAccountIdentifier")

                BindGrid()
                If bEmailCSV Then
                    ExportCSV(row("UniversalAccountIdentifier").ToString)
                End If

                If SendEmailReport_AmazonSES(row("EmailAddress"), sGUID) = True Then
                    SetLastReport(Platform, row("UniversalAccountIdentifier"), Yesterday)
                End If
                Try
                    File.Delete(Server.MapPath("ChartImages/" & sGUID & IIf(bEmailCSV, ".csv", ".pdf")))
                Catch ex As Exception

                End Try

                Session("AccountID") = Nothing
                Session("UserID") = Nothing
                Session("IWSS") = Nothing
            Next
        End If
    End Sub
    Function SendEmailReport_AmazonSES(ByVal sAddress As String, ByVal sFileGUID As String) As Boolean
        Dim dStartDate As Date
        Date.TryParse(tbStartDate.Text, dStartDate)

        Dim bResult As Boolean = False
        Dim fStream As FileStream
        Dim sMessage As String = "This is an automated message from SBOnet." & vbCrLf & vbCrLf
        Dim sSubject As String = "Financial Summary Report"
        Dim sEmailAddress As String = sAddress
        Dim sFileName As String = sOrgName & "-FinancialSummary-" & Format(dStartDate, "yyyyMMdd").ToUpper & IIf(bEmailCSV, ".csv", ".pdf")

        fStream = New FileStream(Server.MapPath("ChartImages/" & sFileGUID & IIf(bEmailCSV, ".csv", ".pdf")), FileMode.Open)
        'WebServicesAddons.AmazonSES.SendMail(sAddress, sSubject, sMessage, fStream,,, sFileName)
        SendMail(sEmailAddress, sSubject, sMessage, fStream, sFileName)
        fStream.Close()
        bResult = True
        Try
            fStream.Close()
        Catch eFileClose As Exception
        End Try

        Try
            File.Delete("ChartImages/" & sFileGUID & IIf(bEmailCSV, ".csv", ".pdf"))
        Catch eFileDelete As Exception
        End Try


        Return bResult

    End Function

    Function SendEmailReport(ByVal sAddress As String, ByVal sFileGUID As String) As Boolean
        'If True Then 'Test
        '    sAddress = "<EMAIL>"
        'End If
        Dim sSQL As String
        Dim bResult As Boolean = False
        Dim msReport As MemoryStream
        Dim EmailStream As FileStream
        Dim sMessageGUID As String = Guid.NewGuid.ToString
        Dim sMessage As String = "This is an automated message from SBOnet." & vbCrLf & vbCrLf
        Dim sSubject As String = "Financial Summary Report"
        Dim bFirstPass As Boolean = True
        Dim sEmailAddress As String = sAddress
        Dim sFileName As String = sOrgName & "-FinancialSummary-" & Format(CDate(tbStartDate.Text), "yyyyMMdd").ToUpper
        Try

            EmailStream = New FileStream(Server.MapPath("ChartImages/" & sFileGUID & IIf(bEmailCSV, ".csv", ".pdf")), FileMode.Open)

            msReport = New MemoryStream
            CopyData(EmailStream, msReport)

            If EMAddAttachment(sMessageGUID.ToUpper, sFileName & IIf(bEmailCSV, ".csv", ".pdf"), msReport) = True Then
                sSQL = "INSERT INTO Message "
                sSQL += "("
                sSQL += "[AllRecipients], "
                sSQL += "[Author], "
                sSQL += "[DTRcvd], "
                sSQL += "[DTSent], "
                sSQL += "[RecordDate], "
                sSQL += "[HasAttachments], "
                sSQL += "[MsgHeader], "
                sSQL += "[Note], "
                sSQL += "[ParentFolder], "
                sSQL += "[Subject], "
                sSQL += "[Viewed], "
                sSQL += "[ReplyTo], "
                sSQL += "[IsPackage], "
                sSQL += "[PackageStatus], "
                sSQL += "[POP3Account], "
                sSQL += "[POPMsgID], "
                sSQL += "[dekaolc], "
                sSQL += "[GUID], "
                sSQL += "[FromAlias], "
                sSQL += "[HTML] "
                sSQL += ")"
                sSQL += "VALUES "
                sSQL += "("
                sSQL += "'" & sEmailAddress & "', "
                sSQL += "'<EMAIL>', "
                sSQL += "'" & Now() & "', "
                sSQL += "'" & Now() & "', "
                sSQL += "'" & Now() & "', "
                sSQL += "1, "
                sSQL += "'', "
                sSQL += "'" & sMessage & "', "
                sSQL += "1, "
                sSQL += "'" & sSubject & "', "
                sSQL += "0, "
                sSQL += "'<EMAIL>', "
                sSQL += "0, "
                sSQL += "0, "
                sSQL += "'<EMAIL>', "
                sSQL += "'" & Guid.NewGuid.ToString & "<EMAIL>', "
                sSQL += "0, "
                sSQL += "'" & sMessageGUID.ToUpper & "', "
                sSQL += "'', "
                sSQL += "''"
                sSQL += ")"

                'Response.Write(sSQL)
                'RunCommand(sSQL, "data source=ASP-SQL-13\SQL2005;initial catalog=eManager_App4;Password=Reporter001;persist security info=False;user id=sa;workstation id=ASP-WSS-1;packet size=4096")
                Dim SQLConEM As New SqlConnection("data source=SQL-CLU01;initial catalog=eManager_App4;Password=Reporter001;persist security info=False;user id=sa;workstation id=ASP-WSS-1;packet size=4096")
                SQLConEM.Open()
                Dim cmdSQL As New SqlCommand(sSQL, SQLConEM)
                With cmdSQL
                    .CommandTimeout = 0
                    .ExecuteNonQuery()
                    .Dispose()
                End With
                SQLConEM.Close()
                SQLConEM.Dispose()
                bResult = True
            End If
            Try
                EmailStream.Close()
            Catch eFileClose As Exception
            End Try
            Try
                File.Delete("ChartImages/" & sFileGUID & IIf(bEmailCSV, ".csv", ".pdf"))
            Catch eFileDelete As Exception
            End Try


        Catch eMailError As Exception
            'Response.Write(eMailError.ToString)
        End Try
        Return bResult
    End Function

    Sub SetLastReport(ByRef Platform As ISBOPlatformV100, ByVal sUAI As String, ByVal dtLastScorecard As Date)
        Dim sbSQL As New StringBuilder

        With sbSQL
            .AppendLine("UPDATE [dbo].[tdEmailMessages]")
            .AppendLine("SET [LastReportDate] = '" & dtLastScorecard.ToShortDateString() & "'")
            .AppendLine("WHERE [EmailMessageID] =")
            .AppendLine("   (")
            .AppendLine("       SELECT [EmailMessageID]")
            .AppendLine("       FROM [dbo].[tmEmailMessages] WITH(NOLOCK)")
            If bEmailCSV Then
                .AppendLine("       WHERE [Title] = 'Financial Summary - CSV'")
            Else
                .AppendLine("       WHERE [Title] = 'Financial Summary'")
            End If
            .AppendLine("   )")
            .AppendLine("           AND [UniversalAccountIdentifier] = '" & sUAI & "'")
        End With

        Platform.SBODBExecNonQuerySQL("SBOCore", sbSQL.ToString)
    End Sub

    Private Sub CopyData(ByRef FromStream As Stream, ByRef ToStream As Stream)
        Try
            Dim nBytesRead As Integer
            Dim m_Size As Integer = 4096
            Dim bytes(m_Size) As Byte

            FromStream.Position = 0
            ToStream.Position = 0

            If FromStream.Length < m_Size Then m_Size = FromStream.Length

            nBytesRead = FromStream.Read(bytes, 0, m_Size)

            While nBytesRead > 0
                ToStream.Write(bytes, 0, nBytesRead)

                If FromStream.Length - FromStream.Position < m_Size Then m_Size = FromStream.Length - FromStream.Position
                nBytesRead = FromStream.Read(bytes, 0, m_Size)
            End While
        Catch eCopyData As Exception
            Throw eCopyData
        End Try
    End Sub

    Function EMAddAttachment(ByVal MsgGUID As String, ByVal sFileName As String, ByVal AttachmentStream As MemoryStream) As Boolean

        Dim bResponse As Boolean = False
        Dim oDB As New SqlConnection("data source=SQL-CLU01;initial catalog=eManager_App4;Password=Reporter001;persist security info=False;user id=sa;workstation id=ASP-WSS-1;packet size=4096")

        Try
            oDB.Open()

            Dim oCmd As SqlCommand = New SqlCommand("INSERT INTO Attachment (Contents, FileName, GUID, MessageGUID) VALUES (@Contents, @FileName, @GUID, @MessageGUID)", oDB)

            oCmd.Parameters.Add(New SqlParameter("@Contents", SqlDbType.Image, AttachmentStream.Length))
            oCmd.Parameters.Add(New SqlParameter("@FileName", SqlDbType.NVarChar, 255))
            oCmd.Parameters.Add(New SqlParameter("@GUID", SqlDbType.NVarChar, 50))
            oCmd.Parameters.Add(New SqlParameter("@MessageGUID", SqlDbType.NVarChar, 50))

            oCmd.Parameters("@Contents").Value = AttachmentStream.ToArray()
            oCmd.Parameters("@FileName").Value = sFileName
            oCmd.Parameters("@GUID").Value = Guid.NewGuid.ToString.Replace("-", "")
            oCmd.Parameters("@MessageGUID").Value = MsgGUID

            oCmd.ExecuteNonQuery()
            oCmd.Dispose()

            bResponse = True
        Catch eAddAttachment As Exception
            bResponse = False
            'Response.Write(eAddAttachment.ToString)
        Finally
            oDB.Close()
        End Try

        Return bResponse
    End Function

    'Function GetDBTable(ByVal sQuery As String, Optional ByVal sSQLServer As String = "", Optional ByVal sSQLDatabase As String = "") As DataTable

    '    'Dim SQLCon As New SqlConnection
    '    'Dim SQLCmd As New SqlDataAdapter
    '    'Dim tTable As New DataTable

    '    'If sSQLServer = String.Empty Then
    '    '    sSQLServer = sOrgSQLServer
    '    'End If

    '    'If sSQLDatabase = String.Empty Then
    '    '    sSQLDatabase = sOrgSQLDatabase
    '    'End If

    '    'Dim DBConString As String = BuildDBConnectString(sSQLServer, sSQLDatabase, "sboapplication", "SoftwareA43A")
    '    'Dim bResult As Boolean = False

    '    'Try

    '    '    SQLCon = New SqlConnection(DBConString)
    '    '    SQLCon.Open()

    '    '    SQLCmd = New SqlDataAdapter(sQuery, SQLCon)
    '    '    SQLCmd.SelectCommand.CommandTimeout = 720
    '    '    SQLCmd.Fill(tTable)
    '    '    SQLCon.Close()

    '    '    bResult = True

    '    'Catch eGetDataTable As Exception
    '    '    'sSQLErr &= sSQLServer & ":" & sSQLDatabase & " - " & eGetDataTable.Message & "<p>"

    '    'Finally

    '    '    If Not SQLCmd Is Nothing Then
    '    '        SQLCmd.Dispose()
    '    '    End If

    '    '    If Not SQLCon Is Nothing Then
    '    '        SQLCon.Dispose()
    '    '    End If

    '    'End Try

    '    'Return tTable

    'End Function

    'Sub RunCommand(ByVal SQL As String)

    '    'Try

    '    '    Dim dbConnect As New SqlConnection(Session("ConnectString"))
    '    '    Dim cmdSQL As SqlCommand

    '    '    dbConnect.Open()
    '    '    cmdSQL = New SqlCommand(SQL, dbConnect)

    '    '    With cmdSQL
    '    '        .CommandTimeout = 0
    '    '        .ExecuteNonQuery()
    '    '        .Dispose()
    '    '    End With

    '    '    dbConnect.Close()
    '    '    dbConnect.Dispose()

    '    'Catch Ex As Exception

    '    '    Response.Write(SQL & "<br /><br />")
    '    '    Response.Write(Ex.ToString)

    '    'End Try

    'End Sub

    'Public Function BuildDBConnectString(Optional ByVal SQLServer As String = "", Optional ByVal SQLDatabase As String = "", Optional ByVal SQLUserID As String = "", Optional ByVal SQLPassword As String = "", Optional ByVal PersistSecurityInfo As Boolean = False) As String

    '    'Dim sConnect As String = ""

    '    'If Len(SQLServer) Then
    '    '    sConnect = sConnect & "Data Source=" & SQLServer & ";"
    '    'End If

    '    'If Len(SQLDatabase) Then
    '    '    sConnect = sConnect & "Database=" & SQLDatabase & ";"
    '    'End If

    '    'If Len(SQLUserID) Then
    '    '    sConnect = sConnect & "User ID=" & SQLUserID & ";"
    '    'End If

    '    'If Len(SQLPassword) Then
    '    '    sConnect = sConnect & "Password=" & SQLPassword & ";"
    '    'End If

    '    'If PersistSecurityInfo = True Then
    '    '    sConnect = sConnect & "Persist Security Info=True;"
    '    'End If

    '    'Return sConnect

    'End Function

    Sub SetOrgInfo(ByVal sOrganization As String)

        Dim sbSQL As New StringBuilder

        With sbSQL
            .AppendLine("SELECT")
            .AppendLine("    tbOD.[Server]")
            .AppendLine("   ,tbOD.[LocalDatabaseName]")
            .AppendLine("   ,tbO.[Organization]")
            .AppendLine("FROM [dbo].[tbOrganizationDatabase] AS [tbOD] WITH(NOLOCK)")
            .AppendLine("   INNER JOIN [dbo].[tbOrganization] AS [tbO] WITH(NOLOCK)")
            .AppendLine("       ON tbOD.[OrganizationNumber] = tbO.[OrganizationNumber]")
            .AppendLine("WHERE")
            .AppendLine("   tbO.[Organization] = '" & sOrganization & "'")
        End With


        Dim OrgDBDir As DataTable = GetDBTable(sbSQL.ToString, "SQL-CLU01", "SBORoot")
        For Each row As DataRow In OrgDBDir.Rows
            sOrgSQLServer = row("Server")
            sOrgSQLDatabase = row("LocalDatabaseName")

            Dim tabOwner As DataTable = GetDBTable("SELECT TOP 1 [Owner] FROM [tmOwner]", sOrgSQLServer, sOrgSQLDatabase)
            For Each drOwner As DataRow In tabOwner.Rows
                sOrgName = drOwner("Owner")
            Next
            tabOwner.Dispose()
        Next
        OrgDBDir.Dispose()
    End Sub


    Friend Sub MyDataGrid_ItemCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs)

        Select Case e.Row.RowType
            Case DataControlRowType.DataRow
                Select Case e.Row.DataItem("RowType")
                    Case RowType.Header 'Header
                        e.Row.BackColor = System.Drawing.ColorTranslator.FromHtml("#D3D3D3")
                        For x As Integer = 0 To e.Row.Cells.Count - 1
                            Dim HeaderLink As New LinkButton
                            HeaderLink.ID = "lbHeader" + x.ToString
                            HeaderLink.CommandArgument = DirectCast(e.Row.DataItem, DataRowView).Row.Table.Columns(x).ToString
                            HeaderLink.CommandName = "NavigationLink_Click"
                            HeaderLink.Text = DirectCast(e.Row.DataItem, DataRowView).Item(x)
                            If oColList.ContainsKey(HeaderLink.CommandArgument) Then
                                HeaderLink.ToolTip = oColList(HeaderLink.CommandArgument).sToolTip
                            End If
                            e.Row.Cells(x).Controls.Add(HeaderLink)
                        Next
                    Case RowType.Store 'Store
                        For x As Integer = 0 To e.Row.Cells.Count - 1
                            Select Case x
                                Case 0
                                    If e.Row.DataItem("StoreComparable") = 1 Then
                                        e.Row.Cells(x).BackColor = System.Drawing.ColorTranslator.FromHtml("#506BBA")
                                        e.Row.Cells(x).ForeColor = System.Drawing.ColorTranslator.FromHtml("#FFFFFF")
                                    Else
                                        e.Row.Cells(x).BackColor = System.Drawing.ColorTranslator.FromHtml("#FFEFD5")
                                    End If
                            End Select
                        Next
                    Case RowType.District, RowType.DistrictByRegion, RowType.DistrictAverage, RowType.DistrictByRegionAverage 'District
                        e.Row.BackColor = System.Drawing.ColorTranslator.FromHtml("#29479F")
                        e.Row.ForeColor = System.Drawing.ColorTranslator.FromHtml("#FFFFFF")

                    Case RowType.Region, RowType.RegionAverage 'Region
                        e.Row.BackColor = System.Drawing.ColorTranslator.FromHtml("#502EC1")
                        e.Row.ForeColor = System.Drawing.ColorTranslator.FromHtml("#FFFFFF")
                    Case RowType.GrandAverage 'Grand Average
                        e.Row.BackColor = System.Drawing.ColorTranslator.FromHtml("#A29FB6")
                    Case RowType.GrandComparable 'Grand Comparable
                        e.Row.BackColor = System.Drawing.ColorTranslator.FromHtml("#506BBA")
                        e.Row.ForeColor = System.Drawing.ColorTranslator.FromHtml("#FFFFFF")
                    Case RowType.GrandNonComparable 'Grand Non-Comparable
                        e.Row.BackColor = System.Drawing.ColorTranslator.FromHtml("#D3D3D3")
                End Select

                For x As Integer = 0 To oVisibleColList.Count - 1
                    Select Case e.Row.DataItem("RowType")
                        Case RowType.Header 'Header
                            If cbFloatingHeader.Checked = True Then
                                e.Row.Cells(x).CssClass = "hcfloat"
                            Else
                                e.Row.Cells(x).CssClass = "dgheadercell"
                            End If
                        Case RowType.Store 'Store
                            e.Row.Cells(x).CssClass = "dgitemcellr"
                        Case RowType.District, RowType.DistrictAverage, RowType.DistrictByRegion, RowType.DistrictByRegionAverage, RowType.Region, RowType.RegionAverage, RowType.GrandAverage 'District, Region, Grand Average
                            If oVisibleColList(x).bGroupComparable = True Then
                                e.Row.Cells(x).BackColor = System.Drawing.ColorTranslator.FromHtml("#506BBA")
                                e.Row.Cells(x).ForeColor = System.Drawing.ColorTranslator.FromHtml("#FFFFFF")
                            End If
                            e.Row.Cells(x).CssClass = "dgitemcellr"
                        Case RowType.GrandComparable, RowType.GrandNonComparable 'Grand Comparable, Grand Non-Comparable
                            e.Row.Cells(x).CssClass = "dgitemcellr"
                    End Select
                    Select Case oVisibleColList(x).sColName
                        Case "StoreDescription"
                            If e.Row.DataItem("RowType") = RowType.Store Then 'Store
                                e.Row.Cells(x).CssClass = "c0IngNameFloat"
                            End If
                        Case "ProjectedCPR"
                            If e.Row.DataItem("RowType") = RowType.Store Then 'Store
                                Dim nCPRvalue As Integer

                                Integer.TryParse(e.Row.Cells(x).Text, nCPRvalue)
                                Select Case nCPRvalue
                                    Case 1 To 54
                                        e.Row.Cells(x).BackColor = System.Drawing.ColorTranslator.FromHtml("#CC3333")
                                    Case 55 To 74
                                        e.Row.Cells(x).BackColor = System.Drawing.ColorTranslator.FromHtml("#FFFF33")
                                    Case 75 To 100
                                        e.Row.Cells(x).BackColor = System.Drawing.ColorTranslator.FromHtml("#00CC33")
                                End Select
                            End If
                        Case "VarianceDollars"
                            If IsNumeric(e.Row.DataItem("StoreID")) AndAlso e.Row.DataItem("StoreID") > 0 Then
                                Dim linkVarianceDollars As New LinkButton
                                linkVarianceDollars.ID = "linkVarianceDollars" + e.Row.DataItem("StoreID").ToString
                                linkVarianceDollars.CommandArgument = DirectCast(e.Row.DataItem, DataRowView).Row.Table.Columns(x).ToString
                                linkVarianceDollars.CommandName = "NavigationLink_Click"
                                linkVarianceDollars.Text = DirectCast(e.Row.DataItem, DataRowView).Item(x)
                                e.Row.Cells(x).Controls.Add(linkVarianceDollars)
                            End If
                    End Select
                Next
        End Select
        For x As Integer = oVisibleColList.Count To e.Row.Cells.Count - 1
            e.Row.Cells(x).Visible = False
        Next
    End Sub

    Sub NavigationLink_Click(ByVal sender As Object, ByVal e As CommandEventArgs)
        Session("SortColumn") = e.CommandArgument.ToString
        SetOrder()

        If ddlLayoutTemplate.SelectedValue = String.Empty Then
            SetUserPreferences() 'Enabled/Ordering of Columns
        Else
            SetUserPreferences(ddlLayoutTemplate.SelectedValue) 'Enabled/Ordering of Columns
        End If

        BindGrid()
    End Sub

    Function LastPeriodDate() As Date

        Dim rsd, TheDate As Date
        Dim rpid As Integer

        InvWSS.GetReportingPeriodInfoFromDate(Now, rpid, iCurrentYear, iCurrentPeriodID, iCurrentWeekID, rsd, TheDate)

        Return TheDate.ToShortDateString

    End Function

    Private Sub GoExcelEPPlus(ByVal gvReport As GridView)
        Me.EnableViewState = False

        Dim ExcelMemoryStream As New MemoryStream()

        Dim oExcelPakcagePlus As New ExcelPackage(ExcelMemoryStream)
        Dim worksheetExcel As ExcelWorksheet = oExcelPakcagePlus.Workbook.Worksheets.Add("FinancialSummary")

        Dim TableBackColor As System.Drawing.Color = System.Drawing.Color.White
        Dim TableForeColor As System.Drawing.Color = System.Drawing.Color.Black
        Dim TableFont As System.Web.UI.WebControls.FontInfo
        Dim gvRow As GridViewRow = gvReport.HeaderRow
        Dim nRowOffset As Integer = 1

        If Not gvReport.BackColor.IsEmpty Then
            TableBackColor = gvReport.BackColor
        End If
        If Not gvReport.ForeColor.IsEmpty Then
            TableForeColor = gvReport.ForeColor
        End If
        TableFont = gvReport.Font

        AddHeaderRow(worksheetExcel, gvReport.Rows(0).Cells.Count - nHiddenColumnsRight, sTitle.ToString, 12, True, nRowOffset)
        AddHeaderRow(worksheetExcel, gvReport.Rows(0).Cells.Count - nHiddenColumnsRight, sSubTitle.Replace("<br>", "").ToString, 12, False, nRowOffset)

        If gvReport.HeaderRow.Visible = True Then
            gvRow = gvReport.HeaderRow
            AddExcelRow(worksheetExcel, TableBackColor, gvReport.HeaderStyle.BackColor, TableForeColor, gvReport.HeaderStyle.ForeColor, TableFont, gvReport.HeaderStyle.Font, gvRow, False, nRowOffset)
        End If

        Dim nRowNum As Integer
        For nRowNum = 0 To gvReport.Rows.Count - 1
            Dim RowBackColor As System.Drawing.Color
            Dim RowForeColor As System.Drawing.Color
            Dim bWrapText As Boolean = False
            gvRow = gvReport.Rows(nRowNum)

            RowBackColor = gvRow.BackColor
            RowForeColor = gvRow.ForeColor

            If Not gvRow.BackColor.IsEmpty Then
                RowBackColor = gvRow.BackColor
            Else
                If Not gvRow.RowState = DataControlRowState.Alternate Then
                    If Not gvReport.BackColor.IsEmpty Then
                        RowBackColor = gvReport.BackColor
                    End If
                Else
                    If Not gvReport.AlternatingRowStyle.BackColor.IsEmpty Then
                        RowBackColor = gvReport.AlternatingRowStyle.BackColor
                    End If
                End If
            End If

            If Not gvRow.ForeColor.IsEmpty Then
                RowForeColor = gvRow.ForeColor
            Else
                If Not gvRow.RowState = DataControlRowState.Alternate Then
                    If Not gvReport.ForeColor.IsEmpty Then
                        RowForeColor = gvReport.ForeColor
                    End If
                Else
                    If Not gvReport.AlternatingRowStyle.ForeColor.IsEmpty Then
                        RowForeColor = gvReport.AlternatingRowStyle.ForeColor
                    End If
                End If
            End If

            If nRowNum = 0 Then
                bWrapText = True
            End If
            AddExcelRow(worksheetExcel, TableBackColor, RowBackColor, TableForeColor, RowForeColor, TableFont, gvReport.RowStyle.Font, gvRow, bWrapText, nRowOffset + nRowNum)
        Next

        If gvReport.FooterRow.Visible = True Then
            gvRow = gvReport.FooterRow

            AddExcelRow(worksheetExcel, TableBackColor, gvReport.FooterStyle.BackColor, TableForeColor, gvReport.FooterStyle.ForeColor, TableFont, gvReport.FooterStyle.Font, gvRow, False, nRowOffset + gvReport.Rows.Count + 1)
        End If

        worksheetExcel.Cells(worksheetExcel.Dimension.Address).AutoFitColumns()

        oExcelPakcagePlus.Save()

        Response.Clear()
        Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        Response.AddHeader("content-disposition", "attachment;filename=FinancialSummary.xlsx")
        Response.BinaryWrite(ExcelMemoryStream.ToArray())
        Response.Flush() ' Send client what we have
        Response.SuppressContent = True ' Don't let ASP.Net send more info after we did
        Context.ApplicationInstance.CompleteRequest() ' Skip straight to end_request, do not pass go, do not collect $200
    End Sub

    Sub AddHeaderRow(worksheetExcel As ExcelWorksheet, ColumnCount As Integer, sHeaderText As String, nFontSize As Integer, bBold As Boolean, ByRef nRowOffset As Integer)
        Dim TargetCells As ExcelRange = worksheetExcel.Cells(nRowOffset, 1, nRowOffset, IIf(ColumnCount < 1, 1, ColumnCount))
        worksheetExcel.Cells(TargetCells.Start.Address).Value = sHeaderText
        TargetCells.Merge = True
        TargetCells.Style.HorizontalAlignment = Style.ExcelHorizontalAlignment.Center
        If nFontSize > 0 Then
            TargetCells.Style.Font.Size = nFontSize
        End If
        TargetCells.Style.Font.Bold = bBold


        nRowOffset += 1
    End Sub

    Sub AddExcelRow(worksheetExcel As ExcelWorksheet, TableBackColor As System.Drawing.Color, RowBackColor As System.Drawing.Color, TableForeColor As System.Drawing.Color, RowForeColor As System.Drawing.Color, TableFont As System.Web.UI.WebControls.FontInfo, RowFont As System.Web.UI.WebControls.FontInfo, gvRow As GridViewRow, bWrapText As Boolean, nRowOffset As Integer)
        Dim CellText As String
        Dim CellStripedText As String
        Dim CellValue As Object
        Dim CellBackColor As System.Drawing.Color
        Dim CellForeColor As System.Drawing.Color

        If RowBackColor.IsEmpty Then
            RowBackColor = TableBackColor
        End If
        If RowForeColor.IsEmpty Then
            RowForeColor = TableForeColor
        End If

        If RowFont.Name = String.Empty Then
            RowFont = TableFont
        End If

        For nCellNum As Integer = 0 To gvRow.Cells.Count - nHiddenColumnsRight - 1
            Dim gvCell As TableCell = gvRow.Cells(nCellNum)
            CellText = Nothing
            CellStripedText = Nothing
            CellValue = Nothing
            If gvCell.BackColor.IsEmpty Then
                CellBackColor = RowBackColor
            Else
                CellBackColor = gvCell.BackColor
            End If
            If gvCell.ForeColor.IsEmpty Then
                CellForeColor = RowForeColor
            Else
                CellForeColor = gvCell.ForeColor
            End If
            If gvCell.Controls.Count = 0 Then
                CellText = gvCell.Text
            Else
                For nCellControl As Integer = 0 To gvCell.Controls.Count - 1
                    Dim gvCellControl As Object = gvCell.Controls(nCellControl)
                    Select Case gvCellControl.GetType.ToString
                        Case "System.Web.UI.WebControls.Label"
                            CellText = DirectCast(gvCellControl, System.Web.UI.WebControls.Label).Text
                        Case "System.Web.UI.WebControls.LinkButton"
                            CellText = DirectCast(gvCellControl, System.Web.UI.WebControls.LinkButton).Text
                        Case Else
                            CellText = String.Empty
                    End Select

                    CellText = Server.HtmlDecode(CellText).Replace("<br>", vbCrLf)
                Next
            End If
            If CellText = "&nbsp;" Then
                CellText = String.Empty
            End If

            Dim TargetCell As ExcelRange = worksheetExcel.Cells(nRowOffset, nCellNum + 1)

            CellStripedText = Regex.Replace(CellText, "[$%]", "")

            Select Case True
                Case Integer.TryParse(CellStripedText, CellValue)
                Case Decimal.TryParse(CellStripedText, CellValue)
                Case Else
                    CellValue = CellText
            End Select

            If IsNumeric(CellStripedText) AndAlso CellText.Contains("%") Then
                CellValue = CellValue / 100.0
            End If

            TargetCell.Value = CellValue
            TargetCell.Style.WrapText = bWrapText
            TargetCell.Style.Fill.PatternType = Style.ExcelFillStyle.Solid
            TargetCell.Style.Fill.BackgroundColor.SetColor(CellBackColor)
            TargetCell.Style.Font.Color.SetColor(CellForeColor)
            TargetCell.Style.Font.Name = RowFont.Name
            If RowFont.Size.Unit.Value > 0 Then
                TargetCell.Style.Font.Size = RowFont.Size.Unit.Value
            End If
            If IsNumeric(CellStripedText) Then
                TargetCell.Style.Numberformat.Format = Regex.Replace(CellText.Replace("-", ""), "\d", "0")
            End If
            TargetCell.Style.Border.Top.Style = Style.ExcelBorderStyle.Thin
            TargetCell.Style.Border.Bottom.Style = Style.ExcelBorderStyle.Thin
            TargetCell.Style.Border.Left.Style = Style.ExcelBorderStyle.Thin
            TargetCell.Style.Border.Right.Style = Style.ExcelBorderStyle.Thin
        Next
    End Sub

    Private Sub ExportCSV(ByVal AccountID As String)
        sbCSVExport = New StringBuilder

        Dim sPath As String = Server.MapPath("ChartImages/FinSum-" & AccountID & ".csv")
        File.Delete(sPath)

        For Each drFinSum As GridViewRow In gv1.Rows
            'nHiddenColumnsRight()
            Dim sCSVData As New StringBuilder

            For nCounter As Integer = 0 To drFinSum.Cells.Count - nHiddenColumnsRight - 1
                Dim tcFinSum As TableCell = drFinSum.Cells(nCounter)
                Dim sCellText As String

                sCellText = tcFinSum.Text.Replace("&nbsp;", "")
                sCellText = Server.HtmlDecode(sCellText).Replace("<br>", String.Empty)

                sCSVData.Append(sCellText & ",")
            Next

            sbCSVExport.AppendLine(Left(sCSVData.ToString, sCSVData.Length - 1))
        Next

        Using fs As New System.IO.StreamWriter(sPath, False, System.Text.Encoding.UTF8)
            fs.Write(sbCSVExport.ToString())
        End Using
    End Sub
    Private Sub ExportPDF(ByVal AccountID As String)
        Try
            'Initialize PDF document
            Dim TableBackColor As System.Drawing.Color = System.Drawing.Color.White
            Dim TableForeColor As System.Drawing.Color = System.Drawing.Color.Black
            Dim RowBackColor() As System.Drawing.Color
            Dim RowForeColor() As System.Drawing.Color
            Dim CellBackColor As System.Drawing.Color
            Dim CellForeColor As System.Drawing.Color

            Dim cWidths() As Integer
            Dim sPath As String = Server.MapPath("ChartImages/FinSum-" & AccountID & ".pdf")
            File.Delete(sPath)
            pdfStream = New FileStream(sPath, FileMode.OpenOrCreate)
            pdfDoc = New Document(iTextSharp.text.PageSize.A4.Rotate(), 20, 20, 20, 35)
            pdfWri = PdfWriter.GetInstance(pdfDoc, pdfStream)
            Dim pdfEvents As New iTextEvents_FinancialSummary
            pdfWri.PageEvent = pdfEvents
            pdfDoc.Open()
            font8 = FontFactory.GetFont("ARIAL", 6)
            font8r = FontFactory.GetFont("ARIAL", 6, New iTextSharp.text.BaseColor(127, 0, 0))
            font8g = FontFactory.GetFont("ARIAL", 6, New iTextSharp.text.BaseColor(0, 127, 0))

            Dim CellCount As Integer
            Dim PageCount As Integer

            CellCount = gv1.Rows(0).Cells.Count - nHiddenColumnsRight - 1
            PageCount = Math.Ceiling(CellCount / (pdfTableCols - 1))

            ReDim pdfDGTable(PageCount - 1)

            For nCount As Integer = 0 To PageCount - 1
                Dim nPageCellCount As Integer
                If (nCount + 1) * (pdfTableCols - 1) < CellCount Then
                    nPageCellCount = pdfTableCols
                Else
                    nPageCellCount = CellCount - ((pdfTableCols - 1) * (nCount)) + 1
                End If
                pdfDGTable(nCount) = New PdfPTable(nPageCellCount)


                ReDim cWidths(pdfDGTable(nCount).NumberOfColumns - 1)
                For iCount As Integer = 0 To cWidths.GetUpperBound(0)
                    cWidths(iCount) = 100
                Next

                If nPageCellCount < 18 Then
                    pdfDGTable(nCount).WidthPercentage = nPageCellCount * 5.5
                Else
                    pdfDGTable(nCount).WidthPercentage = 100
                End If

                cWidths(0) = 300

                pdfDGTable(nCount).SetWidths(cWidths)
                pdfDGTable(nCount).HorizontalAlignment = Element.ALIGN_CENTER
            Next

            'If the Table has colors use them otherwise stick with defaults
            If Not gv1.BackColor.IsEmpty Then
                TableBackColor = gv1.BackColor
            End If
            If Not gv1.ForeColor.IsEmpty Then
                TableForeColor = gv1.ForeColor
            End If

            ReDim RowBackColor(gv1.Rows.Count - 1)
            ReDim RowForeColor(gv1.Rows.Count - 1)

            For nCounter As Integer = 0 To gv1.Rows.Count - 1
                If Not gv1.Rows(nCounter).BackColor.IsEmpty Then
                    RowBackColor(nCounter) = gv1.Rows(nCounter).BackColor
                Else
                    If Not gv1.Rows(nCounter).RowState = DataControlRowState.Alternate Then
                        If Not gv1.BackColor.IsEmpty Then
                            RowBackColor(nCounter) = gv1.BackColor
                        Else
                            RowBackColor(nCounter) = TableBackColor
                        End If
                    Else
                        If Not gv1.AlternatingRowStyle.BackColor.IsEmpty Then
                            RowBackColor(nCounter) = gv1.AlternatingRowStyle.BackColor
                        Else
                            RowBackColor(nCounter) = TableBackColor
                        End If
                    End If
                End If

                If Not gv1.Rows(nCounter).ForeColor.IsEmpty Then
                    RowForeColor(nCounter) = gv1.Rows(nCounter).ForeColor
                Else
                    If Not gv1.Rows(nCounter).RowState = DataControlRowState.Alternate Then
                        If Not gv1.ForeColor.IsEmpty Then
                            RowForeColor(nCounter) = gv1.ForeColor
                        Else
                            RowForeColor(nCounter) = TableForeColor
                        End If
                    Else
                        If Not gv1.AlternatingRowStyle.ForeColor.IsEmpty Then
                            RowForeColor(nCounter) = gv1.AlternatingRowStyle.ForeColor
                        Else
                            RowForeColor(nCounter) = TableForeColor
                        End If
                    End If
                End If

            Next

            For nCount As Integer = 0 To pdfDGTable.Length - 1
                For Each drFinSum As GridViewRow In gv1.Rows
                    'PDF row

                    Dim pdfDGCell As PdfPCell
                    Dim sCellText As String

                    If drFinSum.Cells(0).BackColor.IsEmpty Then
                        CellBackColor = RowBackColor(drFinSum.RowIndex)
                    Else
                        CellBackColor = drFinSum.Cells(0).BackColor
                    End If
                    If drFinSum.Cells(0).ForeColor.IsEmpty Then
                        CellForeColor = RowForeColor(drFinSum.RowIndex)
                    Else
                        CellForeColor = drFinSum.Cells(0).ForeColor
                    End If

                    sCellText = drFinSum.Cells(0).Text
                    sCellText = Server.HtmlDecode(sCellText).Replace("<br>", vbCrLf)

                    pdfDGCell = New PdfPCell(New Phrase(New Chunk(sCellText, FontFactory.GetFont("ARIAL", 6, New iTextSharp.text.BaseColor(CellForeColor)))))

                    pdfDGCell.HorizontalAlignment = PdfPCell.ALIGN_LEFT
                    pdfDGCell.NoWrap = False
                    pdfDGCell.BackgroundColor = New iTextSharp.text.BaseColor(CellBackColor)
                    pdfDGTable(nCount).AddCell(pdfDGCell)

                    Dim iColCount As Integer = 0

                    For iPDFCol As Integer = ((pdfTableCols - 1) * (nCount)) + 1 To ((pdfTableCols - 1) * (nCount + 1))
                        If iPDFCol > CellCount Then
                            Exit For
                        End If

                        If drFinSum.Cells(iPDFCol).BackColor.IsEmpty Then
                            CellBackColor = RowBackColor(drFinSum.RowIndex)
                        Else
                            CellBackColor = drFinSum.Cells(iPDFCol).BackColor
                        End If
                        If drFinSum.Cells(iPDFCol).ForeColor.IsEmpty Then
                            CellForeColor = RowForeColor(drFinSum.RowIndex)
                        Else
                            CellForeColor = drFinSum.Cells(iPDFCol).ForeColor
                        End If

                        sCellText = drFinSum.Cells(iPDFCol).Text
                        sCellText = Server.HtmlDecode(sCellText).Replace("<br>", vbCrLf)

                        pdfDGCell = New PdfPCell(New Phrase(New Chunk(sCellText, FontFactory.GetFont("ARIAL", 6, New iTextSharp.text.BaseColor(CellForeColor)))))
                        pdfDGCell.HorizontalAlignment = PdfPCell.ALIGN_RIGHT
                        pdfDGCell.NoWrap = False
                        pdfDGCell.BackgroundColor = New iTextSharp.text.BaseColor(CellBackColor)
                        pdfDGTable(nCount).AddCell(pdfDGCell)
                    Next
                Next
            Next

            'Old ClosePDF
            Dim sTitle As String
            If sEmailTemplate = "Email Template" Then
                sTitle = "Financial Summary Report"
            Else
                sTitle = String.Format("Financial Summary Report (Template: {0})", sEmailTemplate)
            End If

            Dim titlefont As Font = FontFactory.GetFont("ARIAL", 10)
            Dim subtitlefont As Font = FontFactory.GetFont("ARIAL", 8)
            Dim font3 As Font = FontFactory.GetFont("ARIAL", 3)
            Dim title As Paragraph = New Paragraph(sTitle, titlefont)
            Dim subtitle As Paragraph = New Paragraph(sSubTitle.ToString.Replace("<br>", String.Empty), subtitlefont)
            Dim spacerow As Paragraph = New Paragraph("   ", font3)

            subtitle.Alignment = Element.ALIGN_CENTER

            For nCounter As Integer = 0 To pdfDGTable.Length - 1
                If Not pdfDGTable.Length = 1 Then
                    sTitle = String.Format("Financial Summary Report (Section {0})", (nCounter + 1).ToString)
                End If
                title = New Paragraph(sTitle, titlefont)
                title.Alignment = Element.ALIGN_CENTER
                pdfDoc.Add(title)
                pdfDoc.Add(spacerow)
                pdfDoc.Add(subtitle)
                pdfDoc.Add(spacerow)
                pdfDoc.Add(pdfDGTable(nCounter))
                If Not nCounter = pdfDGTable.Length - 1 Then
                    pdfDoc.NewPage()
                End If
            Next


        Catch ex As Exception
            pdfStream.Close()
        Finally
            pdfDoc.Close()
        End Try

    End Sub

    Function GetDBTable(ByVal sSQL As String, Optional ByVal sSQLServer As String = "", Optional ByVal sSQLDatabase As String = "") As DataTable
        Dim SQLCon As New SqlConnection
        Dim SQLCmd As New SqlDataAdapter
        Dim tTable As New DataTable

        Dim DBConString As String = BuildDBConnectString(sSQLServer, sSQLDatabase, "sboapplication", "SoftwareA43A")
        Dim bResult As Boolean = False
        Try
            SQLCon = New SqlConnection(DBConString)
            SQLCon.Open()

            SQLCmd = New SqlDataAdapter(sSQL, SQLCon)
            SQLCmd.SelectCommand.CommandTimeout = 360
            SQLCmd.Fill(tTable)
            SQLCon.Close()
            bResult = True
        Catch eGetDataTable As Exception
            'sSQLErr &= sSQLServer & ":" & sSQLDatabase & " - " & eGetDataTable.Message & "<p>"
        Finally
            If Not SQLCmd Is Nothing Then
                SQLCmd.Dispose()
            End If
            If Not SQLCon Is Nothing Then
                SQLCon.Dispose()
            End If
        End Try
        Return tTable
    End Function

    Public Function BuildDBConnectString(Optional ByVal SQLServer As String = "", Optional ByVal SQLDatabase As String = "", Optional ByVal SQLUserID As String = "", Optional ByVal SQLPassword As String = "", Optional ByVal PersistSecurityInfo As Boolean = False) As String
        Dim sConnect As String = ""
        If Len(SQLServer) Then
            sConnect = sConnect & "Data Source=" & SQLServer & ";"
        End If
        If Len(SQLDatabase) Then
            sConnect = sConnect & "Database=" & SQLDatabase & ";"
        End If
        If Len(SQLUserID) Then
            sConnect = sConnect & "User ID=" & SQLUserID & ";"
        End If
        If Len(SQLPassword) Then
            sConnect = sConnect & "Password=" & SQLPassword & ";"
        End If
        If PersistSecurityInfo = True Then
            sConnect = sConnect & "Persist Security Info=True;"
        End If
        Return sConnect
    End Function

End Class

Public Class iTextEvents_FinancialSummary

    Inherits iTextSharp.text.pdf.PdfPageEventHelper
    Private cb As PdfContentByte
    Private bf As BaseFont
    Private template As PdfTemplate

    Public Overrides Sub OnOpenDocument(writer As PdfWriter, document As Document)
        MyBase.OnOpenDocument(writer, document)

        cb = writer.DirectContent
        bf = BaseFont.CreateFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED)
        template = cb.CreateTemplate(50, 50)

    End Sub

    Public Overrides Sub OnEndPage(writer As PdfWriter, document As Document)
        MyBase.OnEndPage(writer, document)

        Dim sText As String
        Dim fLen As Single

        'Column 1 - Date
        sText = Now.ToShortDateString
        fLen = bf.GetWidthPoint(sText, 8)

        With cb
            .BeginText()
            .SetFontAndSize(bf, 8)
            .SetTextMatrix(30, 20)
            .ShowText(sText)
            .EndText()
        End With

        'Column 2 - Copyright
        sText = "© 2004-" & Now.Year.ToString & " DUMAC Business Systems, Inc."
        fLen = bf.GetWidthPoint(sText, 8)

        With cb
            .BeginText()
            .SetFontAndSize(bf, 8)
            .SetTextMatrix(document.PageSize.Width / 2 - fLen / 2, 20)
            .ShowText(sText)
            .EndText()
        End With

        'Column 3 - Page Number
        sText = "Page " & writer.PageNumber & " of "
        fLen = bf.GetWidthPoint(sText, 8)

        With cb
            .BeginText()
            .SetFontAndSize(bf, 8)
            .SetTextMatrix(document.PageSize.Width - 90, 20)
            .ShowText(sText)
            .EndText()

            .AddTemplate(template, document.PageSize.Width - 90 + fLen, 20)
            .BeginText()
            .SetFontAndSize(bf, 8)
            .SetTextMatrix(280, 820)
            .EndText()
        End With

    End Sub

    Public Overrides Sub OnCloseDocument(ByVal writer As iTextSharp.text.pdf.PdfWriter, ByVal document As iTextSharp.text.Document)
        MyBase.OnCloseDocument(writer, document)

        With template
            .BeginText()
            .SetFontAndSize(bf, 8)
            .ShowText((writer.PageNumber - 1).ToString)
            .EndText()
        End With

    End Sub
End Class
